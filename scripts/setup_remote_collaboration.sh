#!/bin/bash

# NEXUS Remote Collaboration Setup Script
# Automates Git repository initialization and preparation for remote collaboration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if we're in the right directory
if [ ! -f "ROADMAP.md" ] || [ ! -d "src" ]; then
    print_error "Please run this script from the NEXUS project root directory"
    exit 1
fi

print_header "NEXUS REMOTE COLLABORATION SETUP"

# Step 1: Check Git installation
print_status "Checking Git installation..."
if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install Git first."
    exit 1
fi
print_success "Git is installed: $(git --version)"

# Step 2: Check if already a Git repository
if [ -d ".git" ]; then
    print_warning "Git repository already exists"
    read -p "Do you want to continue? This will not affect existing Git history. (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    print_status "Initializing Git repository..."
    git init
    print_success "Git repository initialized"
fi

# Step 3: Create comprehensive .gitignore
print_status "Creating .gitignore file..."
cat > .gitignore << 'EOF'
# Environment and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
*.pfx
wallet.json
keypair.json

# API Keys and sensitive data
*api_key*
*secret*
*private*
*password*

# Dependencies
node_modules/
venv/
venv_clean/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log

# Build outputs
dist/
build/
*.egg-info/
.eggs/
*.egg
*.whl

# IDE files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~
.project
.metadata
.classpath
.settings/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
.tmp/
.cache/
.parcel-cache/

# Trading specific
trades/
signals/processed/
backups/
*.backup

# Test outputs
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Documentation builds
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Frontend specific
# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
EOF

print_success ".gitignore created with comprehensive exclusions"

# Step 4: Backup and secure .env file
print_status "Securing environment files..."
if [ -f ".env" ]; then
    if [ ! -f ".env.backup" ]; then
        cp .env .env.backup
        print_success "Created backup of .env file as .env.backup"
    fi
    
    # Replace .env with .env.example for repository
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_success "Replaced .env with .env.example for repository safety"
    fi
fi

# Step 5: Check for other sensitive files
print_status "Checking for sensitive files..."
sensitive_files=(
    "*.key"
    "*.pem" 
    "wallet.json"
    "keypair.json"
    "*private*"
    "*secret*"
)

found_sensitive=false
for pattern in "${sensitive_files[@]}"; do
    if ls $pattern 1> /dev/null 2>&1; then
        print_warning "Found sensitive files matching: $pattern"
        found_sensitive=true
    fi
done

if [ "$found_sensitive" = true ]; then
    print_warning "Please review and secure any sensitive files before pushing to remote repository"
fi

# Step 6: Add files to Git
print_status "Adding files to Git repository..."
git add .

# Step 7: Check Git user configuration
print_status "Checking Git configuration..."
if ! git config user.name > /dev/null; then
    read -p "Enter your Git username: " git_username
    git config user.name "$git_username"
fi

if ! git config user.email > /dev/null; then
    read -p "Enter your Git email: " git_email
    git config user.email "$git_email"
fi

print_success "Git user configured as: $(git config user.name) <$(git config user.email)>"

# Step 8: Create initial commit
print_status "Creating initial commit..."
if git diff --staged --quiet; then
    print_warning "No changes to commit"
else
    git commit -m "Initial commit: NEXUS Solana Memecoin Ecosystem Platform

- Complete trading system with 15+ components
- Multi-DEX integration (Jupiter, Orca, Raydium, Pump.fun)
- Real-time token detection and whale tracking
- MEV protection and risk management
- Enterprise-grade architecture
- Task management system for team collaboration"
    print_success "Initial commit created"
fi

# Step 9: Display next steps
print_header "SETUP COMPLETE - NEXT STEPS"

echo ""
echo "🎉 Your Git repository is ready for remote collaboration!"
echo ""
echo "📋 NEXT STEPS:"
echo ""
echo "1. CREATE REMOTE REPOSITORY:"
echo "   • Go to GitHub.com (recommended) or GitLab.com"
echo "   • Create a new PRIVATE repository"
echo "   • Name: nexus-solana-trading"
echo "   • Description: NEXUS - Enterprise Solana Memecoin Ecosystem Platform"
echo "   • Make sure it's PRIVATE (important for trading system)"
echo ""
echo "2. CONNECT TO REMOTE:"
echo "   git remote add origin https://github.com/YOUR_USERNAME/nexus-solana-trading.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "3. INVITE COLLABORATOR:"
echo "   • Go to repository Settings → Collaborators"
echo "   • Add the new developer's GitHub username"
echo ""
echo "4. SHARE WITH NEW DEVELOPER:"
echo "   • Repository URL"
echo "   • NEW_DEVELOPER_QUICKSTART.md file"
echo "   • They need to create their own .env file with API keys"
echo ""
echo "🔒 SECURITY REMINDERS:"
echo "   • Your real .env is backed up as .env.backup"
echo "   • Never commit real API keys or private keys"
echo "   • Repository should be PRIVATE"
echo "   • New developer needs their own API keys"
echo ""
echo "📚 DOCUMENTATION:"
echo "   • REMOTE_COLLABORATION_SETUP.md - Detailed setup guide"
echo "   • NEW_DEVELOPER_QUICKSTART.md - For new developer"
echo "   • TASK_MANAGEMENT.md - Task coordination system"
echo ""

print_success "Remote collaboration setup complete! 🚀"
