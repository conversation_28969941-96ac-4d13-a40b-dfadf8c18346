# Environment and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
*.pfx
wallet.json
keypair.json

# API Keys and sensitive data
*api_key*
*secret*
*private*
*password*

# Dependencies
node_modules/
venv/
venv_clean/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log

# Build outputs
dist/
build/
*.egg-info/
.eggs/
*.egg
*.whl

# IDE files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~
.project
.metadata
.classpath
.settings/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
.tmp/
.cache/
.parcel-cache/

# Trading specific
trades/
signals/processed/
backups/
*.backup

# Test outputs
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Documentation builds
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Frontend specific
# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
