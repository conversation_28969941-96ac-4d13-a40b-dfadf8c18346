{"version": 3, "file": "Authorization.js", "sourceRoot": "", "sources": ["../../core/Authorization.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAsE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,MAAM,UAAU,IAAI,CAIlB,aAA4C,EAC5C,UAAmC,EAAE;IAErC,IAAI,OAAO,aAAa,CAAC,OAAO,KAAK,QAAQ;QAC3C,OAAO,OAAO,CAAC,aAAa,CAAU,CAAA;IACxC,OAAO,EAAE,GAAG,aAAa,EAAE,GAAG,OAAO,CAAC,SAAS,EAAW,CAAA;AAC5D,CAAC;AA2BD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,OAAO,CAAC,aAAkB;IACxC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,aAAa,CAAA;IACjD,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,CAAE,CAAA;IAEnD,OAAO;QACL,OAAO;QACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;QACxB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;QACpB,GAAG,SAAS;KACb,CAAA;AACH,CAAC;AAMD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,WAAW,CAAC,iBAA0B;IACpD,OAAO,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;AACvC,CAAC;AAMD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,MAAM,UAAU,SAAS,CACvB,KAAY;IAEZ,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAA;IACtD,MAAM,SAAS,GACb,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACtE,OAAO,IAAI,CAAC;QACV,OAAO;QACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;QACxB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;QACpB,GAAG,SAAS;KACb,CAAU,CAAA;AACb,CAAC;AAUD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDG;AACH,MAAM,UAAU,aAAa,CAC3B,SAAoB;IAEpB,MAAM,IAAI,GAAkB,EAAE,CAAA;IAC9B,KAAK,MAAM,KAAK,IAAI,SAAS;QAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;IAC1D,OAAO,IAAa,CAAA;AACtB,CAAC;AAUD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,UAAU,cAAc,CAAC,aAA4B;IACzD,OAAO,IAAI,CAAC,aAAa,CAAC,CAAA;AAC5B,CAAC;AAMD;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,IAAI,CAAC,aAA4B;IAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;AAChF,CAAC;AAWD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,KAAK,CAAC,aAAqB;IACzC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS,EAAE,GAAG,aAAa,CAAA;IAE/D,OAAO;QACL,OAAO;QACP,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC;QAChC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;QAC5B,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;KAC9B,CAAA;AACH,CAAC;AAMD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,SAAS,CAAC,iBAA6B;IACrD,OAAO,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACrC,CAAC;AAMD;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,OAAO,CACrB,aAA4B;IAE5B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,aAAa,CAAA;IACjD,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;IAClD,OAAO;QACL,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;QACxC,OAAO;QACP,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KAC1C,CAAA;AACZ,CAAC;AASD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,MAAM,UAAU,WAAW,CAIzB,IAAuB;IACvB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAA;IAEzC,MAAM,SAAS,GAAuB,EAAE,CAAA;IACxC,KAAK,MAAM,aAAa,IAAI,IAAI;QAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAA;IAExE,OAAO,SAAkB,CAAA;AAC3B,CAAC"}