import * as Authorization from './Authorization.js';
import * as Hex from './Hex.js';
import * as Signature from './Signature.js';
/** Type to RPC Type mapping. */
export const toRpcType = {
    legacy: '0x0',
    eip2930: '0x1',
    eip1559: '0x2',
    eip4844: '0x3',
    eip7702: '0x4',
};
/** RPC Type to Type mapping. */
export const fromRpcType = {
    '0x0': 'legacy',
    '0x1': 'eip2930',
    '0x2': 'eip1559',
    '0x3': 'eip4844',
    '0x4': 'eip7702',
};
/**
 * Converts an {@link ox#Transaction.Rpc} to an {@link ox#Transaction.Transaction}.
 *
 * @example
 * ```ts twoslash
 * import { Transaction } from 'ox'
 *
 * const transaction = Transaction.fromRpc({
 *   hash: '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *   nonce: '0x357',
 *   blockHash:
 *     '0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b',
 *   blockNumber: '0x12f296f',
 *   transactionIndex: '0x2',
 *   from: '0x814e5e0e31016b9a7f138c76b7e7b2bb5c1ab6a6',
 *   to: '0x3fc91a3afd70395cd496c647d5a6cc9d4b2b7fad',
 *   value: '0x9b6e64a8ec60000',
 *   gas: '0x43f5d',
 *   maxFeePerGas: '0x2ca6ae494',
 *   maxPriorityFeePerGas: '0x41cc3c0',
 *   input:
 *     '0x3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006643504700000000000000000000000000000000000000000000000000000000000000040b080604000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002800000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec600000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec60000000000000000000000000000000000000000000000000000019124bb5ae978c000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b80000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b8000000000000000000000000000000fee13a103a10d593b9ae06b3e05f2e7e1c00000000000000000000000000000000000000000000000000000000000000190000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b800000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000190240001b9872b',
 *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',
 *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',
 *   yParity: '0x0',
 *   chainId: '0x1',
 *   accessList: [],
 *   type: '0x2',
 * })
 * ```
 *
 * @param transaction - The RPC transaction to convert.
 * @returns An instantiated {@link ox#Transaction.Transaction}.
 */
export function fromRpc(transaction, _options = {}) {
    if (!transaction)
        return null;
    const signature = Signature.extract(transaction);
    const transaction_ = {
        ...transaction,
        ...signature,
    };
    transaction_.blockNumber = transaction.blockNumber
        ? BigInt(transaction.blockNumber)
        : null;
    transaction_.data = transaction.input;
    transaction_.gas = BigInt(transaction.gas ?? 0n);
    transaction_.nonce = BigInt(transaction.nonce ?? 0n);
    transaction_.transactionIndex = transaction.transactionIndex
        ? Number(transaction.transactionIndex)
        : null;
    transaction_.value = BigInt(transaction.value ?? 0n);
    if (transaction.authorizationList)
        transaction_.authorizationList = Authorization.fromRpcList(transaction.authorizationList);
    if (transaction.chainId)
        transaction_.chainId = Number(transaction.chainId);
    if (transaction.gasPrice)
        transaction_.gasPrice = BigInt(transaction.gasPrice);
    if (transaction.maxFeePerBlobGas)
        transaction_.maxFeePerBlobGas = BigInt(transaction.maxFeePerBlobGas);
    if (transaction.maxFeePerGas)
        transaction_.maxFeePerGas = BigInt(transaction.maxFeePerGas);
    if (transaction.maxPriorityFeePerGas)
        transaction_.maxPriorityFeePerGas = BigInt(transaction.maxPriorityFeePerGas);
    if (transaction.type)
        transaction_.type =
            fromRpcType[transaction.type] ?? transaction.type;
    if (signature)
        transaction_.v = Signature.yParityToV(signature.yParity);
    return transaction_;
}
/**
 * Converts an {@link ox#Transaction.Transaction} to an {@link ox#Transaction.Rpc}.
 *
 * @example
 * ```ts twoslash
 * import { Transaction } from 'ox'
 *
 * const transaction = Transaction.toRpc({
 *   accessList: [],
 *   blockHash:
 *     '0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b',
 *   blockNumber: 19868015n,
 *   chainId: 1,
 *   from: '0x814e5e0e31016b9a7f138c76b7e7b2bb5c1ab6a6',
 *   gas: 278365n,
 *   hash: '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *   input:
 *     '0x3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006643504700000000000000000000000000000000000000000000000000000000000000040b080604000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002800000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec600000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec60000000000000000000000000000000000000000000000000000019124bb5ae978c000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b80000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b8000000000000000000000000000000fee13a103a10d593b9ae06b3e05f2e7e1c00000000000000000000000000000000000000000000000000000000000000190000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b800000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000190240001b9872b',
 *   maxFeePerGas: 11985937556n,
 *   maxPriorityFeePerGas: 68993984n,
 *   nonce: 855n,
 *   r: 44944627813007772897391531230081695102703289123332187696115181104739239197517n,
 *   s: 36528503505192438307355164441104001310566505351980369085208178712678799181120n,
 *   to: '0x3fc91a3afd70395cd496c647d5a6cc9d4b2b7fad',
 *   transactionIndex: 2,
 *   type: 'eip1559',
 *   v: 27,
 *   value: 700000000000000000n,
 *   yParity: 0,
 * })
 * ```
 *
 * @param transaction - The transaction to convert.
 * @returns An RPC-formatted transaction.
 */
export function toRpc(transaction, _options) {
    const rpc = {};
    rpc.blockHash = transaction.blockHash;
    rpc.blockNumber =
        typeof transaction.blockNumber === 'bigint'
            ? Hex.fromNumber(transaction.blockNumber)
            : null;
    rpc.from = transaction.from;
    rpc.gas = Hex.fromNumber(transaction.gas ?? 0n);
    rpc.hash = transaction.hash;
    rpc.input = transaction.input;
    rpc.nonce = Hex.fromNumber(transaction.nonce ?? 0n);
    rpc.to = transaction.to;
    rpc.transactionIndex = transaction.transactionIndex
        ? Hex.fromNumber(transaction.transactionIndex)
        : null;
    rpc.type = toRpcType[transaction.type] ?? transaction.type;
    rpc.value = Hex.fromNumber(transaction.value ?? 0n);
    if (transaction.accessList)
        rpc.accessList = transaction.accessList;
    if (transaction.authorizationList)
        rpc.authorizationList = Authorization.toRpcList(transaction.authorizationList);
    if (transaction.blobVersionedHashes)
        rpc.blobVersionedHashes = transaction.blobVersionedHashes;
    if (transaction.chainId)
        rpc.chainId = Hex.fromNumber(transaction.chainId);
    if (typeof transaction.gasPrice === 'bigint')
        rpc.gasPrice = Hex.fromNumber(transaction.gasPrice);
    if (typeof transaction.maxFeePerBlobGas === 'bigint')
        rpc.maxFeePerBlobGas = Hex.fromNumber(transaction.maxFeePerBlobGas);
    if (typeof transaction.maxFeePerGas === 'bigint')
        rpc.maxFeePerGas = Hex.fromNumber(transaction.maxFeePerGas);
    if (typeof transaction.maxPriorityFeePerGas === 'bigint')
        rpc.maxPriorityFeePerGas = Hex.fromNumber(transaction.maxPriorityFeePerGas);
    if (typeof transaction.r === 'bigint')
        rpc.r = Hex.fromNumber(transaction.r, { size: 32 });
    if (typeof transaction.s === 'bigint')
        rpc.s = Hex.fromNumber(transaction.s, { size: 32 });
    if (typeof transaction.v === 'number')
        rpc.v = Hex.fromNumber(transaction.v, { size: 1 });
    if (typeof transaction.yParity === 'number')
        rpc.yParity = transaction.yParity === 0 ? '0x0' : '0x1';
    return rpc;
}
//# sourceMappingURL=Transaction.js.map