{"version": 3, "file": "cursor.js", "sourceRoot": "", "sources": ["../../../core/internal/cursor.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,MAAM,MAAM,cAAc,CAAA;AAsCtC,MAAM,YAAY,GAAW,aAAa,CAAC;IACzC,KAAK,EAAE,IAAI,UAAU,EAAE;IACvB,QAAQ,EAAE,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;IAC1C,QAAQ,EAAE,CAAC;IACX,iBAAiB,EAAE,IAAI,GAAG,EAAE;IAC5B,kBAAkB,EAAE,CAAC;IACrB,kBAAkB,EAAE,MAAM,CAAC,iBAAiB;IAC5C,eAAe;QACb,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB;YACpD,MAAM,IAAI,+BAA+B,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,kBAAkB,GAAG,CAAC;gBAClC,KAAK,EAAE,IAAI,CAAC,kBAAkB;aAC/B,CAAC,CAAA;IACN,CAAC;IACD,cAAc,CAAC,QAAQ;QACrB,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YAClD,MAAM,IAAI,wBAAwB,CAAC;gBACjC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;gBACzB,QAAQ;aACT,CAAC,CAAA;IACN,CAAC;IACD,iBAAiB,CAAC,MAAM;QACtB,IAAI,MAAM,GAAG,CAAC;YAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAA;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAA;QACvC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC1B,CAAC;IACD,YAAY,CAAC,QAAQ;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;IACnE,CAAC;IACD,iBAAiB,CAAC,MAAM;QACtB,IAAI,MAAM,GAAG,CAAC;YAAE,MAAM,IAAI,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAA;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAA;QACvC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC1B,CAAC;IACD,WAAW,CAAC,SAAS;QACnB,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAA;IAC9B,CAAC;IACD,YAAY,CAAC,MAAM,EAAE,SAAS;QAC5B,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,MAAM,GAAG,CAAC,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAA;IACzD,CAAC;IACD,YAAY,CAAC,SAAS;QACpB,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAA;IAC9B,CAAC;IACD,aAAa,CAAC,SAAS;QACrB,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IAC1C,CAAC;IACD,aAAa,CAAC,SAAS;QACrB,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACjC,OAAO,CACL,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CACrC,CAAA;IACH,CAAC;IACD,aAAa,CAAC,SAAS;QACrB,MAAM,QAAQ,GAAG,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IAC1C,CAAC;IACD,QAAQ,CAAC,IAAmB;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QAChC,IAAI,CAAC,QAAQ,EAAE,CAAA;IACjB,CAAC;IACD,SAAS,CAAC,KAAY;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACrD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QACpC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAA;IAC/B,CAAC;IACD,SAAS,CAAC,KAAa;QACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,QAAQ,EAAE,CAAA;IACjB,CAAC;IACD,UAAU,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACtC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;IACpB,CAAC;IACD,UAAU,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACtC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC,CAAA;QAClD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,UAAU,CAAC,CAAA;QAC9D,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;IACpB,CAAC;IACD,UAAU,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA;QACtC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;IACpB,CAAC;IACD,QAAQ;QACN,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAChC,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,OAAO,KAAK,CAAA;IACd,CAAC;IACD,SAAS,CAAC,MAAM,EAAE,IAAI;QACpB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QACvC,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAA;QAC/B,OAAO,KAAK,CAAA;IACd,CAAC;IACD,SAAS;QACP,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QACjC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,UAAU;QACR,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,UAAU;QACR,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,UAAU;QACR,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC1C,CAAC;IACD,WAAW,CAAC,QAAQ;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAA;QACjC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,OAAO,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAA;IAC5C,CAAC;IACD,MAAM;QACJ,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM,CAAC,iBAAiB;YAAE,OAAM;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;QACpD,IAAI,KAAK,GAAG,CAAC;YAAE,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC1C,CAAC;CACF,CAAA;AAED,gBAAgB;AAChB,MAAM,UAAU,MAAM,CACpB,KAAY,EACZ,EAAE,kBAAkB,GAAG,KAAK,KAAoB,EAAE;IAElD,MAAM,MAAM,GAAW,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;IAClD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAC5B,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,UAAU,CACjB,CAAA;IACD,MAAM,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAA;IACpC,MAAM,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;IAC9C,OAAO,MAAM,CAAA;AACf,CAAC;AASD,gBAAgB;AAChB,MAAM,OAAO,mBAAoB,SAAQ,MAAM,CAAC,SAAS;IAGvD,YAAY,EAAE,MAAM,EAAsB;QACxC,KAAK,CAAC,YAAY,MAAM,wBAAwB,CAAC,CAAA;QAHjC;;;;mBAAO,4BAA4B;WAAA;IAIrD,CAAC;CACF;AAED,gBAAgB;AAChB,MAAM,OAAO,wBAAyB,SAAQ,MAAM,CAAC,SAAS;IAG5D,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAwC;QACpE,KAAK,CACH,cAAc,QAAQ,yCAAyC,MAAM,MAAM,CAC5E,CAAA;QALe;;;;mBAAO,iCAAiC;WAAA;IAM1D,CAAC;CACF;AAED,gBAAgB;AAChB,MAAM,OAAO,+BAAgC,SAAQ,MAAM,CAAC,SAAS;IAGnE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAoC;QAC5D,KAAK,CACH,6BAA6B,KAAK,wCAAwC,KAAK,MAAM,CACtF,CAAA;QALe;;;;mBAAO,wCAAwC;WAAA;IAMjE,CAAC;CACF"}