{"version": 3, "file": "TransactionEnvelopeEip4844.js", "sourceRoot": "", "sources": ["../../core/TransactionEnvelopeEip4844.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAC7C,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAEnC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAA;AAC/D,OAAO,KAAK,0BAA0B,MAAM,iCAAiC,CAAA;AAuC7E,MAAM,CAAC,MAAM,cAAc,GAAG,MAAe,CAAA;AAK7C,MAAM,CAAC,MAAM,IAAI,GAAG,SAAkB,CAAA;AAGtC;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,MAAM,CACpB,QAAuD;IAEvD,MAAM,EAAE,mBAAmB,EAAE,GAAG,QAAQ,CAAA;IACxC,IAAI,mBAAmB,EAAE,CAAC;QACxB,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,6BAA6B,EAAE,CAAA;QACjD,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;YACnD,IAAI,IAAI,KAAK,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;YAC/D,IAAI,OAAO,KAAK,GAAG,CAAC,oBAAoB;gBACtC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;oBAC/C,IAAI;oBACJ,OAAO;iBACR,CAAC,CAAA;QACN,CAAC;IACH,CAAC;IACD,0BAA0B,CAAC,MAAM,CAC/B,QAAuE,CACxE,CAAA;AACH,CAAC;AAcD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,WAAW,CACzB,UAAsB;IAEtB,MAAM,yBAAyB,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAA;IAErE,MAAM,iBAAiB,GAAG,yBAAyB,CAAC,MAAM,KAAK,CAAC,CAAA;IAEhE,MAAM,gBAAgB,GAAG,iBAAiB;QACxC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAE;QAC/B,CAAC,CAAC,yBAAyB,CAAA;IAC7B,MAAM,YAAY,GAAG,iBAAiB;QACpC,CAAC,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,EAAE,CAAA;IAEN,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,GAAG,EACH,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,OAAO,EACP,CAAC,EACD,CAAC,EACF,GAAG,gBAAgB,CAAA;IACpB,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,GAAG,YAAY,CAAA;IAEjD,IAAI,CAAC,CAAC,gBAAgB,CAAC,MAAM,KAAK,EAAE,IAAI,gBAAgB,CAAC,MAAM,KAAK,EAAE,CAAC;QACrE,MAAM,IAAI,mBAAmB,CAAC,sBAAsB,CAAC;YACnD,UAAU,EAAE;gBACV,OAAO;gBACP,KAAK;gBACL,oBAAoB;gBACpB,YAAY;gBACZ,GAAG;gBACH,EAAE;gBACF,KAAK;gBACL,IAAI;gBACJ,UAAU;gBACV,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;oBAC7B,CAAC,CAAC;wBACE,OAAO;wBACP,CAAC;wBACD,CAAC;qBACF;oBACH,CAAC,CAAC,EAAE,CAAC;aACR;YACD,UAAU;YACV,IAAI;SACL,CAAC,CAAA;IAEJ,IAAI,WAAW,GAAG;QAChB,mBAAmB,EAAE,mBAAgC;QACrD,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;QACxB,IAAI;KACyB,CAAA;IAC/B,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI;QAAE,WAAW,CAAC,EAAE,GAAG,EAAE,CAAA;IACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI;QAAE,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;IACpE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;QAAE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAA;IAChE,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrB,WAAW,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACzD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI;QAAE,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;IAC5E,IAAI,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,KAAK,IAAI;QAC7D,WAAW,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAA;IACzD,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,KAAK,IAAI;QACrD,WAAW,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;IACjD,IAAI,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,oBAAoB,KAAK,IAAI;QACrE,WAAW,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAA;IACjE,IAAI,UAAU,EAAE,MAAM,KAAK,CAAC,IAAI,UAAU,KAAK,IAAI;QACjD,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,UAAiB,CAAC,CAAA;IACtE,IAAI,KAAK,IAAI,WAAW,IAAI,MAAM;QAChC,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAkB,EAAE;YAC1D,WAAW,EAAE,WAAwB;YACrC,MAAM,EAAE,MAAmB;SAC5B,CAAC,CAAA;IAEJ,MAAM,SAAS,GACb,CAAC,IAAI,CAAC,IAAI,OAAO;QACf,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAkB,EAAE,CAAY,EAAE,CAAY,CAAC,CAAC;QACvE,CAAC,CAAC,SAAS,CAAA;IACf,IAAI,SAAS;QACX,WAAW,GAAG;YACZ,GAAG,WAAW;YACd,GAAG,SAAS;SACiB,CAAA;IAEjC,MAAM,CAAC,WAAW,CAAC,CAAA;IAEnB,OAAO,WAAW,CAAA;AACpB,CAAC;AAMD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6FG;AACH,MAAM,UAAU,IAAI,CAMlB,QAGc,EACd,UAAmC,EAAE;IAErC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAA;IAE7B,MAAM,SAAS,GAAG,CAChB,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAClC,CAAA;IAE/B,MAAM,CAAC,SAAS,CAAC,CAAA;IAEjB,OAAO;QACL,GAAG,SAAS;QACZ,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,IAAI,EAAE,SAAS;KACP,CAAA;AACZ,CAAC;AA8BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,UAAU,cAAc,CAC5B,QAAoC;IAEpC,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;AAC1C,CAAC;AAQD;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,IAAI,CAClB,QAAyE,EACzE,UAAiC,EAAE;IAEnC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;IAC3B,OAAO,IAAI,CAAC,SAAS,CACnB,SAAS,CAAC;QACR,GAAG,QAAQ;QACX,GAAG,CAAC,OAAO;YACT,CAAC,CAAC;gBACE,QAAQ,EAAE,SAAS;gBACnB,CAAC,EAAE,SAAS;gBACZ,CAAC,EAAE,SAAS;gBACZ,OAAO,EAAE,SAAS;gBAClB,CAAC,EAAE,SAAS;aACb;YACH,CAAC,CAAC,EAAE,CAAC;KACR,CAAC,CACH,CAAA;AACH,CAAC;AAgBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+DG;AACH,MAAM,UAAU,SAAS,CACvB,QAAuD,EACvD,UAA6B,EAAE;IAE/B,MAAM,EACJ,mBAAmB,EACnB,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,KAAK,EACL,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,IAAI,GACL,GAAG,QAAQ,CAAA;IAEZ,MAAM,CAAC,QAAQ,CAAC,CAAA;IAEhB,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;IAE1D,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,CAAA;IAElE,MAAM,UAAU,GAAG;QACjB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC;QACvB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QAClE,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QAClD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QAChC,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,IAAI,IAAI,IAAI;QACZ,eAAe;QACf,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI;QAC1D,mBAAmB,IAAI,EAAE;QACzB,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KAC1C,CAAA;IAEV,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAA;IACtD,MAAM,KAAK,GAAc,EAAE,CAAA;IAC3B,MAAM,WAAW,GAAc,EAAE,CAAA;IACjC,MAAM,MAAM,GAAc,EAAE,CAAA;IAC5B,IAAI,QAAQ;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAE,CAAA;YAChD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChB,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACpB,CAAC;IAEH,OAAO,GAAG,CAAC,MAAM,CACf,MAAM,EACN,QAAQ;QACN,CAAC,CAAC,qEAAqE;YACrE,GAAG,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC,wCAAwC;YACxC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CACd,CAAA;AACjB,CAAC;AAmBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,UAAU,KAAK,CAAC,QAAkD;IACtE,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAE7C,OAAO;QACL,GAAG,QAAQ;QACX,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QACzC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;QACrC,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG,KAAK,QAAQ;YAClC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvC,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;YAC/C,CAAC,CAAC,EAAE,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YACjE,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,YAAY,KAAK,QAAQ;YAC3C,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACzD,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,oBAAoB,KAAK,QAAQ;YACnD,CAAC,CAAC,EAAE,oBAAoB,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YACzE,CAAC,CAAC,EAAE,CAAC;QACP,IAAI,EAAE,KAAK;QACX,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACxC,CAAA;AACZ,CAAC;AAMD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,QAAQ,CACtB,QAAuD;IAEvD,IAAI,CAAC;QACH,MAAM,CAAC,QAAQ,CAAC,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC"}