{"version": 3, "file": "Rlp.js", "sourceRoot": "", "sources": ["../../core/Rlp.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,MAAM,MAAM,sBAAsB,CAAA;AAG9C;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,OAAO,CACrB,KAA4B;IAE5B,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;AAC3B,CAAC;AAMD;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,KAAK,CAAC,KAA4B;IAChD,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AACzB,CAAC;AAMD,iFAAiF;AACjF,WAAW;AACX,iFAAiF;AAEjF,gBAAgB;AAChB,MAAM,UAAU,EAAE,CAGhB,KAAY,EAAE,EAAwB;IACtC,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;IAE/D,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;gBAC5C,MAAM,IAAI,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;YACzC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAC7B,CAAC;QACD,OAAO,KAAoB,CAAA;IAC7B,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;QAClC,kBAAkB,EAAE,MAAM,CAAC,iBAAiB;KAC7C,CAAC,CAAA;IACF,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAE3C,OAAO,MAA2B,CAAA;AACpC,CAAC;AAgBD,gBAAgB;AAEhB,gBAAgB;AAChB,MAAM,UAAU,eAAe,CAC7B,MAAqB,EACrB,KAAuC,KAAK;IAE5C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;QAC3B,OAAO,CACL,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CACxB,CAAA;IAErC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;IAChC,IAAI,MAAM,GAAG,IAAI;QAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;IAE9C,QAAQ;IACR,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;QAClB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACtC,OAAO,CACL,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CACV,CAAA;IACrC,CAAC;IAED,OAAO;IACP,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC/C,OAAO,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAyC,CAAA;AAC7E,CAAC;AAYD,gBAAgB;AAChB,MAAM,UAAU,UAAU,CACxB,MAAqB,EACrB,MAAc,EACd,MAAc;IAEd,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI;QAAE,OAAO,CAAC,CAAA;IAC9C,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;QAAE,OAAO,MAAM,GAAG,MAAM,CAAA;IACjD,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAA;IACzD,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;AAClD,CAAC;AAOD,gBAAgB;AAChB,MAAM,UAAU,QAAQ,CACtB,MAAqB,EACrB,MAAc,EACd,EAAwB;IAExB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;IAChC,MAAM,KAAK,GAAqC,EAAE,CAAA;IAClD,OAAO,MAAM,CAAC,QAAQ,GAAG,QAAQ,GAAG,MAAM;QACxC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;IACzC,OAAO,KAAK,CAAA;AACd,CAAC;AAYD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,IAAI,CAClB,KAA4D,EAC5D,OAAyB;IAEzB,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAA;IAEtB,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;IACrC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;IAC9D,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAExB,IAAI,EAAE,KAAK,KAAK;QAAE,OAAO,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAwB,CAAA;IAC3E,OAAO,MAAM,CAAC,KAA4B,CAAA;AAC5C,CAAC;AAmBD;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,SAAS,CACvB,KAAkC,EAClC,UAAiC,EAAE;IAEnC,MAAM,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,CAAA;IAChC,OAAO,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAU,CAAA;AACrC,CAAC;AAYD;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,OAAO,CACrB,GAA4B,EAC5B,UAA+B,EAAE;IAEjC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,CAAA;IAC9B,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAU,CAAA;AACnC,CAAC;AAYD,iFAAiF;AACjF,WAAW;AACX,iFAAiF;AAEjF,SAAS,YAAY,CACnB,KAA4D;IAE5D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACtB,OAAO,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,OAAO,iBAAiB,CAAC,KAAY,CAAC,CAAA;AACxC,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAiB;IACzC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;IAE7D,MAAM,gBAAgB,GAAG,eAAe,CAAC,UAAU,CAAC,CAAA;IACpD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,CAAC,GAAG,UAAU,CAAA;QAC3C,OAAO,CAAC,GAAG,gBAAgB,GAAG,UAAU,CAAA;IAC1C,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,MAAM;QACN,MAAM,CAAC,MAAqB;YAC1B,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;gBACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC,CAAA;YACpC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,gBAAgB,CAAC,CAAA;gBAC7C,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;qBACnD,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;qBACzD,IAAI,gBAAgB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;;oBACzD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;YACpC,CAAC;YACD,KAAK,MAAM,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;gBAC9B,MAAM,CAAC,MAAM,CAAC,CAAA;YAChB,CAAC;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAiC;IAC1D,MAAM,KAAK,GACT,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA;IAEzE,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IACvD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAE,GAAG,IAAI;YAAE,OAAO,CAAC,CAAA;QACpD,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE;YAAE,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;QAC/C,OAAO,CAAC,GAAG,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAA;IAC7C,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,MAAM;QACN,MAAM,CAAC,MAAqB;YAC1B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAE,GAAG,IAAI,EAAE,CAAC;gBAC3C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;iBAAM,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAA;gBACpC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,iBAAiB,CAAC,CAAA;gBAC9C,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;qBACtD,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;qBAC5D,IAAI,iBAAiB,KAAK,CAAC;oBAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;;oBAC5D,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBACpC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,MAAc;IACrC,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC;QAAE,OAAO,CAAC,CAAA;IAC7B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;QAAE,OAAO,CAAC,CAAA;IAC9B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAA;AACpD,CAAC"}