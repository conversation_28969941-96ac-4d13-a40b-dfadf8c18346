{"version": 3, "file": "AbiError.js", "sourceRoot": "", "sources": ["../../core/AbiError.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,OAAO,MAAM,SAAS,CAAA;AAElC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,aAAa,MAAM,oBAAoB,CAAA;AAEnD,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AA6I/B,gBAAgB;AAChB,MAAM,UAAU,MAAM,CACpB,QAAkB,EAClB,IAAa,EACb,UAA0B,EAAE;IAE5B,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAAE,MAAM,IAAI,OAAO,CAAC,wBAAwB,CAAC,EAAE,IAAI,EAAE,CAAC,CAAA;IAC5E,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,SAAS,CAAA;IAElD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CACjC,QAAQ,CAAC,MAAM,EACf,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAClB,OAAO,CACR,CAAA;IACD,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAA;QAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACjC,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAyCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,MAAM,UAAU,MAAM,CACpB,QAAkB,EAClB,GAAG,IAA2B;IAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAA;IAEtC,MAAM,IAAI,GACR,IAAI,CAAC,MAAM,GAAG,CAAC;QACb,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAG,IAAY,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,SAAS,CAAA;IAEf,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;AACrD,CAAC;AAmBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAM,UAAU,MAAM,CACpB,QAA6B;IAE7B,OAAO,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAU,CAAA;AACjD,CAAC;AAMD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2FG;AACH,MAAM,UAAU,IAAI,CAGlB,QAOG,EACH,UAAwB,EAAE;IAE1B,OAAO,OAAO,CAAC,IAAI,CAAC,QAAoB,EAAE,OAAO,CAAU,CAAA;AAC7D,CAAC;AAmBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+DG;AACH,MAAM,UAAU,OAAO,CASrB,GAAuC,EACvC,IAAsD,EACtD,OAKC;IAED,IAAI,IAAI,KAAK,OAAO;QAAE,OAAO,aAAsB,CAAA;IACnD,IAAI,IAAI,KAAK,OAAO;QAAE,OAAO,aAAsB,CAAA;IACnD,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;QACtC,IAAI,QAAQ,KAAK,qBAAqB;YAAE,OAAO,aAAsB,CAAA;QACrE,IAAI,QAAQ,KAAK,qBAAqB;YAAE,OAAO,aAAsB,CAAA;IACvE,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,OAAc,CAAC,CAAA;IACvD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;QACvB,MAAM,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;IAC1D,OAAO,IAAa,CAAA;AACtB,CAAC;AA2BD;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,UAAU,WAAW,CAAC,OAA0B;IACpD,OAAO,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AACrC,CAAC;AAMD,0GAA0G;AAC1G,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,CAAC,EAAE,+BAA+B;IAClC,EAAE,EAAE,yDAAyD;IAC7D,EAAE,EAAE,wDAAwD;IAC5D,EAAE,EAAE,0CAA0C;IAC9C,EAAE,EAAE,uEAAuE;IAC3E,EAAE,EAAE,sCAAsC;IAC1C,EAAE,EAAE,+BAA+B;IACnC,EAAE,EAAE,mEAAmE;IACvE,EAAE,EAAE,0EAA0E;CACrD,CAAA;AAE3B,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC;IAC9C,MAAM,EAAE;QACN;YACE,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,QAAQ;SACf;KACF;IACD,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;CACd,CAAC,CAAA;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,YAAY,CAAA;AAEjD,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC;IAC9C,MAAM,EAAE;QACN;YACE,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,OAAO;SACd;KACF;IACD,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;CACd,CAAC,CAAA;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,YAAY,CAAA"}