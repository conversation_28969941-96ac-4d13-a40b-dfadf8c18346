import * as Hex from './Hex.js';
import * as Log from './Log.js';
/** RPC status to status mapping. */
export const fromRpcStatus = {
    '0x0': 'reverted',
    '0x1': 'success',
};
/** Status to RPC status mapping. */
export const toRpcStatus = {
    reverted: '0x0',
    success: '0x1',
};
/** RPC type to type mapping. */
export const fromRpcType = {
    '0x0': 'legacy',
    '0x1': 'eip2930',
    '0x2': 'eip1559',
    '0x3': 'eip4844',
    '0x4': 'eip7702',
};
/** Type to RPC type mapping. */
export const toRpcType = {
    legacy: '0x0',
    eip2930: '0x1',
    eip1559: '0x2',
    eip4844: '0x3',
    eip7702: '0x4',
};
/**
 * Converts a {@link ox#TransactionReceipt.Rpc} to an {@link ox#TransactionReceipt.TransactionReceipt}.
 *
 * @example
 * ```ts twoslash
 * import { TransactionReceipt } from 'ox'
 *
 * const receipt = TransactionReceipt.fromRpc({
 *   blobGasPrice: '0x42069',
 *   blobGasUsed: '0x1337',
 *   blockHash:
 *     '0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b',
 *   blockNumber: '0x12f296f',
 *   contractAddress: null,
 *   cumulativeGasUsed: '0x82515',
 *   effectiveGasPrice: '0x21c2f6c09',
 *   from: '******************************************',
 *   gasUsed: '0x2abba',
 *   logs: [],
 *   logsBloom:
 *     '0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000',
 *   status: '0x1',
 *   to: '******************************************',
 *   transactionHash:
 *     '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *   transactionIndex: '0x2',
 *   type: '0x2',
 * })
 * // @log: {
 * // @log:   blobGasPrice: 270441n,
 * // @log:   blobGasUsed: 4919n,
 * // @log:   blockHash: "0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b",
 * // @log:   blockNumber: 19868015n,
 * // @log:   contractAddress: null,
 * // @log:   cumulativeGasUsed: 533781n,
 * // @log:   effectiveGasPrice: 9062804489n,
 * // @log:   from: "******************************************",
 * // @log:   gasUsed: 175034n,
 * // @log:   logs: [],
 * // @log:   logsBloom: "0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000",
 * // @log:   root: undefined,
 * // @log:   status: "success",
 * // @log:   to: "******************************************",
 * // @log:   transactionHash: "0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0",
 * // @log:   transactionIndex: 2,
 * // @log:   type: "eip1559",
 * // @log: }
 * ```
 *
 * @example
 * ### End-to-end
 *
 * Below is an example of how to use the `TransactionReceipt.fromRpc` method to convert an RPC transaction receipt to a {@link ox#TransactionReceipt.TransactionReceipt} object.
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { TransactionReceipt } from 'ox'
 *
 * const receipt = await window.ethereum!
 *   .request({
 *     method: 'eth_getTransactionReceipt',
 *     params: [
 *       '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *     ],
 *   })
 *   .then(TransactionReceipt.fromRpc) // [!code hl]
 * // @log: {
 * // @log:   blobGasPrice: 270441n,
 * // @log:   blobGasUsed: 4919n,
 * // @log:   blockHash: "0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b",
 * // @log:   blockNumber: 19868015n,
 * // @log:   contractAddress: null,
 * // @log:   cumulativeGasUsed: 533781n,
 * // @log:   effectiveGasPrice: 9062804489n,
 * // @log:   from: "******************************************",
 * // @log:   gasUsed: 175034n,
 * // @log:   logs: [],
 * // @log:   logsBloom: "0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000",
 * // @log:   root: undefined,
 * // @log:   status: "success",
 * // @log:   to: "******************************************",
 * // @log:   transactionHash: "0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0",
 * // @log:   transactionIndex: 2,
 * // @log:   type: "eip1559",
 * // @log: }
 * ```
 *
 * :::note
 *
 * For simplicity, the above example uses `window.ethereum.request`, but you can use any
 * type of JSON-RPC interface.
 *
 * :::
 *
 * @param receipt - The RPC receipt to convert.
 * @returns An instantiated {@link ox#TransactionReceipt.TransactionReceipt}.
 */
export function fromRpc(receipt) {
    if (!receipt)
        return null;
    return {
        ...receipt,
        blobGasPrice: receipt.blobGasPrice
            ? BigInt(receipt.blobGasPrice)
            : undefined,
        blobGasUsed: receipt.blobGasUsed ? BigInt(receipt.blobGasUsed) : undefined,
        blockNumber: BigInt(receipt.blockNumber ?? 0n),
        cumulativeGasUsed: BigInt(receipt.cumulativeGasUsed ?? 0n),
        effectiveGasPrice: BigInt(receipt.effectiveGasPrice ?? 0n),
        gasUsed: BigInt(receipt.gasUsed ?? 0n),
        logs: receipt.logs.map((log) => Log.fromRpc(log, { pending: false })),
        status: fromRpcStatus[receipt.status],
        transactionIndex: Number(receipt.transactionIndex ?? 0),
        type: fromRpcType[receipt.type] || receipt.type,
    };
}
/**
 * Converts a {@link ox#TransactionReceipt.TransactionReceipt} to a {@link ox#TransactionReceipt.Rpc}.
 *
 * @example
 * ```ts twoslash
 * import { TransactionReceipt } from 'ox'
 *
 * const receipt = TransactionReceipt.toRpc({
 *   blobGasPrice: 270441n,
 *   blobGasUsed: 4919n,
 *   blockHash:
 *     '0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b',
 *   blockNumber: 19868015n,
 *   contractAddress: null,
 *   cumulativeGasUsed: 533781n,
 *   effectiveGasPrice: 9062804489n,
 *   from: '******************************************',
 *   gasUsed: 175034n,
 *   logs: [],
 *   logsBloom:
 *     '0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000',
 *   root: undefined,
 *   status: 'success',
 *   to: '******************************************',
 *   transactionHash:
 *     '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *   transactionIndex: 2,
 *   type: 'eip1559',
 * })
 * // @log: {
 * // @log:   blobGasPrice: "0x042069",
 * // @log:   blobGasUsed: "0x1337",
 * // @log:   blockHash: "0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b",
 * // @log:   blockNumber: "0x012f296f",
 * // @log:   contractAddress: null,
 * // @log:   cumulativeGasUsed: "0x082515",
 * // @log:   effectiveGasPrice: "0x021c2f6c09",
 * // @log:   from: "******************************************",
 * // @log:   gasUsed: "0x02abba",
 * // @log:   logs: [],
 * // @log:   logsBloom: "0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000",
 * // @log:   root: undefined,
 * // @log:   status: "0x1",
 * // @log:   to: "******************************************",
 * // @log:   transactionHash: "0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0",
 * // @log:   transactionIndex: "0x02",
 * // @log:   type: "eip1559",
 * // @log: }
 * ```
 *
 * @param receipt - The receipt to convert.
 * @returns An RPC receipt.
 */
export function toRpc(receipt) {
    return {
        blobGasPrice: receipt.blobGasPrice
            ? Hex.fromNumber(receipt.blobGasPrice)
            : undefined,
        blobGasUsed: receipt.blobGasUsed
            ? Hex.fromNumber(receipt.blobGasUsed)
            : undefined,
        blockHash: receipt.blockHash,
        blockNumber: Hex.fromNumber(receipt.blockNumber),
        contractAddress: receipt.contractAddress,
        cumulativeGasUsed: Hex.fromNumber(receipt.cumulativeGasUsed),
        effectiveGasPrice: Hex.fromNumber(receipt.effectiveGasPrice),
        from: receipt.from,
        gasUsed: Hex.fromNumber(receipt.gasUsed),
        logs: receipt.logs.map(Log.toRpc),
        logsBloom: receipt.logsBloom,
        root: receipt.root,
        status: toRpcStatus[receipt.status],
        to: receipt.to,
        transactionHash: receipt.transactionHash,
        transactionIndex: Hex.fromNumber(receipt.transactionIndex),
        type: toRpcType[receipt.type] ?? receipt.type,
    };
}
//# sourceMappingURL=TransactionReceipt.js.map