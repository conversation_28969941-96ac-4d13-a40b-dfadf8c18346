{"version": 3, "file": "BlockOverrides.js", "sourceRoot": "", "sources": ["../../core/BlockOverrides.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,UAAU,MAAM,iBAAiB,CAAA;AA6B7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAsB;IAC5C,OAAO;QACL,GAAG,CAAC,iBAAiB,CAAC,aAAa,IAAI;YACrC,aAAa,EAAE,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC;SACvD,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,WAAW,IAAI;YACnC,WAAW,EAAE,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC;SACnD,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,YAAY,IAAI;YACpC,YAAY,EAAE,iBAAiB,CAAC,YAAY;SAC7C,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,QAAQ,IAAI;YAChC,QAAQ,EAAE,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SAC7C,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,MAAM,IAAI;YAC9B,MAAM,EAAE,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;SACzC,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,UAAU,IAAI;YAClC,UAAU,EAAE,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;SACjD,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,IAAI,IAAI;YAC5B,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC;SACrC,CAAC;QACF,GAAG,CAAC,iBAAiB,CAAC,WAAW,IAAI;YACnC,WAAW,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC;SACnE,CAAC;KACH,CAAA;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,KAAK,CAAC,cAA8B;IAClD,OAAO;QACL,GAAG,CAAC,OAAO,cAAc,CAAC,aAAa,KAAK,QAAQ,IAAI;YACtD,aAAa,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC;SAC5D,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,WAAW,KAAK,QAAQ,IAAI;YACpD,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC;SACxD,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,YAAY,KAAK,QAAQ,IAAI;YACrD,YAAY,EAAE,cAAc,CAAC,YAAY;SAC1C,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,QAAQ,KAAK,QAAQ,IAAI;YACjD,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC;SAClD,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,MAAM,KAAK,QAAQ,IAAI;YAC/C,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;SAC9C,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,UAAU,KAAK,QAAQ,IAAI;YACnD,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC;SACtD,CAAC;QACF,GAAG,CAAC,OAAO,cAAc,CAAC,IAAI,KAAK,QAAQ,IAAI;YAC7C,IAAI,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC;SAC1C,CAAC;QACF,GAAG,CAAC,cAAc,CAAC,WAAW,IAAI;YAChC,WAAW,EAAE,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;SAC9D,CAAC;KACH,CAAA;AACH,CAAC"}