{"version": 3, "file": "Siwe.js", "sourceRoot": "", "sources": ["../../core/Siwe.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAA;AAEvC,MAAM,CAAC,MAAM,WAAW,GACtB,+EAA+E,CAAA;AAEjF,MAAM,CAAC,MAAM,OAAO,GAClB,gLAAgL,CAAA;AAElL,MAAM,CAAC,MAAM,cAAc,GAAG,2BAA2B,CAAA;AAEzD,MAAM,CAAC,MAAM,UAAU,GAAG,mBAAmB,CAAA;AAE7C,MAAM,CAAC,MAAM,WAAW,GAAG,6BAA6B,CAAA;AAExD,2BAA2B;AAC3B,MAAM,CAAC,MAAM,WAAW,GACtB,0MAA0M,CAAA;AAE5M,2BAA2B;AAC3B,MAAM,CAAC,MAAM,WAAW,GACtB,uQAAuQ,CAAA;AA0DzQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,aAAa,CAAC,KAAc;IAC1C,MAAM,EACJ,OAAO,EACP,MAAM,EACN,cAAc,EACd,QAAQ,GAAG,IAAI,IAAI,EAAE,EACrB,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,GAAG,EACH,OAAO,GACR,GAAG,KAAK,CAAA;IAET,kBAAkB;IAClB,CAAC;QACC,kBAAkB;QAClB,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACjC,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,SAAS;gBAChB,YAAY,EAAE;oBACZ,wCAAwC;oBACxC,8CAA8C;oBAC9C,EAAE;oBACF,mBAAmB,OAAO,EAAE;iBAC7B;aACF,CAAC,CAAA;QACJ,IACE,CAAC,CACC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAC5B;YAED,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,QAAQ;gBACf,YAAY,EAAE;oBACZ,yCAAyC;oBACzC,8CAA8C;oBAC9C,EAAE;oBACF,mBAAmB,MAAM,EAAE;iBAC5B;aACF,CAAC,CAAA;QACJ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,OAAO;gBACd,YAAY,EAAE;oBACZ,wCAAwC;oBACxC,+BAA+B;oBAC/B,EAAE;oBACF,mBAAmB,KAAK,EAAE;iBAC3B;aACF,CAAC,CAAA;QACJ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACb,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,KAAK;gBACZ,YAAY,EAAE;oBACZ,4FAA4F;oBAC5F,8CAA8C;oBAC9C,EAAE;oBACF,mBAAmB,GAAG,EAAE;iBACzB;aACF,CAAC,CAAA;QACJ,IAAI,OAAO,KAAK,GAAG;YACjB,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,SAAS;gBAChB,YAAY,EAAE;oBACZ,wBAAwB;oBACxB,EAAE;oBACF,mBAAmB,OAAO,EAAE;iBAC7B;aACF,CAAC,CAAA;QAEJ,kBAAkB;QAClB,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YACrC,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,QAAQ;gBACf,YAAY,EAAE;oBACZ,0CAA0C;oBAC1C,0DAA0D;oBAC1D,EAAE;oBACF,mBAAmB,MAAM,EAAE;iBAC5B;aACF,CAAC,CAAA;QACJ,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAA;QACjC,IAAI,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC;YAC3B,MAAM,IAAI,wBAAwB,CAAC;gBACjC,KAAK,EAAE,WAAW;gBAClB,YAAY,EAAE;oBACZ,qCAAqC;oBACrC,EAAE;oBACF,mBAAmB,SAAS,EAAE;iBAC/B;aACF,CAAC,CAAA;IACN,CAAC;IAED,oBAAoB;IACpB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAA;IAC/D,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,MAAM;YAAE,OAAO,GAAG,MAAM,MAAM,MAAM,EAAE,CAAA;QAC1C,OAAO,MAAM,CAAA;IACf,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;QACtB,IAAI,CAAC,KAAK,CAAC,SAAS;YAAE,OAAO,EAAE,CAAA;QAC/B,OAAO,GAAG,KAAK,CAAC,SAAS,IAAI,CAAA;IAC/B,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,MAAM,GAAG,GAAG,MAAM,sDAAsD,OAAO,OAAO,SAAS,EAAE,CAAA;IAEvG,IAAI,MAAM,GAAG,QAAQ,GAAG,cAAc,OAAO,eAAe,OAAO,YAAY,KAAK,gBAAgB,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAA;IAE5H,IAAI,cAAc;QAChB,MAAM,IAAI,sBAAsB,cAAc,CAAC,WAAW,EAAE,EAAE,CAAA;IAChE,IAAI,SAAS;QAAE,MAAM,IAAI,iBAAiB,SAAS,CAAC,WAAW,EAAE,EAAE,CAAA;IACnE,IAAI,SAAS;QAAE,MAAM,IAAI,iBAAiB,SAAS,EAAE,CAAA;IACrD,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,OAAO,GAAG,cAAc,CAAA;QAC5B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAClB,MAAM,IAAI,wBAAwB,CAAC;oBACjC,KAAK,EAAE,WAAW;oBAClB,YAAY,EAAE;wBACZ,0CAA0C;wBAC1C,8CAA8C;wBAC9C,EAAE;wBACF,mBAAmB,QAAQ,EAAE;qBAC9B;iBACF,CAAC,CAAA;YACJ,OAAO,IAAI,OAAO,QAAQ,EAAE,CAAA;QAC9B,CAAC;QACD,MAAM,IAAI,OAAO,CAAA;IACnB,CAAC;IAED,OAAO,GAAG,MAAM,KAAK,MAAM,EAAE,CAAA;AAC/B,CAAC;AASD;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,GAAG,CAAC,EAAE,CAAC,CAAA;AAChB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,4CAA4C;AAC5C,MAAM,UAAU,KAAK,CAAC,KAAa;IACjC,+BAA+B;IAC/B,IAAI,0DAA0D,CAAC,IAAI,CAAC,KAAK,CAAC;QACxE,OAAO,KAAK,CAAA;IAEd,6CAA6C;IAC7C,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAA;IAC3C,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAA;IAEzD,gBAAgB;IAChB,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC1B,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IACzB,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAE5B,6DAA6D;IAC7D,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QAAE,OAAO,KAAK,CAAA;IAE/D,oEAAoE;IACpE,IAAI,SAAS,EAAE,MAAM,EAAE,CAAC;QACtB,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;IAC5D,CAAC;SAAM,CAAC;QACN,+DAA+D;QAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAA;IACtC,CAAC;IAED,+EAA+E;IAC/E,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAAE,OAAO,KAAK,CAAA;IAEtE,IAAI,GAAG,GAAG,EAAE,CAAA;IACZ,kDAAkD;IAClD,GAAG,IAAI,GAAG,MAAM,GAAG,CAAA;IACnB,IAAI,SAAS,EAAE,MAAM;QAAE,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;IAE9C,GAAG,IAAI,IAAI,CAAA;IAEX,IAAI,KAAK,EAAE,MAAM;QAAE,GAAG,IAAI,IAAI,KAAK,EAAE,CAAA;IACrC,IAAI,QAAQ,EAAE,MAAM;QAAE,GAAG,IAAI,IAAI,QAAQ,EAAE,CAAA;IAE3C,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,QAAQ,CAAC,KAAa;IAC7B,OAAO,KAAK,CAAC,KAAK,CAChB,sEAAsE,CACtE,CAAA;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,YAAY,CAAC,OAAe;IAC1C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;QAClE,EAAE,MAAM,IAAI,EAAE,CAKf,CAAA;IACD,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE,GAC1E,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,MAAM,IAAI,EAAE,CASxC,CAAA;IACH,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACxE,OAAO;QACL,GAAG,MAAM;QACT,GAAG,MAAM;QACT,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACpC,CAAA;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,UAAU,eAAe,CAAC,KAA4B;IAC1D,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,EAAE,GAAG,KAAK,CAAA;IAE5E,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM;QAAE,OAAO,KAAK,CAAA;IACrD,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK;QAAE,OAAO,KAAK,CAAA;IAClD,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM;QAAE,OAAO,KAAK,CAAA;IAErD,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,IAAI,OAAO,CAAC,cAAc;QAAE,OAAO,KAAK,CAAA;IAC1E,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS;QAAE,OAAO,KAAK,CAAA;IAE/D,IAAI,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,OAAO;YAAE,OAAO,KAAK,CAAA;QAClC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;YAAE,OAAO,KAAK,CAAA;IACzE,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAiCD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,OAAO,wBAAyB,SAAQ,MAAM,CAAC,SAAS;IAG5D,YAAY,UAGX;QACC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,UAAU,CAAA;QAC1C,KAAK,CAAC,gDAAgD,KAAK,IAAI,EAAE;YAC/D,YAAY;SACb,CAAC,CAAA;QATc;;;;mBAAO,+BAA+B;WAAA;IAUxD,CAAC;CACF"}