{"version": 3, "file": "RpcTransport.js", "sourceRoot": "", "sources": ["../../core/RpcTransport.ts"], "names": [], "mappings": ";;;AA6EA,4BAwEC;AArJD,sCAAqC;AAGrC,oDAA6C;AAC7C,iDAAgD;AAEhD,uDAAsD;AAuEtD,SAAgB,QAAQ,CAGtB,GAAW,EAAE,UAAyC,EAAE;IACxD,OAAO,QAAQ,CAAC,MAAM,CACpB;QACE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ;YAC3B,MAAM,EACJ,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,EAClC,YAAY,EAAE,aAAa,GAAG,OAAO,CAAC,YAAY,EAClD,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,MAAM,GACpC,GAAG,QAAQ,CAAA;YAEZ,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAElC,MAAM,YAAY,GAChB,OAAO,aAAa,KAAK,UAAU;gBACjC,CAAC,CAAC,MAAM,aAAa,CAAC,KAAK,CAAC;gBAC5B,CAAC,CAAC,aAAa,CAAA;YAEnB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,WAAW,CACxC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;gBACb,MAAM,IAAI,GAAgB;oBACxB,GAAG,YAAY;oBACf,IAAI;oBACJ,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;wBAClC,GAAG,YAAY,EAAE,OAAO;qBACzB;oBACD,MAAM,EAAE,YAAY,EAAE,MAAM,IAAI,MAAM;oBACtC,MAAM,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;iBAC9D,CAAA;gBACD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;gBACtC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,CAAC,EACD;gBACE,OAAO;gBACP,MAAM,EAAE,IAAI;aACb,CACF,CAAA;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;gBAC7B,IACE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,UAAU,CAAC,kBAAkB,CAAC;oBAEpE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAA;gBACxB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBACnC,IAAI,CAAC;wBACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAA;oBACjC,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACb,IAAI,QAAQ,CAAC,EAAE;4BACb,MAAM,IAAI,sBAAsB,CAAC;gCAC/B,QAAQ,EAAE,IAAI;6BACf,CAAC,CAAA;wBACJ,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;oBACxB,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,EAAE,CAAA;YAEJ,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACd,MAAM,IAAI,SAAS,CAAC;oBAClB,IAAI;oBACJ,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,UAAU;oBAC1D,QAAQ;oBACR,GAAG;iBACJ,CAAC,CAAA;YAEJ,OAAO,IAAa,CAAA;QACtB,CAAC;KACF,EACD,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CACrB,CAAA;AACH,CAAC;AAeD,MAAa,SAAU,SAAQ,MAAM,CAAC,SAAS;IAG7C,YAAY,EACV,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,GAAG,GACiE;QACpE,KAAK,CAAC,sBAAsB,EAAE;YAC5B,OAAO;YACP,YAAY,EAAE;gBACZ,WAAW,QAAQ,CAAC,MAAM,EAAE;gBAC5B,QAAQ,IAAA,kBAAM,EAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;aACnD;SACF,CAAC,CAAA;QAfc;;;;mBAAO,wBAAwB;WAAA;IAgBjD,CAAC;CACF;AAlBD,8BAkBC;AAGD,MAAa,sBAAuB,SAAQ,MAAM,CAAC,SAAS;IAG1D,YAAY,EAAE,QAAQ,EAAwB;QAC5C,KAAK,CAAC,4CAA4C,EAAE;YAClD,YAAY,EAAE,CAAC,aAAa,QAAQ,EAAE,CAAC;SACxC,CAAC,CAAA;QALc;;;;mBAAO,qCAAqC;WAAA;IAM9D,CAAC;CACF;AARD,wDAQC"}