{"version": 3, "file": "AbiParameters.js", "sourceRoot": "", "sources": ["../../core/AbiParameters.ts"], "names": [], "mappings": ";;;AAwEA,wBAoCC;AAwED,wBAyBC;AAqCD,oCAgBC;AAkGD,wBAcC;AA0FD,oBAUC;AAtdD,mCAAkC;AAClC,wCAAuC;AACvC,oCAAmC;AACnC,sCAAqC;AACrC,gCAA+B;AAC/B,0CAAyC;AACzC,wDAAuD;AACvD,+CAA8C;AAiE9C,SAAgB,MAAM,CACpB,UAAyB,EACzB,IAA2B,EAC3B,UAGI,EAAE;IAEN,MAAM,EAAE,EAAE,GAAG,OAAO,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAA;IAEzD,MAAM,KAAK,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACnE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAEnC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC;QAClD,MAAM,IAAI,aAAa,EAAE,CAAA;IAC3B,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;QAC7C,MAAM,IAAI,qBAAqB,CAAC;YAC9B,IAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YAC3D,UAAU,EAAE,UAAkC;YAC9C,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;SACxB,CAAC,CAAA;IAEJ,IAAI,QAAQ,GAAG,CAAC,CAAA;IAChB,MAAM,MAAM,GAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAc,CAAA;QACxC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAC5B,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE;YAChE,eAAe;YACf,cAAc,EAAE,CAAC;SAClB,CAAC,CAAA;QACF,QAAQ,IAAI,SAAS,CAAA;QACrB,IAAI,EAAE,KAAK,OAAO;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;;YAChC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;IACrC,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAwED,SAAgB,MAAM,CAGpB,UAAsB,EACtB,MAES,EACT,OAAwB;IAExB,MAAM,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAA;IAEjD,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;QACrC,MAAM,IAAI,mBAAmB,CAAC;YAC5B,cAAc,EAAE,UAAU,CAAC,MAAgB;YAC3C,WAAW,EAAE,MAAM,CAAC,MAAa;SAClC,CAAC,CAAA;IAEJ,MAAM,kBAAkB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;QACpD,eAAe;QACf,UAAU,EAAE,UAAkC;QAC9C,MAAM,EAAE,MAAa;KACtB,CAAC,CAAA;IACF,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAA;IAChD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IAClC,OAAO,IAAI,CAAA;AACb,CAAC;AAqCD,SAAgB,YAAY,CAE1B,KAAqB,EAAE,MAA2C;IAClE,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;QAChC,MAAM,IAAI,mBAAmB,CAAC;YAC5B,cAAc,EAAE,KAAK,CAAC,MAAgB;YACtC,WAAW,EAAE,MAAM,CAAC,MAAgB;SACrC,CAAC,CAAA;IAEJ,MAAM,IAAI,GAAc,EAAE,CAAA;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAI,KAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;IAC7C,CAAC;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAA;AAC5B,CAAC;AAED,WAAiB,YAAY;IAe3B,SAAgB,MAAM,CACpB,IAAmB,EACnB,KAAiC,EACjC,OAAO,GAAG,KAAK;QAEf,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,KAAwB,CAAA;YACxC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YACvB,OAAO,GAAG,CAAC,OAAO,CAChB,OAAO,CAAC,WAAW,EAAa,EAChC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CACE,CAAA;QACtB,CAAC;QACD,IAAI,IAAI,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAC,UAAU,CAAC,KAAe,CAAC,CAAA;QAC7D,IAAI,IAAI,KAAK,OAAO;YAAE,OAAO,KAAgB,CAAA;QAC7C,IAAI,IAAI,KAAK,MAAM;YACjB,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,KAAgB,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAEzE,MAAM,QAAQ,GAAI,IAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;QAC9D,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAA;YAChD,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACtC,OAAO,GAAG,CAAC,UAAU,CAAC,KAAe,EAAE;gBACrC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;gBACzB,MAAM,EAAE,QAAQ,KAAK,KAAK;aAC3B,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,UAAU,GAAI,IAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC9D,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,UAAU,CAAA;YAChC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAK,CAAC,KAAK,CAAE,KAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;gBAChE,MAAM,IAAI,sBAAsB,CAAC;oBAC/B,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAK,CAAC;oBACpC,KAAK,EAAE,KAAgB;iBACxB,CAAC,CAAA;YACJ,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAgB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAY,CAAA;QACpE,CAAC;QAED,MAAM,UAAU,GAAI,IAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC9D,IAAI,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,UAAU,CAAA;YACrC,MAAM,IAAI,GAAc,EAAE,CAAA;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAA;YAC9C,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,IAAI,CAAA;YAClC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAA;QAC5B,CAAC;QAED,MAAM,IAAI,gBAAgB,CAAC,IAAc,CAAC,CAAA;IAC5C,CAAC;IAnDe,mBAAM,SAmDrB,CAAA;AACH,CAAC,EAnEgB,YAAY,4BAAZ,YAAY,QAmE5B;AA6BD,SAAgB,MAAM,CAMpB,UAKK;IAEL,OAAO,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA;AAChD,CAAC;AA0FD,SAAgB,IAAI,CAGlB,UAAmE;IAEnE,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ;QAChE,OAAO,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAU,CAAA;IACxD,IAAI,OAAO,UAAU,KAAK,QAAQ;QAChC,OAAO,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAU,CAAA;IACxD,OAAO,UAAmB,CAAA;AAC5B,CAAC;AAuCD,MAAa,qBAAsB,SAAQ,MAAM,CAAC,SAAS;IAEzD,YAAY,EACV,IAAI,EACJ,UAAU,EACV,IAAI,GAC8D;QAClE,KAAK,CAAC,gBAAgB,IAAI,2CAA2C,EAAE;YACrE,YAAY,EAAE;gBACZ,YAAY,OAAO,CAAC,mBAAmB,CAAC,UAAkC,CAAC,GAAG;gBAC9E,WAAW,IAAI,KAAK,IAAI,SAAS;aAClC;SACF,CAAC,CAAA;QAXc;;;;mBAAO,qCAAqC;WAAA;IAY9D,CAAC;CACF;AAdD,sDAcC;AA2BD,MAAa,aAAc,SAAQ,MAAM,CAAC,SAAS;IAEjD;QACE,KAAK,CAAC,qDAAqD,CAAC,CAAA;QAF5C;;;;mBAAO,6BAA6B;WAAA;IAGtD,CAAC;CACF;AALD,sCAKC;AA4BD,MAAa,wBAAyB,SAAQ,MAAM,CAAC,SAAS;IAE5D,YAAY,EACV,cAAc,EACd,WAAW,EACX,IAAI,GAC0D;QAC9D,KAAK,CACH,oCAAoC,IAAI,mBAAmB,cAAc,gBAAgB,WAAW,KAAK,CAC1G,CAAA;QARe;;;;mBAAO,wCAAwC;WAAA;IASjE,CAAC;CACF;AAXD,4DAWC;AA4BD,MAAa,sBAAuB,SAAQ,MAAM,CAAC,SAAS;IAE1D,YAAY,EACV,YAAY,EACZ,KAAK,GACoC;QACzC,KAAK,CACH,kBAAkB,KAAK,WAAW,GAAG,CAAC,IAAI,CACxC,KAAK,CACN,wCAAwC,YAAY,IAAI,CAC1D,CAAA;QATe;;;;mBAAO,sCAAsC;WAAA;IAU/D,CAAC;CACF;AAZD,wDAYC;AAyBD,MAAa,mBAAoB,SAAQ,MAAM,CAAC,SAAS;IAEvD,YAAY,EACV,cAAc,EACd,WAAW,GACqC;QAChD,KAAK,CACH;YACE,iDAAiD;YACjD,iCAAiC,cAAc,EAAE;YACjD,0BAA0B,WAAW,EAAE;SACxC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAA;QAXe;;;;mBAAO,mCAAmC;WAAA;IAY5D,CAAC;CACF;AAdD,kDAcC;AAkBD,MAAa,iBAAkB,SAAQ,MAAM,CAAC,SAAS;IAErD,YAAY,KAAc;QACxB,KAAK,CAAC,WAAW,KAAK,0BAA0B,CAAC,CAAA;QAFjC;;;;mBAAO,iCAAiC;WAAA;IAG1D,CAAC;CACF;AALD,8CAKC;AAcD,MAAa,gBAAiB,SAAQ,MAAM,CAAC,SAAS;IAEpD,YAAY,IAAY;QACtB,KAAK,CAAC,UAAU,IAAI,6BAA6B,CAAC,CAAA;QAFlC;;;;mBAAO,gCAAgC;WAAA;IAGzD,CAAC;CACF;AALD,4CAKC"}