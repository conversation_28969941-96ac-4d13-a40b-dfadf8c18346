{"version": 3, "file": "BinaryStateTree.js", "sourceRoot": "", "sources": ["../../core/BinaryStateTree.ts"], "names": [], "mappings": ";;AAyBA,wBAIC;AAsBD,wBAoDC;AAuBD,8BAqBC;AAnJD,iDAA6C;AAE7C,oCAAmC;AAuBnC,SAAgB,MAAM;IACpB,OAAO;QACL,IAAI,EAAE,SAAS,EAAE;KAClB,CAAA;AACH,CAAC;AAsBD,SAAgB,MAAM,CACpB,IAAqB,EACrB,GAAgB,EAChB,KAAkB;IAElB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;IACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAE,CAAA;IAEzC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QAClC,OAAM;IACR,CAAC;IAED,SAAS,KAAK,CACZ,KAAW,EACX,IAAiB,EACjB,QAAgB,EAChB,KAAkB,EAClB,KAAa;QAEb,IAAI,IAAI,GAAG,KAAK,CAAA;QAEhB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;YACrB,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,GAAG,KAAK,CAAA;YAC9B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,GAAG,KAAK,CAAA;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,MAAM,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC/C,OAAO,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5E,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC3B,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;gBACd,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;YAChE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;YAClE,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,SAAS,EAAE,CAAA;IACpB,CAAC;IACD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;AACxD,CAAC;AAuBD,SAAgB,SAAS,CAAC,IAAqB;IAC7C,SAAS,KAAK,CAAC,IAAU;QACvB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;YAAE,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACjC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,MAAM,GAAG,EAAE,CAAA;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAE,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CAAA;YAC3D,KAAK,GAAG,MAAM,CAAA;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAA;IAC5E,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACzB,CAAC;AA0BD,SAAS,SAAS,CAChB,IAAU,EACV,QAAkB,EAClB,gBAA0B,EAC1B,QAAgB,EAChB,KAAkB,EAClB,KAAa;IAEb,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;QAChD,MAAM,QAAQ,GAAG,YAAY,EAAE,CAAA;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC3B,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,GAAG,SAAS,CACvB,IAAI,EACJ,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,KAAK,EACL,KAAK,GAAG,CAAC,CACV,CAAA;QACH,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,KAAK,GAAG,SAAS,CACxB,IAAI,EACJ,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,KAAK,EACL,KAAK,GAAG,CAAC,CACV,CAAA;QACH,CAAC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,MAAM,QAAQ,GAAG,YAAY,EAAE,CAAA;IAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC3B,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAA;IAClC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;QACd,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC9B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACtC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAA;IACvB,CAAC;SAAM,CAAC;QACN,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC/B,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACvC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAA;IACtB,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AAGD,SAAS,SAAS;IAChB,OAAO;QACL,IAAI,EAAE,OAAO;KACd,CAAA;AACH,CAAC;AAGD,SAAS,YAAY;IACnB,OAAO;QACL,IAAI,EAAE,SAAS,EAAE;QACjB,KAAK,EAAE,SAAS,EAAE;QAClB,IAAI,EAAE,UAAU;KACjB,CAAA;AACH,CAAC;AAGD,SAAS,QAAQ,CAAC,IAAiB;IACjC,OAAO;QACL,IAAI;QACJ,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;QACpD,IAAI,EAAE,MAAM;KACb,CAAA;AACH,CAAC;AAGD,SAAS,WAAW,CAAC,KAAkB;IACrC,MAAM,IAAI,GAAG,EAAE,CAAA;IACf,KAAK,MAAM,IAAI,IAAI,KAAK;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAC9D,OAAO,IAAI,CAAA;AACb,CAAC;AAGD,SAAS,WAAW,CAAC,IAAc;IACjC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAAE,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QAC3D,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;IACzB,CAAC;IACD,OAAO,SAAS,CAAA;AAClB,CAAC;AAGD,SAAS,IAAI,CAAC,KAA8B;IAC1C,IAAI,CAAC,KAAK;QAAE,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC;QAAE,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACxE,OAAO,IAAA,eAAM,EAAC,KAAK,CAAC,CAAA;AACtB,CAAC"}