"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.type = void 0;
exports.assert = assert;
exports.deserialize = deserialize;
exports.from = from;
exports.getSignPayload = getSignPayload;
exports.hash = hash;
exports.serialize = serialize;
exports.toRpc = toRpc;
exports.validate = validate;
const Address = require("./Address.js");
const Hash = require("./Hash.js");
const Hex = require("./Hex.js");
const Rlp = require("./Rlp.js");
const Signature = require("./Signature.js");
const TransactionEnvelope = require("./TransactionEnvelope.js");
exports.type = 'legacy';
function assert(envelope) {
    const { chainId, gasPrice, to } = envelope;
    if (to)
        Address.assert(to, { strict: false });
    if (typeof chainId !== 'undefined' && chainId <= 0)
        throw new TransactionEnvelope.InvalidChainIdError({ chainId });
    if (gasPrice && BigInt(gasPrice) > 2n ** 256n - 1n)
        throw new TransactionEnvelope.GasPriceTooHighError({ gasPrice });
}
function deserialize(serialized) {
    const tuple = Rlp.toHex(serialized);
    const [nonce, gasPrice, gas, to, value, data, chainIdOrV_, r, s] = tuple;
    if (!(tuple.length === 6 || tuple.length === 9))
        throw new TransactionEnvelope.InvalidSerializedError({
            attributes: {
                nonce,
                gasPrice,
                gas,
                to,
                value,
                data,
                ...(tuple.length > 6
                    ? {
                        v: chainIdOrV_,
                        r,
                        s,
                    }
                    : {}),
            },
            serialized,
            type: exports.type,
        });
    const transaction = {
        type: exports.type,
    };
    if (Hex.validate(to) && to !== '0x')
        transaction.to = to;
    if (Hex.validate(gas) && gas !== '0x')
        transaction.gas = BigInt(gas);
    if (Hex.validate(data) && data !== '0x')
        transaction.data = data;
    if (Hex.validate(nonce))
        transaction.nonce = nonce === '0x' ? 0n : BigInt(nonce);
    if (Hex.validate(value) && value !== '0x')
        transaction.value = BigInt(value);
    if (Hex.validate(gasPrice) && gasPrice !== '0x')
        transaction.gasPrice = BigInt(gasPrice);
    if (tuple.length === 6)
        return transaction;
    const chainIdOrV = Hex.validate(chainIdOrV_) && chainIdOrV_ !== '0x'
        ? Number(chainIdOrV_)
        : 0;
    if (s === '0x' && r === '0x') {
        if (chainIdOrV > 0)
            transaction.chainId = Number(chainIdOrV);
        return transaction;
    }
    const v = chainIdOrV;
    const chainId = Math.floor((v - 35) / 2);
    if (chainId > 0)
        transaction.chainId = chainId;
    else if (v !== 27 && v !== 28)
        throw new Signature.InvalidVError({ value: v });
    transaction.yParity = Signature.vToYParity(v);
    transaction.v = v;
    transaction.s = s === '0x' ? 0n : BigInt(s);
    transaction.r = r === '0x' ? 0n : BigInt(r);
    assert(transaction);
    return transaction;
}
function from(envelope, options = {}) {
    const { signature } = options;
    const envelope_ = (typeof envelope === 'string' ? deserialize(envelope) : envelope);
    assert(envelope_);
    const signature_ = (() => {
        if (!signature)
            return {};
        const s = Signature.from(signature);
        s.v = Signature.yParityToV(s.yParity);
        return s;
    })();
    return {
        ...envelope_,
        ...signature_,
        type: 'legacy',
    };
}
function getSignPayload(envelope) {
    return hash(envelope, { presign: true });
}
function hash(envelope, options = {}) {
    const { presign } = options;
    return Hash.keccak256(serialize({
        ...envelope,
        ...(presign
            ? {
                r: undefined,
                s: undefined,
                yParity: undefined,
                v: undefined,
            }
            : {}),
    }));
}
function serialize(envelope, options = {}) {
    const { chainId = 0, gas, data, input, nonce, to, value, gasPrice } = envelope;
    assert(envelope);
    let serialized = [
        nonce ? Hex.fromNumber(nonce) : '0x',
        gasPrice ? Hex.fromNumber(gasPrice) : '0x',
        gas ? Hex.fromNumber(gas) : '0x',
        to ?? '0x',
        value ? Hex.fromNumber(value) : '0x',
        data ?? input ?? '0x',
    ];
    const signature = (() => {
        if (options.signature)
            return {
                r: options.signature.r,
                s: options.signature.s,
                v: Signature.yParityToV(options.signature.yParity),
            };
        if (typeof envelope.r === 'undefined' || typeof envelope.s === 'undefined')
            return undefined;
        return {
            r: envelope.r,
            s: envelope.s,
            v: envelope.v,
        };
    })();
    if (signature) {
        const v = (() => {
            if (signature.v >= 35) {
                const inferredChainId = Math.floor((signature.v - 35) / 2);
                if (inferredChainId > 0)
                    return signature.v;
                return 27 + (signature.v === 35 ? 0 : 1);
            }
            if (chainId > 0)
                return chainId * 2 + 35 + signature.v - 27;
            const v = 27 + (signature.v === 27 ? 0 : 1);
            if (signature.v !== v)
                throw new Signature.InvalidVError({ value: signature.v });
            return v;
        })();
        serialized = [
            ...serialized,
            Hex.fromNumber(v),
            signature.r === 0n ? '0x' : Hex.trimLeft(Hex.fromNumber(signature.r)),
            signature.s === 0n ? '0x' : Hex.trimLeft(Hex.fromNumber(signature.s)),
        ];
    }
    else if (chainId > 0)
        serialized = [...serialized, Hex.fromNumber(chainId), '0x', '0x'];
    return Rlp.fromHex(serialized);
}
function toRpc(envelope) {
    const signature = Signature.extract(envelope);
    return {
        ...envelope,
        chainId: typeof envelope.chainId === 'number'
            ? Hex.fromNumber(envelope.chainId)
            : undefined,
        data: envelope.data ?? envelope.input,
        type: '0x0',
        ...(typeof envelope.gas === 'bigint'
            ? { gas: Hex.fromNumber(envelope.gas) }
            : {}),
        ...(typeof envelope.nonce === 'bigint'
            ? { nonce: Hex.fromNumber(envelope.nonce) }
            : {}),
        ...(typeof envelope.value === 'bigint'
            ? { value: Hex.fromNumber(envelope.value) }
            : {}),
        ...(typeof envelope.gasPrice === 'bigint'
            ? { gasPrice: Hex.fromNumber(envelope.gasPrice) }
            : {}),
        ...(signature
            ? {
                ...Signature.toRpc(signature),
                v: signature.yParity === 0 ? '0x1b' : '0x1c',
            }
            : {}),
    };
}
function validate(envelope) {
    try {
        assert(envelope);
        return true;
    }
    catch {
        return false;
    }
}
//# sourceMappingURL=TransactionEnvelopeLegacy.js.map