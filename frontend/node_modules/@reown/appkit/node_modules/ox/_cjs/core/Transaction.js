"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromRpcType = exports.toRpcType = void 0;
exports.fromRpc = fromRpc;
exports.toRpc = toRpc;
const Authorization = require("./Authorization.js");
const Hex = require("./Hex.js");
const Signature = require("./Signature.js");
exports.toRpcType = {
    legacy: '0x0',
    eip2930: '0x1',
    eip1559: '0x2',
    eip4844: '0x3',
    eip7702: '0x4',
};
exports.fromRpcType = {
    '0x0': 'legacy',
    '0x1': 'eip2930',
    '0x2': 'eip1559',
    '0x3': 'eip4844',
    '0x4': 'eip7702',
};
function fromRpc(transaction, _options = {}) {
    if (!transaction)
        return null;
    const signature = Signature.extract(transaction);
    const transaction_ = {
        ...transaction,
        ...signature,
    };
    transaction_.blockNumber = transaction.blockNumber
        ? BigInt(transaction.blockNumber)
        : null;
    transaction_.data = transaction.input;
    transaction_.gas = BigInt(transaction.gas ?? 0n);
    transaction_.nonce = BigInt(transaction.nonce ?? 0n);
    transaction_.transactionIndex = transaction.transactionIndex
        ? Number(transaction.transactionIndex)
        : null;
    transaction_.value = BigInt(transaction.value ?? 0n);
    if (transaction.authorizationList)
        transaction_.authorizationList = Authorization.fromRpcList(transaction.authorizationList);
    if (transaction.chainId)
        transaction_.chainId = Number(transaction.chainId);
    if (transaction.gasPrice)
        transaction_.gasPrice = BigInt(transaction.gasPrice);
    if (transaction.maxFeePerBlobGas)
        transaction_.maxFeePerBlobGas = BigInt(transaction.maxFeePerBlobGas);
    if (transaction.maxFeePerGas)
        transaction_.maxFeePerGas = BigInt(transaction.maxFeePerGas);
    if (transaction.maxPriorityFeePerGas)
        transaction_.maxPriorityFeePerGas = BigInt(transaction.maxPriorityFeePerGas);
    if (transaction.type)
        transaction_.type =
            exports.fromRpcType[transaction.type] ?? transaction.type;
    if (signature)
        transaction_.v = Signature.yParityToV(signature.yParity);
    return transaction_;
}
function toRpc(transaction, _options) {
    const rpc = {};
    rpc.blockHash = transaction.blockHash;
    rpc.blockNumber =
        typeof transaction.blockNumber === 'bigint'
            ? Hex.fromNumber(transaction.blockNumber)
            : null;
    rpc.from = transaction.from;
    rpc.gas = Hex.fromNumber(transaction.gas ?? 0n);
    rpc.hash = transaction.hash;
    rpc.input = transaction.input;
    rpc.nonce = Hex.fromNumber(transaction.nonce ?? 0n);
    rpc.to = transaction.to;
    rpc.transactionIndex = transaction.transactionIndex
        ? Hex.fromNumber(transaction.transactionIndex)
        : null;
    rpc.type = exports.toRpcType[transaction.type] ?? transaction.type;
    rpc.value = Hex.fromNumber(transaction.value ?? 0n);
    if (transaction.accessList)
        rpc.accessList = transaction.accessList;
    if (transaction.authorizationList)
        rpc.authorizationList = Authorization.toRpcList(transaction.authorizationList);
    if (transaction.blobVersionedHashes)
        rpc.blobVersionedHashes = transaction.blobVersionedHashes;
    if (transaction.chainId)
        rpc.chainId = Hex.fromNumber(transaction.chainId);
    if (typeof transaction.gasPrice === 'bigint')
        rpc.gasPrice = Hex.fromNumber(transaction.gasPrice);
    if (typeof transaction.maxFeePerBlobGas === 'bigint')
        rpc.maxFeePerBlobGas = Hex.fromNumber(transaction.maxFeePerBlobGas);
    if (typeof transaction.maxFeePerGas === 'bigint')
        rpc.maxFeePerGas = Hex.fromNumber(transaction.maxFeePerGas);
    if (typeof transaction.maxPriorityFeePerGas === 'bigint')
        rpc.maxPriorityFeePerGas = Hex.fromNumber(transaction.maxPriorityFeePerGas);
    if (typeof transaction.r === 'bigint')
        rpc.r = Hex.fromNumber(transaction.r, { size: 32 });
    if (typeof transaction.s === 'bigint')
        rpc.s = Hex.fromNumber(transaction.s, { size: 32 });
    if (typeof transaction.v === 'number')
        rpc.v = Hex.fromNumber(transaction.v, { size: 1 });
    if (typeof transaction.yParity === 'number')
        rpc.yParity = transaction.yParity === 0 ? '0x0' : '0x1';
    return rpc;
}
//# sourceMappingURL=Transaction.js.map