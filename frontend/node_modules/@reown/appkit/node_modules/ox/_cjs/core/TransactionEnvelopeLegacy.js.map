{"version": 3, "file": "TransactionEnvelopeLegacy.js", "sourceRoot": "", "sources": ["../../core/TransactionEnvelopeLegacy.ts"], "names": [], "mappings": ";;;AAgEA,wBAOC;AA+BD,kCAkEC;AA8ED,oBAgCC;AAgED,wCAIC;AAsCD,oBAkBC;AAkED,8BA+DC;AA4CD,sBA8BC;AAwBD,4BASC;AA9nBD,wCAAuC;AAEvC,kCAAiC;AACjC,gCAA+B;AAC/B,gCAA+B;AAC/B,4CAA2C;AAC3C,gEAA+D;AAmClD,QAAA,IAAI,GAAG,QAAQ,CAAA;AAuB5B,SAAgB,MAAM,CAAC,QAAsD;IAC3E,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA;IAC1C,IAAI,EAAE;QAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;IAC7C,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC;QAChD,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAChE,IAAI,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE;QAChD,MAAM,IAAI,mBAAmB,CAAC,oBAAoB,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAA;AACpE,CAAC;AA+BD,SAAgB,WAAW,CACzB,UAAmB;IAEnB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;IAEnC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,GAC9D,KAA2B,CAAA;IAE7B,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;QAC7C,MAAM,IAAI,mBAAmB,CAAC,sBAAsB,CAAC;YACnD,UAAU,EAAE;gBACV,KAAK;gBACL,QAAQ;gBACR,GAAG;gBACH,EAAE;gBACF,KAAK;gBACL,IAAI;gBACJ,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;oBAClB,CAAC,CAAC;wBACE,CAAC,EAAE,WAAW;wBACd,CAAC;wBACD,CAAC;qBACF;oBACH,CAAC,CAAC,EAAE,CAAC;aACR;YACD,UAAU;YACV,IAAI,EAAJ,YAAI;SACL,CAAC,CAAA;IAEJ,MAAM,WAAW,GAAG;QAClB,IAAI,EAAJ,YAAI;KACwB,CAAA;IAC9B,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI;QAAE,WAAW,CAAC,EAAE,GAAG,EAAE,CAAA;IACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI;QAAE,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;IACpE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;QAAE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAA;IAChE,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrB,WAAW,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACzD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI;QAAE,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;IAC5E,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,KAAK,IAAI;QAC7C,WAAW,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;IAEzC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,WAAW,CAAA;IAE1C,MAAM,UAAU,GACd,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,KAAK,IAAI;QAC/C,CAAC,CAAC,MAAM,CAAC,WAAsB,CAAC;QAChC,CAAC,CAAC,CAAC,CAAA;IAEP,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QAC7B,IAAI,UAAU,GAAG,CAAC;YAAE,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;QAC5D,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,MAAM,CAAC,GAAG,UAAU,CAAA;IACpB,MAAM,OAAO,GAAuB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;IAC5D,IAAI,OAAO,GAAG,CAAC;QAAE,WAAW,CAAC,OAAO,GAAG,OAAO,CAAA;SACzC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;QAAE,MAAM,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAA;IAE9E,WAAW,CAAC,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IAC7C,WAAW,CAAC,CAAC,GAAG,CAAC,CAAA;IACjB,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA;IAC5C,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA;IAE5C,MAAM,CAAC,WAAW,CAAC,CAAA;IAEnB,OAAO,WAAW,CAAA;AACpB,CAAC;AA8ED,SAAgB,IAAI,CAMlB,QAGW,EACX,UAAmC,EAAE;IAErC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAA;IAE7B,MAAM,SAAS,GAAG,CAChB,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CACnC,CAAA;IAE9B,MAAM,CAAC,SAAS,CAAC,CAAA;IAEjB,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE;QACvB,IAAI,CAAC,SAAS;YAAE,OAAO,EAAE,CAAA;QACzB,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAQ,CAAA;QAC1C,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACrC,OAAO,CAAC,CAAA;IACV,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,GAAG,SAAS;QACZ,GAAG,UAAU;QACb,IAAI,EAAE,QAAQ;KACN,CAAA;AACZ,CAAC;AAgED,SAAgB,cAAc,CAC5B,QAA0C;IAE1C,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;AAC1C,CAAC;AAsCD,SAAgB,IAAI,CAClB,QAAwE,EACxE,UAAiC,EAAE;IAEnC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;IAC3B,OAAO,IAAI,CAAC,SAAS,CACnB,SAAS,CAAC;QACR,GAAG,QAAQ;QACX,GAAG,CAAC,OAAO;YACT,CAAC,CAAC;gBACE,CAAC,EAAE,SAAS;gBACZ,CAAC,EAAE,SAAS;gBACZ,OAAO,EAAE,SAAS;gBAClB,CAAC,EAAE,SAAS;aACb;YACH,CAAC,CAAC,EAAE,CAAC;KACR,CAAC,CACH,CAAA;AACH,CAAC;AAkED,SAAgB,SAAS,CACvB,QAAsD,EACtD,UAA6B,EAAE;IAE/B,MAAM,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAA;IAE9E,MAAM,CAAC,QAAQ,CAAC,CAAA;IAEhB,IAAI,UAAU,GAAG;QACf,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;QAC1C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QAChC,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,IAAI,IAAI,KAAK,IAAI,IAAI;KACtB,CAAA;IAED,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;QACtB,IAAI,OAAO,CAAC,SAAS;YACnB,OAAO;gBACL,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBACtB,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBACtB,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;aACnD,CAAA;QAEH,IAAI,OAAO,QAAQ,CAAC,CAAC,KAAK,WAAW,IAAI,OAAO,QAAQ,CAAC,CAAC,KAAK,WAAW;YACxE,OAAO,SAAS,CAAA;QAClB,OAAO;YACL,CAAC,EAAE,QAAQ,CAAC,CAAC;YACb,CAAC,EAAE,QAAQ,CAAC,CAAC;YACb,CAAC,EAAE,QAAQ,CAAC,CAAE;SACf,CAAA;IACH,CAAC,CAAC,EAAE,CAAA;IAEJ,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;YAEd,IAAI,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC1D,IAAI,eAAe,GAAG,CAAC;oBAAE,OAAO,SAAS,CAAC,CAAC,CAAA;gBAC3C,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1C,CAAC;YAGD,IAAI,OAAO,GAAG,CAAC;gBAAE,OAAO,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,EAAE,CAAA;YAG3D,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC3C,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC;gBACnB,MAAM,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAA;YAC3D,OAAO,CAAC,CAAA;QACV,CAAC,CAAC,EAAE,CAAA;QAEJ,UAAU,GAAG;YACX,GAAG,UAAU;YACb,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;YACjB,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACrE,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACtE,CAAA;IACH,CAAC;SAAM,IAAI,OAAO,GAAG,CAAC;QACpB,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAEnE,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,CAAU,CAAA;AACzC,CAAC;AA4CD,SAAgB,KAAK,CAAC,QAAiD;IACrE,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAE,CAAA;IAE9C,OAAO;QACL,GAAG,QAAQ;QACX,OAAO,EACL,OAAO,QAAQ,CAAC,OAAO,KAAK,QAAQ;YAClC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;YAClC,CAAC,CAAC,SAAS;QACf,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;QACrC,IAAI,EAAE,KAAK;QACX,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG,KAAK,QAAQ;YAClC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvC,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,QAAQ,KAAK,QAAQ;YACvC,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACjD,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,SAAS;YACX,CAAC,CAAC;gBACE,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC7B,CAAC,EAAE,SAAS,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;aAC7C;YACH,CAAC,CAAC,EAAE,CAAC;KACC,CAAA;AACZ,CAAC;AAwBD,SAAgB,QAAQ,CACtB,QAAsD;IAEtD,IAAI,CAAC;QACH,MAAM,CAAC,QAAQ,CAAC,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC"}