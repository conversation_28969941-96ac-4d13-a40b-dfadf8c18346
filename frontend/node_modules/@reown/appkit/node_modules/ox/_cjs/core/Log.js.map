{"version": 3, "file": "Log.js", "sourceRoot": "", "sources": ["../../core/Log.ts"], "names": [], "mappings": ";;AAyIA,0BAeC;AAyDD,sBAsBC;AArOD,gCAA+B;AAuI/B,SAAgB,OAAO,CAIrB,GAAuB,EACvB,WAAqC,EAAE;IAEvC,OAAO;QACL,GAAG,GAAG;QACN,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI;QAC7D,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;QACpD,gBAAgB,EAAE,GAAG,CAAC,gBAAgB;YACpC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAC9B,CAAC,CAAC,IAAI;KACO,CAAA;AACnB,CAAC;AAyDD,SAAgB,KAAK,CAGnB,GAAQ,EAAE,WAAmC,EAAE;IAC/C,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,WAAW,EACT,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ;YACjC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;YACjC,CAAC,CAAC,IAAI;QACV,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,QAAQ,EACN,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;QACxE,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,eAAe,EAAE,GAAG,CAAC,eAAe;QACpC,gBAAgB,EACd,OAAO,GAAG,CAAC,gBAAgB,KAAK,QAAQ;YACtC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtC,CAAC,CAAC,IAAI;QACV,OAAO,EAAE,GAAG,CAAC,OAAO;KACL,CAAA;AACnB,CAAC"}