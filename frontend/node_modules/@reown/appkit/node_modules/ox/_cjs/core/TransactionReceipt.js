"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toRpcType = exports.fromRpcType = exports.toRpcStatus = exports.fromRpcStatus = void 0;
exports.fromRpc = fromRpc;
exports.toRpc = toRpc;
const Hex = require("./Hex.js");
const Log = require("./Log.js");
exports.fromRpcStatus = {
    '0x0': 'reverted',
    '0x1': 'success',
};
exports.toRpcStatus = {
    reverted: '0x0',
    success: '0x1',
};
exports.fromRpcType = {
    '0x0': 'legacy',
    '0x1': 'eip2930',
    '0x2': 'eip1559',
    '0x3': 'eip4844',
    '0x4': 'eip7702',
};
exports.toRpcType = {
    legacy: '0x0',
    eip2930: '0x1',
    eip1559: '0x2',
    eip4844: '0x3',
    eip7702: '0x4',
};
function fromRpc(receipt) {
    if (!receipt)
        return null;
    return {
        ...receipt,
        blobGasPrice: receipt.blobGasPrice
            ? BigInt(receipt.blobGasPrice)
            : undefined,
        blobGasUsed: receipt.blobGasUsed ? BigInt(receipt.blobGasUsed) : undefined,
        blockNumber: BigInt(receipt.blockNumber ?? 0n),
        cumulativeGasUsed: BigInt(receipt.cumulativeGasUsed ?? 0n),
        effectiveGasPrice: BigInt(receipt.effectiveGasPrice ?? 0n),
        gasUsed: BigInt(receipt.gasUsed ?? 0n),
        logs: receipt.logs.map((log) => Log.fromRpc(log, { pending: false })),
        status: exports.fromRpcStatus[receipt.status],
        transactionIndex: Number(receipt.transactionIndex ?? 0),
        type: exports.fromRpcType[receipt.type] || receipt.type,
    };
}
function toRpc(receipt) {
    return {
        blobGasPrice: receipt.blobGasPrice
            ? Hex.fromNumber(receipt.blobGasPrice)
            : undefined,
        blobGasUsed: receipt.blobGasUsed
            ? Hex.fromNumber(receipt.blobGasUsed)
            : undefined,
        blockHash: receipt.blockHash,
        blockNumber: Hex.fromNumber(receipt.blockNumber),
        contractAddress: receipt.contractAddress,
        cumulativeGasUsed: Hex.fromNumber(receipt.cumulativeGasUsed),
        effectiveGasPrice: Hex.fromNumber(receipt.effectiveGasPrice),
        from: receipt.from,
        gasUsed: Hex.fromNumber(receipt.gasUsed),
        logs: receipt.logs.map(Log.toRpc),
        logsBloom: receipt.logsBloom,
        root: receipt.root,
        status: exports.toRpcStatus[receipt.status],
        to: receipt.to,
        transactionHash: receipt.transactionHash,
        transactionIndex: Hex.fromNumber(receipt.transactionIndex),
        type: exports.toRpcType[receipt.type] ?? receipt.type,
    };
}
//# sourceMappingURL=TransactionReceipt.js.map