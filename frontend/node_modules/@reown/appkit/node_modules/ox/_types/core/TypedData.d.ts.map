{"version": 3, "file": "TypedData.d.ts", "sourceRoot": "", "sources": ["../../core/TypedData.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,OAAO,MAAM,SAAS,CAAA;AACvC,OAAO,KAAK,aAAa,MAAM,oBAAoB,CAAA;AACnD,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AAEjC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAElD,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;AACzC,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,CAAA;AAC5C,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAA;AAGlD,MAAM,MAAM,UAAU,CACpB,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,EAEtE,YAAY,GAAG,SAAS,SAAS,SAAS,GAAG,MAAM,SAAS,GAAG,MAAM,IACnE,WAAW,SAAS,cAAc,GAClC,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,GAC9C,iBAAiB,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,CAAA;AAE3D,MAAM,MAAM,sBAAsB,CAChC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,cAAc,GAAG,cAAc,EAEnD,MAAM,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,SAAS,SAAS,GAChE,OAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,GAC5C,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IACzB;IACF,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;CAC9B,GAAG;IACF,WAAW,EACP,cAAc,GACd,CAAC,WAAW,SAAS,cAAc,GAAG,WAAW,GAAG,KAAK,CAAC,CAAA;IAC9D,MAAM,EAAE,MAAM,SAAS;QAAE,YAAY,EAAE,MAAM,MAAM,CAAA;KAAE,GACjD,MAAM,GACN,OAAO,CAAC,MAAM,CAAC,CAAA;IACnB,OAAO,CAAC,EAAE,SAAS,CAAA;CACpB,CAAA;AAED,MAAM,MAAM,iBAAiB,CAC3B,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,MAAM,SAAS,EAErD,YAAY,GAAG,SAAS,SAAS,SAAS,GAAG,MAAM,SAAS,GAAG,MAAM,EACrE,MAAM,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,SAAS,SAAS,GAChE,OAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,GAC5C,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3B,OAAO,GAAG,MAAM,CAAC,WAAW,SAAS,MAAM,MAAM,GAC7C,WAAW,GACX,MAAM,MAAM,CAAC,IACf;IACF,KAAK,EAAE,SAAS,CAAA;CACjB,GAAG;IACF,WAAW,EACP,YAAY,GACZ,CAAC,WAAW,SAAS,YAAY,GAAG,WAAW,GAAG,KAAK,CAAC,CAAA;IAC5D,MAAM,CAAC,EACH,CAAC,MAAM,SAAS;QAAE,YAAY,EAAE,MAAM,MAAM,CAAA;KAAE,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAC1E,SAAS,CAAA;IACb,OAAO,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,SAAS,OAAO,GACzC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACvB,OAAO,CAAA;CACZ,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,wBAAgB,MAAM,CACpB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,IAAI,CAiEnD;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,KAAK,CACR,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,IACpE,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IAEtC,KAAK,SAAS,GACV,OAAO,CAAC,mBAAmB,GAC3B,sBAAsB,GACtB,uBAAuB,GACvB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,IAAI,CAAC,SAAS,GAClB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAIvD;AAED,MAAM,CAAC,OAAO,WAAW,eAAe,CAAC;IACvC,KAAK,SAAS,GAAG,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/D;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,wBAAgB,MAAM,CACpB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,CAoCtD;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,KAAK,CACR,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,IACpE,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IAEtC,KAAK,SAAS,GACV,wBAAwB,CAAC,SAAS,GAClC,UAAU,CAAC,SAAS,GACpB,UAAU,CAAC,SAAS,GACpB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,MAAM,CAe1D;AAED,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,KAAK,KAAK,GAAG;QACX,WAAW,EAAE,MAAM,CAAA;QACnB,KAAK,EAAE,SAAS,CAAA;KACjB,CAAA;IAED,KAAK,SAAS,GAAG,oBAAoB,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACzE;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,wBAAwB,CACtC,MAAM,EAAE,MAAM,GAAG,SAAS,GACzB,SAAS,EAAE,CAcb;AAED,MAAM,CAAC,OAAO,WAAW,wBAAwB,CAAC;IAChD,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,wBAAgB,cAAc,CAC5B,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,CAEtD;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,SAAS,GACV,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAU3D;AAED,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,KAAK,KAAK,GAAG;QACX,6BAA6B;QAC7B,MAAM,EAAE,MAAM,CAAA;QACd,4BAA4B;QAC5B,KAAK,CAAC,EACF;YACE,YAAY,CAAC,EAAE,SAAS,SAAS,EAAE,GAAG,SAAS,CAAA;YAC/C,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,SAAS,EAAE,GAAG,SAAS,CAAA;SAChD,GACD,SAAS,CAAA;KACd,CAAA;IAED,KAAK,SAAS,GAAG,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/D;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAQ3D;AAED,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,KAAK,KAAK,GAAG;QACX,qCAAqC;QACrC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC7B,iDAAiD;QACjD,WAAW,EAAE,MAAM,CAAA;QACnB,0CAA0C;QAC1C,KAAK,EAAE,SAAS,CAAA;KACjB,CAAA;IAED,KAAK,SAAS,GACV,UAAU,CAAC,SAAS,GACpB,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,wBAAgB,SAAS,CACvB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,MAAM,CAoCxD;AAED,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC,KAAK,KAAK,CACR,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,IACpE,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IAEtC,KAAK,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACnE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,wBAAgB,QAAQ,CACtB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,OAAO,CAOtD;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC3D;AAED,yFAAyF;AACzF,qBAAa,sBAAuB,SAAQ,MAAM,CAAC,SAAS;IAC1D,SAAkB,IAAI,sCAAqC;gBAE/C,EACV,YAAY,EACZ,SAAS,GACV,EAAE;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAA;KAAE;CAG/C;AAED,yCAAyC;AACzC,qBAAa,kBAAmB,SAAQ,MAAM,CAAC,SAAS;IACtD,SAAkB,IAAI,kCAAiC;gBAE3C,EAAE,MAAM,EAAE,EAAE;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE;CAK5C;AAED,qEAAqE;AACrE,qBAAa,uBAAwB,SAAQ,MAAM,CAAC,SAAS;IAC3D,SAAkB,IAAI,uCAAsC;gBAEhD,EACV,WAAW,EACX,KAAK,GACN,EAAE;QAAE,WAAW,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAAE;CAQvE;AAED,uDAAuD;AACvD,qBAAa,sBAAuB,SAAQ,MAAM,CAAC,SAAS;IAC1D,SAAkB,IAAI,sCAAqC;gBAE/C,EAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE;CAKvC;AAED,gBAAgB;AAChB,wBAAgB,UAAU,CAAC,KAAK,EAAE;IAChC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC7B,WAAW,EAAE,MAAM,CAAA;IACnB,KAAK,EAAE,SAAS,CAAA;CACjB,GAAG,GAAG,CAAC,GAAG,CAiBV;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,KAAK,SAAS,GACV,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,WAAW,CAAC,SAAS,GACrB,QAAQ,CAAC,SAAS,GAClB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,QAAQ,CAAC,KAAK,EAAE;IAC9B,WAAW,EAAE,MAAM,CAAA;IACnB,KAAK,EAAE,SAAS,CAAA;CACjB,GAAG,GAAG,CAAC,GAAG,CAIV;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAK,SAAS,GACV,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,UAAU,CAAC,SAAS,GACpB,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CAAC,UAAU,EAAE;IACtC,KAAK,EAAE,SAAS,CAAA;IAChB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,GAAG,CAAA;CACX,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CA4ClD;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GACV,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,KAAK,CAAC,UAAU,CAAC,SAAS,GAC1B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,oBAAoB,CAClC,KAAK,EAAE;IACL,WAAW,EAAE,MAAM,CAAA;IACnB,KAAK,EAAE,SAAS,CAAA;CACjB,EACD,OAAO,GAAE,GAAG,CAAC,MAAM,CAAa,GAC/B,GAAG,CAAC,MAAM,CAAC,CAYb;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,oBAAoB,CAAC;IAC5C,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC"}