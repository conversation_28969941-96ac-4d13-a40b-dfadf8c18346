{"version": 3, "file": "TransactionEnvelopeLegacy.d.ts", "sourceRoot": "", "sources": ["../../core/TransactionEnvelopeLegacy.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAA;AAC/D,OAAO,KAAK,EACV,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,cAAc,EACf,MAAM,qBAAqB,CAAA;AAE5B,MAAM,MAAM,yBAAyB,CACnC,MAAM,SAAS,OAAO,GAAG,OAAO,EAChC,UAAU,GAAG,MAAM,EACnB,UAAU,GAAG,MAAM,EACnB,IAAI,SAAS,MAAM,GAAG,IAAI,IACxB,OAAO,CACT,SAAS,CACP,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,EAC9D,SAAS,CACV,GAAG;IACF,wBAAwB;IACxB,QAAQ,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;CAClC,CACF,CAAA;AAED,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,OAAO,GAAG,OAAO,IAAI,yBAAyB,CAC3E,MAAM,EACN,GAAG,CAAC,GAAG,EACP,GAAG,CAAC,GAAG,EACP,KAAK,CACN,CAAA;AAED,MAAM,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAA;AAEzD,MAAM,MAAM,MAAM,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAA;AAEpD,eAAO,MAAM,IAAI,WAAW,CAAA;AAC5B,MAAM,MAAM,IAAI,GAAG,OAAO,IAAI,CAAA;AAE9B;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAO5E;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,SAAS,GACV,OAAO,CAAC,MAAM,CAAC,SAAS,GACxB,mBAAmB,CAAC,mBAAmB,GACvC,mBAAmB,CAAC,oBAAoB,GACxC,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,WAAW,CACzB,UAAU,EAAE,GAAG,CAAC,GAAG,GAClB,OAAO,CAAC,yBAAyB,CAAC,CAgEpC;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuEG;AACH,wBAAgB,IAAI,CAClB,KAAK,CAAC,QAAQ,SACV,cAAc,CAAC,yBAAyB,EAAE,MAAM,CAAC,GACjD,GAAG,CAAC,GAAG,EACX,KAAK,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAEnE,QAAQ,EACJ,QAAQ,GACR,cAAc,CAAC,yBAAyB,EAAE,MAAM,CAAC,GACjD,GAAG,CAAC,GAAG,EACX,OAAO,GAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAM,GACpC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAqBtC;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,IACxE;QACE,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;KACxD,CAAA;IAEH,KAAK,UAAU,CACb,QAAQ,SACJ,cAAc,CAAC,yBAAyB,EAAE,MAAM,CAAC,GACjD,GAAG,CAAC,GAAG,GAAG,yBAAyB,GAAG,GAAG,CAAC,GAAG,EACjD,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,IAC3D,OAAO,CACT,QAAQ,SAAS,GAAG,CAAC,GAAG,GACpB,yBAAyB,GACzB,MAAM,CACJ,QAAQ,EACR,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAClC,QAAQ,CACN,SAAS,GAAG;QACV,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,CAAA;KAC5C,CACF,GACD,EAAE,CAAC,GAAG;QACR,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;KACxB,CACF,CACN,CAAA;IAED,KAAK,SAAS,GACV,WAAW,CAAC,SAAS,GACrB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,cAAc,CAC5B,QAAQ,EAAE,yBAAyB,CAAC,KAAK,CAAC,GACzC,cAAc,CAAC,UAAU,CAE3B;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG,CAAA;IAEzB,KAAK,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACzD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,wBAAgB,IAAI,CAAC,OAAO,SAAS,OAAO,GAAG,KAAK,EAClD,QAAQ,EAAE,yBAAyB,CAAC,OAAO,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,EACxE,OAAO,GAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAM,GAClC,IAAI,CAAC,UAAU,CAejB;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,CAAC,OAAO,SAAS,OAAO,GAAG,KAAK,IAAI;QAC9C,mEAAmE;QACnE,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,SAAS,CAAA;KACxC,CAAA;IAED,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG,CAAA;IAEzB,KAAK,SAAS,GACV,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,SAAS,CAAC,SAAS,GACnB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiDG;AACH,wBAAgB,SAAS,CACvB,QAAQ,EAAE,SAAS,CAAC,yBAAyB,EAAE,MAAM,CAAC,EACtD,OAAO,GAAE,SAAS,CAAC,OAAY,GAC9B,UAAU,CA4DZ;AAED,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC,KAAK,OAAO,GAAG;QACb,kEAAkE;QAClE,SAAS,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;KAC5C,CAAA;IAED,KAAK,SAAS,GACV,MAAM,CAAC,SAAS,GAChB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,QAAQ,CAAC,SAAS,GACtB,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,SAAS,CAAC,aAAa,GACvB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,EAAE,MAAM,CAAC,GAAG,GAAG,CA8B5E;AAED,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,KAAY,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC7E;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,QAAQ,CACtB,QAAQ,EAAE,SAAS,CAAC,yBAAyB,EAAE,MAAM,CAAC,WAQvD;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC"}