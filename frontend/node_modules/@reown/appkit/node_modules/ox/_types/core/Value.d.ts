import * as Errors from './Errors.js';
/** @see https://ethereum.github.io/yellowpaper/paper.pdf */
export declare const exponents: {
    readonly wei: 0;
    readonly gwei: 9;
    readonly szabo: 12;
    readonly finney: 15;
    readonly ether: 18;
};
/**
 * Formats a `bigint` Value to its string representation (divided by the given exponent).
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.format(420_000_000_000n, 9)
 * // @log: '420'
 * ```
 *
 * @param value - The `bigint` Value to format.
 * @param decimals - The exponent to divide the `bigint` Value by.
 * @returns The string representation of the Value.
 */
export declare function format(value: bigint, decimals?: number): string;
export declare namespace format {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Formats a `bigint` Value (default: wei) to a string representation of Ether.
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.formatEther(1_000_000_000_000_000_000n)
 * // @log: '1'
 * ```
 *
 * @param wei - The Value to format.
 * @param unit - The unit to format the Value in. @default 'wei'.
 * @returns The Ether string representation of the Value.
 */
export declare function formatEther(wei: bigint, unit?: 'wei' | 'gwei' | 'szabo' | 'finney'): string;
export declare namespace formatEther {
    type ErrorType = format.ErrorType | Errors.GlobalErrorType;
}
/**
 * Formats a `bigint` Value (default: wei) to a string representation of Gwei.
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.formatGwei(1_000_000_000n)
 * // @log: '1'
 * ```
 *
 * @param wei - The Value to format.
 * @param unit - The unit to format the Value in. @default 'wei'.
 * @returns The Gwei string representation of the Value.
 */
export declare function formatGwei(wei: bigint, unit?: 'wei'): string;
export declare namespace formatGwei {
    type ErrorType = format.ErrorType | Errors.GlobalErrorType;
}
/**
 * Parses a `string` representation of a Value to `bigint` (multiplied by the given exponent).
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.from('420', 9)
 * // @log: 420000000000n
 * ```
 *
 * @param value - The string representation of the Value.
 * @param decimals - The exponent to multiply the Value by.
 * @returns The `bigint` representation of the Value.
 */
export declare function from(value: string, decimals?: number): bigint;
export declare namespace from {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Parses a string representation of Ether to a `bigint` Value (default: wei).
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.fromEther('420')
 * // @log: 420000000000000000000n
 * ```
 *
 * @param ether - String representation of Ether.
 * @param unit - The unit to parse to. @default 'wei'.
 * @returns A `bigint` Value.
 */
export declare function fromEther(ether: string, unit?: 'wei' | 'gwei' | 'szabo' | 'finney'): bigint;
export declare namespace fromEther {
    type ErrorType = from.ErrorType | Errors.GlobalErrorType;
}
/**
 * Parses a string representation of Gwei to a `bigint` Value (default: wei).
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.fromGwei('420')
 * // @log: 420000000000n
 * ```
 *
 * @param gwei - String representation of Gwei.
 * @param unit - The unit to parse to. @default 'wei'.
 * @returns A `bigint` Value.
 */
export declare function fromGwei(gwei: string, unit?: 'wei'): bigint;
export declare namespace fromGwei {
    type ErrorType = from.ErrorType | Errors.GlobalErrorType;
}
/**
 * Thrown when a value is not a valid decimal number.
 *
 * @example
 * ```ts twoslash
 * import { Value } from 'ox'
 *
 * Value.fromEther('123.456.789')
 * // @error: Value.InvalidDecimalNumberError: Value `123.456.789` is not a valid decimal number.
 * ```
 */
export declare class InvalidDecimalNumberError extends Errors.BaseError {
    readonly name = "Value.InvalidDecimalNumberError";
    constructor({ value }: {
        value: string;
    });
}
//# sourceMappingURL=Value.d.ts.map