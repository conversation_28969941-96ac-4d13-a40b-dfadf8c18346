{"version": 3, "file": "abiParameters.d.ts", "sourceRoot": "", "sources": ["../../../core/internal/abiParameters.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,YAAY,EACZ,gBAAgB,EAChB,2BAA2B,EAC3B,6BAA6B,EAC9B,MAAM,SAAS,CAAA;AAChB,OAAO,KAAK,aAAa,MAAM,qBAAqB,CAAA;AACpD,OAAO,KAAK,OAAO,MAAM,eAAe,CAAA;AACxC,OAAO,KAAK,KAAK,MAAM,aAAa,CAAA;AACpC,OAAO,KAAK,MAAM,MAAM,cAAc,CAAA;AACtC,OAAO,KAAK,GAAG,MAAM,WAAW,CAAA;AAEhC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAA;AAE5E,gBAAgB;AAChB,MAAM,MAAM,wBAAwB,CAClC,YAAY,SAAS,YAAY,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,IAAI,EAAE,OAAO,CAAA;CAAE,EACnE,gBAAgB,SAAS,gBAAgB,GAAG,gBAAgB,IAC1D,2BAA2B,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;AAE/D,gBAAgB;AAChB,MAAM,MAAM,iBAAiB,GAAG;IAAE,OAAO,EAAE,OAAO,CAAC;IAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;CAAE,CAAA;AAEtE,gBAAgB;AAChB,MAAM,MAAM,QAAQ,CAClB,UAAU,SAAS,SAAS,YAAY,EAAE,EAC1C,IAAI,SAAS,gBAAgB,GAAG,gBAAgB,IAC9C,YAAY,CAAC,UAAU,EAAE,aAAa,CAAC,aAAa,CAAC,SAAS,IAAI,GAClE,OAAO,CACL,mBAAmB,CACjB;KACG,KAAK,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS;QACrD,IAAI,EAAE,MAAM,IAAI,SAAS,MAAM,CAAA;KAChC,GACG;SACG,GAAG,IAAI,IAAI,GAAG,2BAA2B,CACxC,UAAU,CAAC,KAAK,CAAC,EACjB,IAAI,CACL;KACF,GACD;SACG,GAAG,IAAI,KAAK,GAAG,2BAA2B,CACzC,UAAU,CAAC,KAAK,CAAC,EACjB,IAAI,CACL;KACF;CACN,CAAC,MAAM,CAAC,CACV,CACF,GACD,OAAO,CAAA;AAEX,gBAAgB;AAChB,MAAM,MAAM,gBAAgB,CAC1B,aAAa,SAAS,SAAS,YAAY,EAAE,EAC7C,gBAAgB,SAAS,gBAAgB,GAAG,gBAAgB,IAC1D,6BAA6B,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAA;AAElE,gBAAgB;AAChB,MAAM,MAAM,KAAK,GAAG,wBAAwB,CAAC,iBAAiB,CAAC,CAAA;AAE/D,gBAAgB;AAChB,wBAAgB,eAAe,CAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,aAAa,CAAC,SAAS,EAC9B,OAAO,EAAE;IAAE,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAAC,cAAc,EAAE,MAAM,CAAA;CAAE,SA0B3E;AAED,MAAM,CAAC,OAAO,WAAW,eAAe,CAAC;IACvC,KAAK,SAAS,GACV,WAAW,CAAC,SAAS,GACrB,WAAW,CAAC,SAAS,GACrB,aAAa,CAAC,SAAS,GACvB,UAAU,CAAC,SAAS,GACpB,WAAW,CAAC,SAAS,GACrB,YAAY,CAAC,SAAS,GACtB,YAAY,CAAC,SAAS,GACtB,aAAa,CAAC,gBAAgB,GAC9B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAKD,gBAAgB;AAChB,wBAAgB,aAAa,CAC3B,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,OAAO,GAAE;IAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAAO,8BAOjD;AAED,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,KAAK,SAAS,GACV,GAAG,CAAC,SAAS,CAAC,SAAS,GACvB,KAAK,CAAC,KAAK,CAAC,SAAS,GACrB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CACzB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,aAAa,CAAC,SAAS,EAC9B,OAAO,EAAE;IACP,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAA;IACrB,cAAc,EAAE,MAAM,CAAA;CACvB,0BA+EF;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACnE;AAED,gBAAgB;AAChB,wBAAgB,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,wBAE/C;AAED,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,KAAK,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACpE;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CACzB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,aAAa,CAAC,SAAS,EAC9B,EAAE,cAAc,EAAE,EAAE;IAAE,cAAc,EAAE,MAAM,CAAA;CAAE,uBA4B/C;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GACV,GAAG,CAAC,SAAS,CAAC,SAAS,GACvB,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,YAAY,CAC1B,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,aAAa,CAAC,SAAS,uBAW/B;AAED,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,KAAK,SAAS,GACV,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,MAAM,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,GAAG;IACxD,UAAU,EAAE,SAAS,aAAa,CAAC,SAAS,EAAE,CAAA;CAC/C,CAAA;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CACzB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,iBAAiB,EACxB,OAAO,EAAE;IAAE,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAAC,cAAc,EAAE,MAAM,CAAA;CAAE,SAqD3E;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACnE;AAED,gBAAgB;AAChB,wBAAgB,YAAY,CAC1B,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,EAAE,cAAc,EAAE,EAAE;IAAE,cAAc,EAAE,MAAM,CAAA;CAAE,uBAwB/C;AAED,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,KAAK,SAAS,GACV,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,KAAK,CAAC,QAAQ,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,iBAAiB,CAC/B,KAAK,CAAC,UAAU,SAAS,aAAa,CAAC,aAAa,EACpD,EACA,eAAe,EACf,UAAU,EACV,MAAM,GACP,EAAE;IACD,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,UAAU,EAAE,UAAU,CAAA;IACtB,MAAM,EAAE,UAAU,SAAS,aAAa,CAAC,aAAa,GAClD,gBAAgB,CAAC,UAAU,CAAC,GAC5B,KAAK,CAAA;CACV,uBAYA;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,iBAAiB,CAAC;IACzC,KAAK,SAAS,GAAG,gBAAgB,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACrE;AAED,gBAAgB;AAChB,wBAAgB,gBAAgB,CAC9B,KAAK,CAAC,SAAS,SAAS,aAAa,CAAC,SAAS,EAC/C,EACA,eAAuB,EACvB,SAAS,EAAE,UAAU,EACrB,KAAK,GACN,EAAE;IACD,SAAS,EAAE,SAAS,CAAA;IACpB,KAAK,EAAE,SAAS,SAAS,aAAa,CAAC,SAAS,GAC5C,wBAAwB,CAAC,SAAS,CAAC,GACnC,KAAK,CAAA;IACT,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACtC,GAAG,iBAAiB,CA4CpB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,KAAK,SAAS,GACV,WAAW,CAAC,SAAS,GACrB,WAAW,CAAC,SAAS,GACrB,aAAa,CAAC,SAAS,GACvB,aAAa,CAAC,SAAS,GACvB,WAAW,CAAC,SAAS,GACrB,YAAY,CAAC,SAAS,GACtB,aAAa,CAAC,gBAAgB,GAC9B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,GAAG,GAAG,CAAC,GAAG,CA4BvE;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,SAAS,GACV,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,IAAI,CAAC,SAAS,GAClB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,aAAa,CAC3B,KAAK,EAAE,GAAG,CAAC,GAAG,EACd,OAAO,EAAE;IAAE,QAAQ,EAAE,OAAO,CAAA;CAAE,GAC7B,iBAAiB,CAOnB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,KAAK,SAAS,GACV,OAAO,CAAC,MAAM,CAAC,SAAS,GACxB,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CAAC,KAAK,CAAC,SAAS,SAAS,aAAa,CAAC,SAAS,EACzE,KAAK,EAAE,wBAAwB,CAAC,SAAS,CAAC,EAC1C,OAAO,EAAE;IACP,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAA;IACrB,SAAS,EAAE,SAAS,CAAA;CACrB,GACA,iBAAiB,CAyCnB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GACV,aAAa,CAAC,iBAAiB,GAC/B,aAAa,CAAC,wBAAwB,GACtC,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CACzB,KAAK,EAAE,GAAG,CAAC,GAAG,EACd,EAAE,IAAI,EAAE,EAAE;IAAE,IAAI,EAAE,MAAM,CAAA;CAAE,GACzB,iBAAiB,CAuBnB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GACV,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,GAAG,CAAC,QAAQ,CAAC,SAAS,GACtB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,KAAK,CAAC,SAAS,GACnB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,aAAa,CAAC,KAAK,EAAE,OAAO,GAAG,iBAAiB,CAM/D;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,KAAK,SAAS,GACV,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,GAAG,CAAC,WAAW,CAAC,SAAS,GACzB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,YAAY,CAC1B,KAAK,EAAE,MAAM,EACb,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;IAAE,MAAM,EAAE,OAAO,CAAC;IAAC,IAAI,EAAE,MAAM,CAAA;CAAE,GAClD,iBAAiB,CAoBnB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,KAAK,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACnE;AAED,gBAAgB;AAChB,wBAAgB,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,iBAAiB,CAc7D;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,KAAK,SAAS,GACV,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,QAAQ,CAAC,SAAS,GACtB,GAAG,CAAC,KAAK,CAAC,SAAS,GACnB,GAAG,CAAC,IAAI,CAAC,SAAS,GAClB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,gBAAgB;AAChB,wBAAgB,WAAW,CACzB,KAAK,CAAC,SAAS,SAAS,aAAa,CAAC,SAAS,GAAG;IAChD,UAAU,EAAE,SAAS,aAAa,CAAC,SAAS,EAAE,CAAA;CAC/C,EAED,KAAK,EAAE,wBAAwB,CAAC,SAAS,CAAC,EAC1C,OAAO,EAAE;IACP,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,SAAS,EAAE,SAAS,CAAA;CACrB,GACA,iBAAiB,CAsBnB;AAED,gBAAgB;AAChB,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/D;AAED,gBAAgB;AAChB,wBAAgB,kBAAkB,CAChC,IAAI,EAAE,MAAM,GACX,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,SAAS,CAMxD;AAED,gBAAgB;AAChB,wBAAgB,eAAe,CAAC,KAAK,EAAE,aAAa,CAAC,SAAS,OAmB7D"}