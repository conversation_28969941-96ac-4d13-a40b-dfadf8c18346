export declare const chainConfig: {
    readonly contracts: {
        readonly gasPriceOracle: {
            readonly address: "0x420000000000000000000000000000000000000F";
        };
        readonly l1Block: {
            readonly address: "0x4200000000000000000000000000000000000015";
        };
        readonly l2CrossDomainMessenger: {
            readonly address: "0x4200000000000000000000000000000000000007";
        };
        readonly l2Erc721Bridge: {
            readonly address: "0x4200000000000000000000000000000000000014";
        };
        readonly l2StandardBridge: {
            readonly address: "0x4200000000000000000000000000000000000010";
        };
        readonly l2ToL1MessagePasser: {
            readonly address: "0x4200000000000000000000000000000000000016";
        };
    };
    readonly formatters: {
        readonly block: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcBlock) => {
                baseFeePerGas: bigint | null;
                blobGasUsed: bigint;
                difficulty: bigint | undefined;
                excessBlobGas: bigint;
                extraData: import("../index.js").Hex;
                gasLimit: bigint | undefined;
                gasUsed: bigint;
                hash: `0x${string}` | null;
                logsBloom: `0x${string}` | null;
                miner: import("abitype").Address;
                nonce: bigint | null | undefined;
                number: bigint | null;
                parentBeaconBlockRoot?: import("../index.js").Hex | undefined;
                parentHash: import("../index.js").Hash;
                receiptsRoot: import("../index.js").Hex;
                sealFields: import("../index.js").Hex[];
                sha3Uncles: import("../index.js").Hash;
                size: bigint;
                stateRoot: import("../index.js").Hash;
                timestamp: bigint;
                totalDifficulty: bigint | null;
                transactions: `0x${string}`[] | import("./types.js").CeloTransaction<boolean>[];
                transactionsRoot: import("../index.js").Hash;
                withdrawals?: import("../index.js").Withdrawal[] | undefined;
                withdrawalsRoot?: import("../index.js").Hex | undefined;
                mixHash?: undefined;
                randomness?: {
                    committed: import("../index.js").Hex;
                    revealed: import("../index.js").Hex;
                } | undefined;
                uncles?: undefined;
            } & {};
            type: "block";
        };
        readonly transaction: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcTransaction) => ({
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                v: bigint;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                gas: bigint;
                nonce: number;
                value: bigint;
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId?: number | undefined;
                yParity?: undefined;
                type: "legacy";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip2930";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip1559";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes: readonly import("../index.js").Hex[];
                chainId: number;
                type: "eip4844";
                gasPrice?: undefined;
                maxFeePerBlobGas: bigint;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList: import("../experimental/index.js").SignedAuthorizationList;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip7702";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                gatewayFee: bigint | null;
                gatewayFeeRecipient: import("abitype").Address | null;
                type: "cip42";
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                type: "cip64";
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                isSystemTx?: boolean;
                mint?: bigint | undefined;
                sourceHash: import("../index.js").Hex;
                type: "deposit";
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId?: undefined;
                feeCurrency?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            }) & {};
            type: "transaction";
        };
        readonly transactionRequest: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloTransactionRequest) => ({
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x0" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                blobs?: undefined;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x1" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x2" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                type?: "0x3" | undefined;
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                to: `0x${string}` | null;
                gasPrice?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                maxFeePerBlobGas: `0x${string}`;
                accessList?: import("../index.js").AccessList | undefined;
                blobs: readonly import("../index.js").Hex[] | readonly import("../index.js").ByteArray[];
                blobVersionedHashes?: readonly import("../index.js").Hex[] | undefined;
                kzg?: import("../index.js").Kzg | undefined;
                sidecars?: readonly import("../index.js").BlobSidecar<import("../index.js").Hex>[] | undefined;
                authorizationList?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                type?: "0x4" | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                authorizationList?: import("../experimental/index.js").RpcAuthorizationList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x7b" | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                feeCurrency?: import("abitype").Address | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
            }) & {};
            type: "transactionRequest";
        };
    };
    readonly serializers: {
        readonly transaction: typeof import("./serializers.js").serializeTransaction;
    };
    readonly fees: import("../index.js").ChainFees<{
        readonly block: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcBlock) => {
                baseFeePerGas: bigint | null;
                blobGasUsed: bigint;
                difficulty: bigint | undefined;
                excessBlobGas: bigint;
                extraData: import("../index.js").Hex;
                gasLimit: bigint | undefined;
                gasUsed: bigint;
                hash: `0x${string}` | null;
                logsBloom: `0x${string}` | null;
                miner: import("abitype").Address;
                nonce: bigint | null | undefined;
                number: bigint | null;
                parentBeaconBlockRoot?: import("../index.js").Hex | undefined;
                parentHash: import("../index.js").Hash;
                receiptsRoot: import("../index.js").Hex;
                sealFields: import("../index.js").Hex[];
                sha3Uncles: import("../index.js").Hash;
                size: bigint;
                stateRoot: import("../index.js").Hash;
                timestamp: bigint;
                totalDifficulty: bigint | null;
                transactions: `0x${string}`[] | import("./types.js").CeloTransaction<boolean>[];
                transactionsRoot: import("../index.js").Hash;
                withdrawals?: import("../index.js").Withdrawal[] | undefined;
                withdrawalsRoot?: import("../index.js").Hex | undefined;
                mixHash?: undefined;
                randomness?: {
                    committed: import("../index.js").Hex;
                    revealed: import("../index.js").Hex;
                } | undefined;
                uncles?: undefined;
            } & {};
            type: "block";
        };
        readonly transaction: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcTransaction) => ({
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                v: bigint;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                gas: bigint;
                nonce: number;
                value: bigint;
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId?: number | undefined;
                yParity?: undefined;
                type: "legacy";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip2930";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip1559";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined;
                blobVersionedHashes: readonly import("../index.js").Hex[];
                chainId: number;
                type: "eip4844";
                gasPrice?: undefined;
                maxFeePerBlobGas: bigint;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList: import("../experimental/index.js").SignedAuthorizationList;
                blobVersionedHashes?: undefined;
                chainId: number;
                type: "eip7702";
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                gatewayFee: bigint | null;
                gatewayFeeRecipient: import("abitype").Address | null;
                type: "cip42";
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                type: "cip64";
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                isSystemTx?: boolean;
                mint?: bigint | undefined;
                sourceHash: import("../index.js").Hex;
                type: "deposit";
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                chainId?: undefined;
                feeCurrency?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            }) & {};
            type: "transaction";
        };
        readonly transactionRequest: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloTransactionRequest) => ({
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x0" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                blobs?: undefined;
                accessList?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x1" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: undefined;
                maxPriorityFeePerGas?: undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x2" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                type?: "0x3" | undefined;
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                to: `0x${string}` | null;
                gasPrice?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                maxFeePerBlobGas: `0x${string}`;
                accessList?: import("../index.js").AccessList | undefined;
                blobs: readonly import("../index.js").Hex[] | readonly import("../index.js").ByteArray[];
                blobVersionedHashes?: readonly import("../index.js").Hex[] | undefined;
                kzg?: import("../index.js").Kzg | undefined;
                sidecars?: readonly import("../index.js").BlobSidecar<import("../index.js").Hex>[] | undefined;
                authorizationList?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                type?: "0x4" | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                authorizationList?: import("../experimental/index.js").RpcAuthorizationList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: import("abitype").Address | undefined;
            } | {
                data?: import("../index.js").Hex | undefined;
                from?: import("abitype").Address | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: import("abitype").Address | null | undefined;
                type?: "0x7b" | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                feeCurrency?: import("abitype").Address | undefined;
                gasPrice?: undefined;
                maxFeePerBlobGas?: undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                blobs?: undefined;
                authorizationList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
            }) & {};
            type: "transactionRequest";
        };
    }>;
};
//# sourceMappingURL=chainConfig.d.ts.map