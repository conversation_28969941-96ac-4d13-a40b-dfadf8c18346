"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addressV07 = exports.addressV06 = exports.abiV07 = exports.abiV06 = void 0;
exports.abiV06 = [
    {
        inputs: [
            { name: 'preOpGas', type: 'uint256' },
            { name: 'paid', type: 'uint256' },
            { name: 'validAfter', type: 'uint48' },
            { name: 'validUntil', type: 'uint48' },
            { name: 'targetSuccess', type: 'bool' },
            { name: 'targetResult', type: 'bytes' },
        ],
        name: 'ExecutionResult',
        type: 'error',
    },
    {
        inputs: [
            { name: 'opIndex', type: 'uint256' },
            { name: 'reason', type: 'string' },
        ],
        name: 'FailedOp',
        type: 'error',
    },
    {
        inputs: [{ name: 'sender', type: 'address' }],
        name: 'SenderAddressResult',
        type: 'error',
    },
    {
        inputs: [{ name: 'aggregator', type: 'address' }],
        name: 'SignatureValidationFailed',
        type: 'error',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'preOpGas', type: 'uint256' },
                    { name: 'prefund', type: 'uint256' },
                    { name: 'sigFailed', type: 'bool' },
                    { name: 'validAfter', type: 'uint48' },
                    { name: 'validUntil', type: 'uint48' },
                    { name: 'paymasterContext', type: 'bytes' },
                ],
                name: 'returnInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'senderInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'factoryInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'paymasterInfo',
                type: 'tuple',
            },
        ],
        name: 'ValidationResult',
        type: 'error',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'preOpGas', type: 'uint256' },
                    { name: 'prefund', type: 'uint256' },
                    { name: 'sigFailed', type: 'bool' },
                    { name: 'validAfter', type: 'uint48' },
                    { name: 'validUntil', type: 'uint48' },
                    { name: 'paymasterContext', type: 'bytes' },
                ],
                name: 'returnInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'senderInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'factoryInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'stake', type: 'uint256' },
                    { name: 'unstakeDelaySec', type: 'uint256' },
                ],
                name: 'paymasterInfo',
                type: 'tuple',
            },
            {
                components: [
                    { name: 'aggregator', type: 'address' },
                    {
                        components: [
                            { name: 'stake', type: 'uint256' },
                            {
                                name: 'unstakeDelaySec',
                                type: 'uint256',
                            },
                        ],
                        name: 'stakeInfo',
                        type: 'tuple',
                    },
                ],
                name: 'aggregatorInfo',
                type: 'tuple',
            },
        ],
        name: 'ValidationResultWithAggregation',
        type: 'error',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'factory',
                type: 'address',
            },
            {
                indexed: false,
                name: 'paymaster',
                type: 'address',
            },
        ],
        name: 'AccountDeployed',
        type: 'event',
    },
    { anonymous: false, inputs: [], name: 'BeforeExecution', type: 'event' },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'totalDeposit',
                type: 'uint256',
            },
        ],
        name: 'Deposited',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'aggregator',
                type: 'address',
            },
        ],
        name: 'SignatureAggregatorChanged',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'totalStaked',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'unstakeDelaySec',
                type: 'uint256',
            },
        ],
        name: 'StakeLocked',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawTime',
                type: 'uint256',
            },
        ],
        name: 'StakeUnlocked',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawAddress',
                type: 'address',
            },
            {
                indexed: false,
                name: 'amount',
                type: 'uint256',
            },
        ],
        name: 'StakeWithdrawn',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: true,
                name: 'paymaster',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
            { indexed: false, name: 'success', type: 'bool' },
            {
                indexed: false,
                name: 'actualGasCost',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'actualGasUsed',
                type: 'uint256',
            },
        ],
        name: 'UserOperationEvent',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'revertReason',
                type: 'bytes',
            },
        ],
        name: 'UserOperationRevertReason',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawAddress',
                type: 'address',
            },
            {
                indexed: false,
                name: 'amount',
                type: 'uint256',
            },
        ],
        name: 'Withdrawn',
        type: 'event',
    },
    {
        inputs: [],
        name: 'SIG_VALIDATION_FAILED',
        outputs: [{ name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { name: 'initCode', type: 'bytes' },
            { name: 'sender', type: 'address' },
            { name: 'paymasterAndData', type: 'bytes' },
        ],
        name: '_validateSenderAndPaymaster',
        outputs: [],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'unstakeDelaySec', type: 'uint32' }],
        name: 'addStake',
        outputs: [],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'balanceOf',
        outputs: [{ name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'depositTo',
        outputs: [],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [{ name: '', type: 'address' }],
        name: 'deposits',
        outputs: [
            { name: 'deposit', type: 'uint112' },
            { name: 'staked', type: 'bool' },
            { name: 'stake', type: 'uint112' },
            { name: 'unstakeDelaySec', type: 'uint32' },
            { name: 'withdrawTime', type: 'uint48' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'getDepositInfo',
        outputs: [
            {
                components: [
                    { name: 'deposit', type: 'uint112' },
                    { name: 'staked', type: 'bool' },
                    { name: 'stake', type: 'uint112' },
                    { name: 'unstakeDelaySec', type: 'uint32' },
                    { name: 'withdrawTime', type: 'uint48' },
                ],
                name: 'info',
                type: 'tuple',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { name: 'sender', type: 'address' },
            { name: 'key', type: 'uint192' },
        ],
        name: 'getNonce',
        outputs: [{ name: 'nonce', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'initCode', type: 'bytes' }],
        name: 'getSenderAddress',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    { name: 'callGasLimit', type: 'uint256' },
                    {
                        name: 'verificationGasLimit',
                        type: 'uint256',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'maxFeePerGas', type: 'uint256' },
                    {
                        name: 'maxPriorityFeePerGas',
                        type: 'uint256',
                    },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'userOp',
                type: 'tuple',
            },
        ],
        name: 'getUserOpHash',
        outputs: [{ name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    {
                        components: [
                            { name: 'sender', type: 'address' },
                            { name: 'nonce', type: 'uint256' },
                            { name: 'initCode', type: 'bytes' },
                            { name: 'callData', type: 'bytes' },
                            {
                                name: 'callGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'verificationGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'preVerificationGas',
                                type: 'uint256',
                            },
                            {
                                name: 'maxFeePerGas',
                                type: 'uint256',
                            },
                            {
                                name: 'maxPriorityFeePerGas',
                                type: 'uint256',
                            },
                            {
                                name: 'paymasterAndData',
                                type: 'bytes',
                            },
                            { name: 'signature', type: 'bytes' },
                        ],
                        name: 'userOps',
                        type: 'tuple[]',
                    },
                    {
                        name: 'aggregator',
                        type: 'address',
                    },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'opsPerAggregator',
                type: 'tuple[]',
            },
            { name: 'beneficiary', type: 'address' },
        ],
        name: 'handleAggregatedOps',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    { name: 'callGasLimit', type: 'uint256' },
                    {
                        name: 'verificationGasLimit',
                        type: 'uint256',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'maxFeePerGas', type: 'uint256' },
                    {
                        name: 'maxPriorityFeePerGas',
                        type: 'uint256',
                    },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'ops',
                type: 'tuple[]',
            },
            { name: 'beneficiary', type: 'address' },
        ],
        name: 'handleOps',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ name: 'key', type: 'uint192' }],
        name: 'incrementNonce',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { name: 'callData', type: 'bytes' },
            {
                components: [
                    {
                        components: [
                            { name: 'sender', type: 'address' },
                            { name: 'nonce', type: 'uint256' },
                            {
                                name: 'callGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'verificationGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'preVerificationGas',
                                type: 'uint256',
                            },
                            { name: 'paymaster', type: 'address' },
                            {
                                name: 'maxFeePerGas',
                                type: 'uint256',
                            },
                            {
                                name: 'maxPriorityFeePerGas',
                                type: 'uint256',
                            },
                        ],
                        name: 'mUserOp',
                        type: 'tuple',
                    },
                    { name: 'userOpHash', type: 'bytes32' },
                    { name: 'prefund', type: 'uint256' },
                    { name: 'contextOffset', type: 'uint256' },
                    { name: 'preOpGas', type: 'uint256' },
                ],
                name: 'opInfo',
                type: 'tuple',
            },
            { name: 'context', type: 'bytes' },
        ],
        name: 'innerHandleOp',
        outputs: [{ name: 'actualGasCost', type: 'uint256' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { name: '', type: 'address' },
            { name: '', type: 'uint192' },
        ],
        name: 'nonceSequenceNumber',
        outputs: [{ name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    { name: 'callGasLimit', type: 'uint256' },
                    {
                        name: 'verificationGasLimit',
                        type: 'uint256',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'maxFeePerGas', type: 'uint256' },
                    {
                        name: 'maxPriorityFeePerGas',
                        type: 'uint256',
                    },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'op',
                type: 'tuple',
            },
            { name: 'target', type: 'address' },
            { name: 'targetCallData', type: 'bytes' },
        ],
        name: 'simulateHandleOp',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    { name: 'callGasLimit', type: 'uint256' },
                    {
                        name: 'verificationGasLimit',
                        type: 'uint256',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'maxFeePerGas', type: 'uint256' },
                    {
                        name: 'maxPriorityFeePerGas',
                        type: 'uint256',
                    },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'userOp',
                type: 'tuple',
            },
        ],
        name: 'simulateValidation',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [],
        name: 'unlockStake',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                name: 'withdrawAddress',
                type: 'address',
            },
        ],
        name: 'withdrawStake',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                name: 'withdrawAddress',
                type: 'address',
            },
            { name: 'withdrawAmount', type: 'uint256' },
        ],
        name: 'withdrawTo',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    { stateMutability: 'payable', type: 'receive' },
];
exports.abiV07 = [
    {
        inputs: [
            { name: 'success', type: 'bool' },
            { name: 'ret', type: 'bytes' },
        ],
        name: 'DelegateAndRevert',
        type: 'error',
    },
    {
        inputs: [
            { name: 'opIndex', type: 'uint256' },
            { name: 'reason', type: 'string' },
        ],
        name: 'FailedOp',
        type: 'error',
    },
    {
        inputs: [
            { name: 'opIndex', type: 'uint256' },
            { name: 'reason', type: 'string' },
            { name: 'inner', type: 'bytes' },
        ],
        name: 'FailedOpWithRevert',
        type: 'error',
    },
    {
        inputs: [{ name: 'returnData', type: 'bytes' }],
        name: 'PostOpReverted',
        type: 'error',
    },
    { inputs: [], name: 'ReentrancyGuardReentrantCall', type: 'error' },
    {
        inputs: [{ name: 'sender', type: 'address' }],
        name: 'SenderAddressResult',
        type: 'error',
    },
    {
        inputs: [{ name: 'aggregator', type: 'address' }],
        name: 'SignatureValidationFailed',
        type: 'error',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'factory',
                type: 'address',
            },
            {
                indexed: false,
                name: 'paymaster',
                type: 'address',
            },
        ],
        name: 'AccountDeployed',
        type: 'event',
    },
    { anonymous: false, inputs: [], name: 'BeforeExecution', type: 'event' },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'totalDeposit',
                type: 'uint256',
            },
        ],
        name: 'Deposited',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'revertReason',
                type: 'bytes',
            },
        ],
        name: 'PostOpRevertReason',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'aggregator',
                type: 'address',
            },
        ],
        name: 'SignatureAggregatorChanged',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'totalStaked',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'unstakeDelaySec',
                type: 'uint256',
            },
        ],
        name: 'StakeLocked',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawTime',
                type: 'uint256',
            },
        ],
        name: 'StakeUnlocked',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawAddress',
                type: 'address',
            },
            {
                indexed: false,
                name: 'amount',
                type: 'uint256',
            },
        ],
        name: 'StakeWithdrawn',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: true,
                name: 'paymaster',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
            { indexed: false, name: 'success', type: 'bool' },
            {
                indexed: false,
                name: 'actualGasCost',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'actualGasUsed',
                type: 'uint256',
            },
        ],
        name: 'UserOperationEvent',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
        ],
        name: 'UserOperationPrefundTooLow',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                indexed: true,
                name: 'sender',
                type: 'address',
            },
            {
                indexed: false,
                name: 'nonce',
                type: 'uint256',
            },
            {
                indexed: false,
                name: 'revertReason',
                type: 'bytes',
            },
        ],
        name: 'UserOperationRevertReason',
        type: 'event',
    },
    {
        anonymous: false,
        inputs: [
            {
                indexed: true,
                name: 'account',
                type: 'address',
            },
            {
                indexed: false,
                name: 'withdrawAddress',
                type: 'address',
            },
            {
                indexed: false,
                name: 'amount',
                type: 'uint256',
            },
        ],
        name: 'Withdrawn',
        type: 'event',
    },
    {
        inputs: [{ name: 'unstakeDelaySec', type: 'uint32' }],
        name: 'addStake',
        outputs: [],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'balanceOf',
        outputs: [{ name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { name: 'target', type: 'address' },
            { name: 'data', type: 'bytes' },
        ],
        name: 'delegateAndRevert',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'depositTo',
        outputs: [],
        stateMutability: 'payable',
        type: 'function',
    },
    {
        inputs: [{ name: '', type: 'address' }],
        name: 'deposits',
        outputs: [
            { name: 'deposit', type: 'uint256' },
            { name: 'staked', type: 'bool' },
            { name: 'stake', type: 'uint112' },
            { name: 'unstakeDelaySec', type: 'uint32' },
            { name: 'withdrawTime', type: 'uint48' },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'account', type: 'address' }],
        name: 'getDepositInfo',
        outputs: [
            {
                components: [
                    { name: 'deposit', type: 'uint256' },
                    { name: 'staked', type: 'bool' },
                    { name: 'stake', type: 'uint112' },
                    { name: 'unstakeDelaySec', type: 'uint32' },
                    { name: 'withdrawTime', type: 'uint48' },
                ],
                name: 'info',
                type: 'tuple',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            { name: 'sender', type: 'address' },
            { name: 'key', type: 'uint192' },
        ],
        name: 'getNonce',
        outputs: [{ name: 'nonce', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'initCode', type: 'bytes' }],
        name: 'getSenderAddress',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    {
                        name: 'accountGasLimits',
                        type: 'bytes32',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'gasFees', type: 'bytes32' },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'userOp',
                type: 'tuple',
            },
        ],
        name: 'getUserOpHash',
        outputs: [{ name: '', type: 'bytes32' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    {
                        components: [
                            { name: 'sender', type: 'address' },
                            { name: 'nonce', type: 'uint256' },
                            { name: 'initCode', type: 'bytes' },
                            { name: 'callData', type: 'bytes' },
                            {
                                name: 'accountGasLimits',
                                type: 'bytes32',
                            },
                            {
                                name: 'preVerificationGas',
                                type: 'uint256',
                            },
                            { name: 'gasFees', type: 'bytes32' },
                            {
                                name: 'paymasterAndData',
                                type: 'bytes',
                            },
                            { name: 'signature', type: 'bytes' },
                        ],
                        name: 'userOps',
                        type: 'tuple[]',
                    },
                    {
                        name: 'aggregator',
                        type: 'address',
                    },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'opsPerAggregator',
                type: 'tuple[]',
            },
            { name: 'beneficiary', type: 'address' },
        ],
        name: 'handleAggregatedOps',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                components: [
                    { name: 'sender', type: 'address' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'initCode', type: 'bytes' },
                    { name: 'callData', type: 'bytes' },
                    {
                        name: 'accountGasLimits',
                        type: 'bytes32',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    { name: 'gasFees', type: 'bytes32' },
                    { name: 'paymasterAndData', type: 'bytes' },
                    { name: 'signature', type: 'bytes' },
                ],
                name: 'ops',
                type: 'tuple[]',
            },
            { name: 'beneficiary', type: 'address' },
        ],
        name: 'handleOps',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ name: 'key', type: 'uint192' }],
        name: 'incrementNonce',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { name: 'callData', type: 'bytes' },
            {
                components: [
                    {
                        components: [
                            { name: 'sender', type: 'address' },
                            { name: 'nonce', type: 'uint256' },
                            {
                                name: 'verificationGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'callGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'paymasterVerificationGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'paymasterPostOpGasLimit',
                                type: 'uint256',
                            },
                            {
                                name: 'preVerificationGas',
                                type: 'uint256',
                            },
                            { name: 'paymaster', type: 'address' },
                            {
                                name: 'maxFeePerGas',
                                type: 'uint256',
                            },
                            {
                                name: 'maxPriorityFeePerGas',
                                type: 'uint256',
                            },
                        ],
                        name: 'mUserOp',
                        type: 'tuple',
                    },
                    { name: 'userOpHash', type: 'bytes32' },
                    { name: 'prefund', type: 'uint256' },
                    { name: 'contextOffset', type: 'uint256' },
                    { name: 'preOpGas', type: 'uint256' },
                ],
                name: 'opInfo',
                type: 'tuple',
            },
            { name: 'context', type: 'bytes' },
        ],
        name: 'innerHandleOp',
        outputs: [{ name: 'actualGasCost', type: 'uint256' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            { name: '', type: 'address' },
            { name: '', type: 'uint192' },
        ],
        name: 'nonceSequenceNumber',
        outputs: [{ name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [{ name: 'interfaceId', type: 'bytes4' }],
        name: 'supportsInterface',
        outputs: [{ name: '', type: 'bool' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        inputs: [],
        name: 'unlockStake',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                name: 'withdrawAddress',
                type: 'address',
            },
        ],
        name: 'withdrawStake',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [
            {
                name: 'withdrawAddress',
                type: 'address',
            },
            { name: 'withdrawAmount', type: 'uint256' },
        ],
        name: 'withdrawTo',
        outputs: [],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    { stateMutability: 'payable', type: 'receive' },
];
exports.addressV06 = '0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789';
exports.addressV07 = '0x0000000071727De22E5E9d8BAf0edAc6f37da032';
//# sourceMappingURL=EntryPoint.js.map