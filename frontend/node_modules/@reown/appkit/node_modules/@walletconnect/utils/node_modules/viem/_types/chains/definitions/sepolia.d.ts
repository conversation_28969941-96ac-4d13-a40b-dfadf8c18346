export declare const sepolia: {
    blockExplorers: {
        readonly default: {
            readonly name: "Etherscan";
            readonly url: "https://sepolia.etherscan.io";
            readonly apiUrl: "https://api-sepolia.etherscan.io/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 751532;
        };
        readonly ensRegistry: {
            readonly address: "******************************************";
        };
        readonly ensUniversalResolver: {
            readonly address: "******************************************";
            readonly blockCreated: 5317080;
        };
    };
    id: 11155111;
    name: "<PERSON><PERSON>";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON> Ether";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://sepolia.drpc.org"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=sepolia.d.ts.map