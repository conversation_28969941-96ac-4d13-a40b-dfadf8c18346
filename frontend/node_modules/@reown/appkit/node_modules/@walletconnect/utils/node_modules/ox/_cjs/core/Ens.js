"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.labelhash = labelhash;
exports.namehash = namehash;
exports.normalize = normalize;
const ens_normalize_1 = require("@adraffy/ens-normalize");
const Bytes = require("./Bytes.js");
const Hash = require("./Hash.js");
const Hex = require("./Hex.js");
const internal = require("./internal/ens.js");
function labelhash(label) {
    const result = new Uint8Array(32).fill(0);
    if (!label)
        return Hex.fromBytes(result);
    return (internal.unwrapLabelhash(label) || Hash.keccak256(Hex.fromString(label)));
}
function namehash(name) {
    let result = new Uint8Array(32).fill(0);
    if (!name)
        return Hex.fromBytes(result);
    const labels = name.split('.');
    for (let i = labels.length - 1; i >= 0; i -= 1) {
        const hashFromEncodedLabel = internal.unwrapLabelhash(labels[i]);
        const hashed = hashFromEncodedLabel
            ? Bytes.fromHex(hashFromEncodedLabel)
            : Hash.keccak256(Bytes.fromString(labels[i]), { as: 'Bytes' });
        result = Hash.keccak256(Bytes.concat(result, hashed), { as: 'Bytes' });
    }
    return Hex.fromBytes(result);
}
function normalize(name) {
    return (0, ens_normalize_1.ens_normalize)(name);
}
//# sourceMappingURL=Ens.js.map