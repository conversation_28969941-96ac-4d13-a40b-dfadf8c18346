{"version": 3, "file": "signTransaction.d.ts", "sourceRoot": "", "sources": ["../../../actions/wallet/signTransaction.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAA;AACtD,OAAO,EACL,KAAK,qBAAqB,EAE3B,MAAM,sCAAsC,CAAA;AAC7C,OAAO,KAAK,EAAE,wBAAwB,IAAI,gCAAgC,EAAE,MAAM,yCAAyC,CAAA;AAC3H,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,6CAA6C,CAAA;AAE5E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAA;AACjE,OAAO,KAAK,EACV,KAAK,EACL,WAAW,EACX,iBAAiB,EAClB,MAAM,sBAAsB,CAAA;AAC7B,OAAO,KAAK,EAAE,iCAAiC,EAAE,MAAM,oBAAoB,CAAA;AAE3E,OAAO,KAAK,EAGV,qBAAqB,EACtB,MAAM,4BAA4B,CAAA;AACnC,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AACrD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAA;AACnE,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,yCAAyC,CAAA;AAEhD,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAA;AACzE,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,8CAA8C,CAAA;AAErD,OAAO,EACL,KAAK,sBAAsB,EAE5B,MAAM,0CAA0C,CAAA;AACjD,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,+CAA+C,CAAA;AACvF,OAAO,EAAE,KAAK,mBAAmB,EAAc,MAAM,yBAAyB,CAAA;AAE9E,MAAM,MAAM,sBAAsB,CAChC,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EAE3D,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,IACzE,SAAS,CAAC,2BAA2B,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,CAAA;AAEjE,MAAM,MAAM,yBAAyB,CACnC,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,EACnC,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EAC3D,OAAO,SAAS,sBAAsB,CACpC,KAAK,EACL,aAAa,CACd,GAAG,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,IAC9C,OAAO,GACT,mBAAmB,CAAC,OAAO,CAAC,GAC5B,iBAAiB,CAAC,KAAK,EAAE,aAAa,CAAC,GACvC,iCAAiC,CAAC,OAAO,CAAC,CAAA;AAE5C,MAAM,MAAM,yBAAyB,CACnC,OAAO,SAAS,sBAAsB,GAAG,sBAAsB,IAC7D,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAA;AAEtD,MAAM,MAAM,wBAAwB,GAChC,qBAAqB,GACrB,sBAAsB,GACtB,mBAAmB,GACnB,2BAA2B,GAC3B,gCAAgC,GAChC,oBAAoB,GACpB,gBAAgB,GAChB,SAAS,CAAA;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,wBAAsB,eAAe,CACnC,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,EACnC,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EACnD,KAAK,CAAC,OAAO,SAAS,sBAAsB,CAC1C,KAAK,EACL,aAAa,CACd,GAAG,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,EAEhD,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,EACzC,UAAU,EAAE,yBAAyB,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,GAC5E,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAmD7C"}