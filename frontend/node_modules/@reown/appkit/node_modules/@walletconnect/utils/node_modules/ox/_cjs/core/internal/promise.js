"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeoutError = void 0;
exports.withTimeout = withTimeout;
const Errors = require("../Errors.js");
function withTimeout(fn, options) {
    const { errorInstance = new TimeoutError(), timeout, signal } = options;
    return new Promise((resolve, reject) => {
        ;
        (async () => {
            let timeoutId;
            try {
                const controller = new AbortController();
                if (timeout > 0)
                    timeoutId = setTimeout(() => {
                        if (signal) {
                            controller.abort();
                        }
                        else {
                            reject(errorInstance);
                        }
                    }, timeout);
                resolve(await fn({ signal: controller.signal }));
            }
            catch (err) {
                if (err?.name === 'AbortError')
                    reject(errorInstance);
                reject(err);
            }
            finally {
                clearTimeout(timeoutId);
            }
        })();
    });
}
class TimeoutError extends Errors.BaseError {
    constructor() {
        super('Operation timed out.');
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 'Promise.TimeoutError'
        });
    }
}
exports.TimeoutError = TimeoutError;
//# sourceMappingURL=promise.js.map