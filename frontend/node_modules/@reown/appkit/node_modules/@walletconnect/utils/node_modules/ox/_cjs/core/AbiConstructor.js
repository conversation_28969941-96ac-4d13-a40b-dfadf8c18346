"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decode = decode;
exports.encode = encode;
exports.format = format;
exports.from = from;
exports.fromAbi = fromAbi;
const abitype = require("abitype");
const AbiItem = require("./AbiItem.js");
const AbiParameters = require("./AbiParameters.js");
const Hex = require("./Hex.js");
function decode(abiConstructor, options) {
    const { bytecode } = options;
    if (abiConstructor.inputs.length === 0)
        return undefined;
    const data = options.data.replace(bytecode, '0x');
    return AbiParameters.decode(abiConstructor.inputs, data);
}
function encode(abiConstructor, options) {
    const { bytecode, args } = options;
    return Hex.concat(bytecode, abiConstructor.inputs?.length && args?.length
        ? AbiParameters.encode(abiConstructor.inputs, args)
        : '0x');
}
function format(abiConstructor) {
    return abitype.formatAbiItem(abiConstructor);
}
function from(abiConstructor) {
    return AbiItem.from(abiConstructor);
}
function fromAbi(abi) {
    const item = abi.find((item) => item.type === 'constructor');
    if (!item)
        throw new AbiItem.NotFoundError({ name: 'constructor' });
    return item;
}
//# sourceMappingURL=AbiConstructor.js.map