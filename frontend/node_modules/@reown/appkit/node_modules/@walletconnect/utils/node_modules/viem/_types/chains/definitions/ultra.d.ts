export declare const ultra: {
    blockExplorers: {
        readonly default: {
            readonly name: "Ultra EVM Explorer";
            readonly url: "https://evmexplorer.ultra.io";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 19991;
    name: "Ultra EVM";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "Ultra Token";
        readonly symbol: "UOS";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://evm.ultra.eosusa.io"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=ultra.d.ts.map