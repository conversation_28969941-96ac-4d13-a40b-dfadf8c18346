{"version": 3, "file": "estimateMaxPriorityFeePerGas.d.ts", "sourceRoot": "", "sources": ["../../../actions/public/estimateMaxPriorityFeePerGas.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,6CAA6C,CAAA;AAC5E,OAAO,EAEL,KAAK,gCAAgC,EACtC,MAAM,qBAAqB,CAAA;AAC5B,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAA;AACrD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EAAE,KAAK,EAAyB,MAAM,sBAAsB,CAAA;AACxE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAA;AAC7D,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAA;AACnE,OAAO,EACL,KAAK,oBAAoB,EAE1B,MAAM,iCAAiC,CAAA;AAExC,OAAO,KAAK,EAAE,mCAAmC,EAAE,MAAM,wCAAwC,CAAA;AACjG,OAAO,EAAE,KAAK,iBAAiB,EAAY,MAAM,eAAe,CAAA;AAChE,OAAO,EAAE,KAAK,oBAAoB,EAAe,MAAM,kBAAkB,CAAA;AAEzE,MAAM,MAAM,sCAAsC,CAChD,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,IACzD,iBAAiB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;AAE3C,MAAM,MAAM,sCAAsC,GAAG,MAAM,CAAA;AAE3D,MAAM,MAAM,qCAAqC,GAC7C,iBAAiB,GACjB,oBAAoB,GACpB,gBAAgB,GAChB,iBAAiB,GACjB,oBAAoB,GACpB,gCAAgC,GAChC,SAAS,CAAA;AAEb;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAsB,4BAA4B,CAChD,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,aAAa,SAAS,KAAK,GAAG,SAAS,EAEvC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAChC,IAAI,CAAC,EACD,sCAAsC,CAAC,KAAK,EAAE,aAAa,CAAC,GAC5D,SAAS,GACZ,OAAO,CAAC,sCAAsC,CAAC,CAEjD;AAED,wBAAsB,qCAAqC,CACzD,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,aAAa,SAAS,KAAK,GAAG,SAAS,EAEvC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAChC,IAAI,EAAE,sCAAsC,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG;IACnE,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IACzB,OAAO,CAAC,EACJ,mCAAmC,CACjC,KAAK,EACL,OAAO,GAAG,SAAS,EACnB,aAAa,CACd,GACD,SAAS,CAAA;CACd,GACA,OAAO,CAAC,sCAAsC,CAAC,CA4CjD"}