import * as Address from './Address.js';
import * as Bytes from './Bytes.js';
import * as Hash from './Hash.js';
import * as Hex from './Hex.js';
import * as Rlp from './Rlp.js';
/**
 * Computes Contract Address generated by the [CREATE](https://ethereum.stackexchange.com/questions/68943/create-opcode-what-does-it-really-do/68945#68945) or [CREATE2](https://eips.ethereum.org/EIPS/eip-1014) opcode.
 *
 * @example
 * ### CREATE
 *
 * Computes via the [CREATE](https://ethereum.stackexchange.com/questions/68943/create-opcode-what-does-it-really-do/68945#68945) opcode. Shorthand for {@link ox#ContractAddress.(fromCreate:function)}.
 *
 * ```ts twoslash
 * import { ContractAddress } from 'ox'
 * ContractAddress.from({
 *   from: '******************************************',
 *   nonce: 0n,
 * })
 * // @log: '******************************************'
 * ```
 *
 * @example
 * ### CREATE2
 *
 * Computes via the [CREATE2](https://eips.ethereum.org/EIPS/eip-1014) opcode. Shorthand for {@link ox#ContractAddress.(fromCreate2:function)}.
 *
 * ```ts twoslash
 * import { ContractAddress, Hex } from 'ox'
 * ContractAddress.from({
 *   from: '******************************************',
 *   bytecode: '0x6394198df16000526103ff60206004601c335afa6040516060f3',
 *   salt: Hex.fromString('hello world'),
 * })
 * // @log: '******************************************'
 * ```
 *
 * @param options - Options.
 * @returns Contract Address.
 */
export function from(options) {
    if (options.salt)
        return fromCreate2(options);
    return fromCreate(options);
}
/**
 * Computes contract address via [CREATE](https://ethereum.stackexchange.com/questions/68943/create-opcode-what-does-it-really-do/68945#68945) opcode.
 *
 * @example
 * ```ts twoslash
 * import { ContractAddress } from 'ox'
 *
 * ContractAddress.fromCreate({
 *   from: '******************************************',
 *   nonce: 0n,
 * })
 * // @log: '******************************************'
 * ```
 *
 * @param options - Options for retrieving address.
 * @returns Contract Address.
 */
export function fromCreate(options) {
    const from = Bytes.fromHex(Address.from(options.from));
    let nonce = Bytes.fromNumber(options.nonce);
    if (nonce[0] === 0)
        nonce = new Uint8Array([]);
    return Address.from(`0x${Hash.keccak256(Rlp.fromBytes([from, nonce], { as: 'Hex' })).slice(26)}`);
}
/**
 * Computes contract address via [CREATE2](https://eips.ethereum.org/EIPS/eip-1014) opcode.
 *
 * @example
 * ```ts twoslash
 * import { ContractAddress, Hex } from 'ox'
 *
 * ContractAddress.fromCreate2({
 *   from: '******************************************',
 *   bytecode: '0x6394198df16000526103ff60206004601c335afa6040516060f3',
 *   salt: Hex.fromString('hello world'),
 * })
 * // @log: '******************************************'
 * ```
 *
 * @param options - Options for retrieving address.
 * @returns Contract Address.
 */
export function fromCreate2(options) {
    const from = Bytes.fromHex(Address.from(options.from));
    const salt = Bytes.padLeft(Bytes.validate(options.salt) ? options.salt : Bytes.fromHex(options.salt), 32);
    const bytecodeHash = (() => {
        if ('bytecodeHash' in options) {
            if (Bytes.validate(options.bytecodeHash))
                return options.bytecodeHash;
            return Bytes.fromHex(options.bytecodeHash);
        }
        return Hash.keccak256(options.bytecode, { as: 'Bytes' });
    })();
    return Address.from(Hex.slice(Hash.keccak256(Bytes.concat(Bytes.fromHex('0xff'), from, salt, bytecodeHash), { as: 'Hex' }), 12));
}
//# sourceMappingURL=ContractAddress.js.map