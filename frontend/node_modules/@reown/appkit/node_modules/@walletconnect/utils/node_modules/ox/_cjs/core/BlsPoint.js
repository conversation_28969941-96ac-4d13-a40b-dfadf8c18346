"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBytes = toBytes;
exports.toHex = toHex;
exports.fromBytes = fromBytes;
exports.fromHex = fromHex;
const bls12_381_1 = require("@noble/curves/bls12-381");
const Hex = require("./Hex.js");
function toBytes(point) {
    const group = typeof point.z === 'bigint' ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    return new group.ProjectivePoint(point.x, point.y, point.z).toRawBytes();
}
function toHex(point) {
    return Hex.fromBytes(toBytes(point));
}
function fromBytes(bytes) {
    const group = bytes.length === 48 ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    const point = group.ProjectivePoint.fromHex(bytes);
    return {
        x: point.px,
        y: point.py,
        z: point.pz,
    };
}
function fromHex(hex, group) {
    return fromBytes(Hex.toBytes(hex), group);
}
//# sourceMappingURL=BlsPoint.js.map