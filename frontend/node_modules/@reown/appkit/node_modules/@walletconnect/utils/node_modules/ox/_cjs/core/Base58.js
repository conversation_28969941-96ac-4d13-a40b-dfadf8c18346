"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromBytes = fromBytes;
exports.fromHex = fromHex;
exports.fromString = fromString;
exports.toBytes = toBytes;
exports.toHex = toHex;
exports.toString = toString;
const Bytes = require("./Bytes.js");
const Hex = require("./Hex.js");
const internal = require("./internal/base58.js");
function fromBytes(value) {
    return internal.from(value);
}
function fromHex(value) {
    return internal.from(value);
}
function fromString(value) {
    return internal.from(Bytes.fromString(value));
}
function toBytes(value) {
    return Bytes.fromHex(toHex(value));
}
function toHex(value) {
    let integer = BigInt(0);
    let pad = 0;
    let checkPad = true;
    for (let i = 0; i < value.length; i++) {
        const char = value[i];
        if (checkPad && char === '1')
            pad++;
        else
            checkPad = false;
        if (typeof internal.alphabetToInteger[char] !== 'bigint')
            throw new Error('invalid base58 character: ' + char);
        integer = integer * 58n;
        integer = integer + internal.alphabetToInteger[char];
    }
    if (!pad)
        return `0x${integer.toString(16)}`;
    return `0x${'0'.repeat(pad * 2)}${integer.toString(16)}`;
}
function toString(value) {
    return Hex.toString(toHex(value));
}
//# sourceMappingURL=Base58.js.map