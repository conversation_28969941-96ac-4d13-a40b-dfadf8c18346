{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../celo/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAEtC,OAAO,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AACxD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAA;AACvD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AAC3C,OAAO,KAAK,EACV,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,eAAe,EACf,cAAc,IAAI,mBAAmB,EACrC,qBAAqB,IAAI,0BAA0B,EACpD,MAAM,iBAAiB,CAAA;AACxB,OAAO,KAAK,EACV,UAAU,EACV,eAAe,EACf,sBAAsB,EACtB,uBAAuB,EACvB,2BAA2B,EAC3B,qBAAqB,EACrB,WAAW,IAAI,gBAAgB,EAC/B,kBAAkB,IAAI,uBAAuB,EAC9C,MAAM,yBAAyB,CAAA;AAChC,OAAO,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAEpE,OAAO,KAAK,EACV,yBAAyB,EACzB,qBAAqB,EACrB,8BAA8B,EAC9B,4BAA4B,EAC7B,MAAM,kCAAkC,CAAA;AAEzC,MAAM,MAAM,SAAS,CACnB,mBAAmB,SAAS,OAAO,GAAG,OAAO,EAC7C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAClC,MAAM,CACR,KAAK,CACH,MAAM,EACN,mBAAmB,EACnB,QAAQ,EACR,eAAe,CAAC,QAAQ,SAAS,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC,CAC3D,EACD;IACE,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC/B,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC7B,OAAO,CAAC,EAAE,SAAS,CAAA;IACnB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACrB,UAAU,CAAC,EACP;QACE,SAAS,EAAE,GAAG,CAAA;QACd,QAAQ,EAAE,GAAG,CAAA;KACd,GACD,SAAS,CAAA;IACb,MAAM,CAAC,EAAE,SAAS,CAAA;CACnB,CACF,CAAA;AAED,MAAM,MAAM,YAAY,CACtB,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EACpC,mBAAmB,SAAS,OAAO,GAAG,OAAO,IAC3C,MAAM,CACR,QAAQ,CACN,QAAQ,EACR,mBAAmB,EACnB,cAAc,CAAC,QAAQ,SAAS,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC,CAC1D,EACD;IACE,UAAU,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;IAC5B,OAAO,CAAC,EAAE,SAAS,CAAA;IACnB,KAAK,CAAC,EAAE,GAAG,GAAG,IAAI,CAAA;IAClB,QAAQ,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;IAC1B,UAAU,CAAC,EACP;QACE,SAAS,EAAE,GAAG,CAAA;QACd,QAAQ,EAAE,GAAG,CAAA;KACd,GACD,SAAS,CAAA;IACb,MAAM,CAAC,EAAE,SAAS,CAAA;CACnB,CACF,CAAA;AAED,MAAM,MAAM,kBAAkB,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAAI,KAAK,CACvE,cAAc,CAAC,SAAS,CAAC,GACzB,mBAAmB,CAAC,SAAS,CAAC,GAC9B,mBAAmB,CAAC,SAAS,CAAC,GAC9B,qBAAqB,CAAC,SAAS,CAAC,CACnC,CAAA;AAED,MAAM,MAAM,yBAAyB,GAAG,KAAK,CAC3C,qBAAqB,GAAG,0BAA0B,CACnD,CAAA;AAED,MAAM,MAAM,eAAe,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAAI,KAAK,CACpE,WAAW,CAAC,SAAS,CAAC,GACtB,gBAAgB,CAAC,SAAS,CAAC,GAC3B,gBAAgB,CAAC,SAAS,CAAC,GAC3B,yBAAyB,CAAC,SAAS,CAAC,CACvC,CAAA;AAED,MAAM,MAAM,sBAAsB,GAAG,KAAK,CACxC,kBAAkB,GAAG,uBAAuB,CAC7C,CAAA;AAED,MAAM,MAAM,2BAA2B,GAAG,KAAK,CAC3C,uBAAuB,GACvB,4BAA4B,GAC5B,8BAA8B,CACjC,CAAA;AAED,MAAM,MAAM,yBAAyB,CACnC,IAAI,SAAS,mBAAmB,GAAG,mBAAmB,IAEpD,qBAAqB,CAAC,IAAI,CAAC,GAC3B,0BAA0B,GAC1B,0BAA0B,GAC1B,4BAA4B,CAAA;AAEhC,MAAM,MAAM,mBAAmB,GAAG,eAAe,GAAG,OAAO,GAAG,OAAO,CAAA;AAErE,KAAK,cAAc,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IACrD,mBAAmB,CAAC,SAAS,CAAC,GAAG;IAC/B,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;IAC3B,UAAU,EAAE,GAAG,GAAG,IAAI,CAAA;IACtB,mBAAmB,EAAE,OAAO,GAAG,IAAI,CAAA;CACpC,CAAA;AAEH,KAAK,qBAAqB,GAAG,0BAA0B,GAAG;IACxD,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAClC,CAAA;AAED,MAAM,MAAM,mBAAmB,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAAI,IAAI,CACzE,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,EAC3C,SAAS,CACV,GAAG;IACF,UAAU,EAAE,UAAU,CAAA;IACtB,OAAO,EAAE,KAAK,CAAA;IACd,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;IAC3B,UAAU,EAAE,GAAG,GAAG,IAAI,CAAA;IACtB,mBAAmB,EAAE,OAAO,GAAG,IAAI,CAAA;IACnC,IAAI,EAAE,MAAM,CAAA;CACb,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AAE9B,MAAM,MAAM,mBAAmB,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAAI,IAAI,CACzE,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,EAC3C,SAAS,CACV,GAAG;IACF,UAAU,EAAE,UAAU,CAAA;IACtB,OAAO,EAAE,KAAK,CAAA;IACd,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;IAC3B,IAAI,EAAE,MAAM,CAAA;CACb,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AAE9B,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,CAC7D,QAAQ,EACR,KAAK,CACN,GAAG;IACF,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACnC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC1B,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAA;AAE5C,KAAK,WAAW,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAAI,gBAAgB,CACtE,MAAM,EACN,MAAM,EACN,SAAS,CACV,GAAG;IACF,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;CAC5B,CAAA;AAED,MAAM,MAAM,gBAAgB,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAC9D,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,GACxC,gBAAgB,GAAG;IACjB,UAAU,EAAE,UAAU,CAAA;IACtB,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;IAC3B,UAAU,EAAE,MAAM,GAAG,IAAI,CAAA;IACzB,mBAAmB,EAAE,OAAO,GAAG,IAAI,CAAA;IACnC,IAAI,EAAE,OAAO,CAAA;CACd,CAAA;AAEL,MAAM,MAAM,gBAAgB,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO,IAC9D,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,GACxC,gBAAgB,GAAG;IACjB,UAAU,EAAE,UAAU,CAAA;IACtB,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,OAAO,GAAG,IAAI,CAAA;IAC3B,IAAI,EAAE,OAAO,CAAA;CACd,CAAA;AAEL,KAAK,kBAAkB,GAAG,uBAAuB,GAAG;IAClD,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAClC,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG,sBAAsB,GAAG;IAC7D,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACnC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjC,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC3B,GAAG,YAAY,CAAC,gBAAgB,CAAC,CAAA;AAElC,MAAM,MAAM,4BAA4B,CACtC,QAAQ,GAAG,MAAM,EACjB,KAAK,GAAG,MAAM,IACZ,2BAA2B,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG;IACjD,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACnC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjC,mBAAmB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACzC,UAAU,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAA;IACjC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC3B,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAA;AAE5C,MAAM,MAAM,4BAA4B,CACtC,QAAQ,GAAG,MAAM,EACjB,KAAK,GAAG,MAAM,IACZ,2BAA2B,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG;IACjD,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACnC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC3B,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAA;AAE5C,MAAM,MAAM,0BAA0B,GAAG,OAAO,MAAM,EAAE,CAAA;AACxD,MAAM,MAAM,0BAA0B,GAAG,OAAO,MAAM,EAAE,CAAA"}