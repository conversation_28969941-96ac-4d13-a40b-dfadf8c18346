"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.from = from;
exports.fromCreate = fromCreate;
exports.fromCreate2 = fromCreate2;
const Address = require("./Address.js");
const Bytes = require("./Bytes.js");
const Hash = require("./Hash.js");
const Hex = require("./Hex.js");
const Rlp = require("./Rlp.js");
function from(options) {
    if (options.salt)
        return fromCreate2(options);
    return fromCreate(options);
}
function fromCreate(options) {
    const from = Bytes.fromHex(Address.from(options.from));
    let nonce = Bytes.fromNumber(options.nonce);
    if (nonce[0] === 0)
        nonce = new Uint8Array([]);
    return Address.from(`0x${Hash.keccak256(Rlp.fromBytes([from, nonce], { as: 'Hex' })).slice(26)}`);
}
function fromCreate2(options) {
    const from = Bytes.fromHex(Address.from(options.from));
    const salt = Bytes.padLeft(Bytes.validate(options.salt) ? options.salt : Bytes.fromHex(options.salt), 32);
    const bytecodeHash = (() => {
        if ('bytecodeHash' in options) {
            if (Bytes.validate(options.bytecodeHash))
                return options.bytecodeHash;
            return Bytes.fromHex(options.bytecodeHash);
        }
        return Hash.keccak256(options.bytecode, { as: 'Bytes' });
    })();
    return Address.from(Hex.slice(Hash.keccak256(Bytes.concat(Bytes.fromHex('0xff'), from, salt, bytecodeHash), { as: 'Hex' }), 12));
}
//# sourceMappingURL=ContractAddress.js.map