"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toRpc = toRpc;
exports.fromRpc = fromRpc;
const Hex = require("./Hex.js");
const Transaction = require("./Transaction.js");
const Withdrawal = require("./Withdrawal.js");
function toRpc(block, _options = {}) {
    const transactions = block.transactions.map((transaction) => {
        if (typeof transaction === 'string')
            return transaction;
        return Transaction.toRpc(transaction);
    });
    return {
        baseFeePerGas: typeof block.baseFeePerGas === 'bigint'
            ? Hex.fromNumber(block.baseFeePerGas)
            : undefined,
        blobGasUsed: typeof block.blobGasUsed === 'bigint'
            ? Hex.fromNumber(block.blobGasUsed)
            : undefined,
        excessBlobGas: typeof block.excessBlobGas === 'bigint'
            ? Hex.fromNumber(block.excessBlobGas)
            : undefined,
        extraData: block.extraData,
        difficulty: typeof block.difficulty === 'bigint'
            ? Hex.fromNumber(block.difficulty)
            : undefined,
        gasLimit: Hex.fromNumber(block.gasLimit),
        gasUsed: Hex.fromNumber(block.gasUsed),
        hash: block.hash,
        logsBloom: block.logsBloom,
        miner: block.miner,
        mixHash: block.mixHash,
        nonce: block.nonce,
        number: (typeof block.number === 'bigint'
            ? Hex.fromNumber(block.number)
            : null),
        parentBeaconBlockRoot: block.parentBeaconBlockRoot,
        parentHash: block.parentHash,
        receiptsRoot: block.receiptsRoot,
        sealFields: block.sealFields,
        sha3Uncles: block.sha3Uncles,
        size: Hex.fromNumber(block.size),
        stateRoot: block.stateRoot,
        timestamp: Hex.fromNumber(block.timestamp),
        totalDifficulty: typeof block.totalDifficulty === 'bigint'
            ? Hex.fromNumber(block.totalDifficulty)
            : undefined,
        transactions,
        transactionsRoot: block.transactionsRoot,
        uncles: block.uncles,
        withdrawals: block.withdrawals?.map(Withdrawal.toRpc),
        withdrawalsRoot: block.withdrawalsRoot,
    };
}
function fromRpc(block, _options = {}) {
    if (!block)
        return null;
    const transactions = block.transactions.map((transaction) => {
        if (typeof transaction === 'string')
            return transaction;
        return Transaction.fromRpc(transaction);
    });
    return {
        ...block,
        baseFeePerGas: block.baseFeePerGas
            ? BigInt(block.baseFeePerGas)
            : undefined,
        blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : undefined,
        difficulty: block.difficulty ? BigInt(block.difficulty) : undefined,
        excessBlobGas: block.excessBlobGas
            ? BigInt(block.excessBlobGas)
            : undefined,
        gasLimit: BigInt(block.gasLimit ?? 0n),
        gasUsed: BigInt(block.gasUsed ?? 0n),
        number: block.number ? BigInt(block.number) : null,
        size: BigInt(block.size ?? 0n),
        stateRoot: block.stateRoot,
        timestamp: BigInt(block.timestamp ?? 0n),
        totalDifficulty: BigInt(block.totalDifficulty ?? 0n),
        transactions,
        withdrawals: block.withdrawals?.map(Withdrawal.fromRpc),
    };
}
//# sourceMappingURL=Block.js.map