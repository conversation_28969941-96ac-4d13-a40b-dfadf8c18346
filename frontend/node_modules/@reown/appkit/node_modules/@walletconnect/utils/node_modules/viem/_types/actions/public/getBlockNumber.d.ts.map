{"version": 3, "file": "getBlockNumber.d.ts", "sourceRoot": "", "sources": ["../../../actions/public/getBlockNumber.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,6CAA6C,CAAA;AAC5E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAA;AACnE,OAAO,EACL,KAAK,iBAAiB,EAGvB,MAAM,kCAAkC,CAAA;AAEzC,MAAM,MAAM,wBAAwB,GAAG;IACrC,mEAAmE;IACnE,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC/B,CAAA;AAED,MAAM,MAAM,wBAAwB,GAAG,MAAM,CAAA;AAE7C,MAAM,MAAM,uBAAuB,GAAG,gBAAgB,GAAG,SAAS,CAAA;AAIlE,gBAAgB;AAChB,MAAM,MAAM,4BAA4B,GAAG,iBAAiB,GAAG,SAAS,CAAA;AAExE,gBAAgB;AAChB,wBAAgB,mBAAmB,CAAC,EAAE,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;EAE7C;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAsB,cAAc,CAAC,KAAK,SAAS,KAAK,GAAG,SAAS,EAClE,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAChC,EAAE,SAA4B,EAAE,GAAE,wBAA6B,GAC9D,OAAO,CAAC,wBAAwB,CAAC,CASnC"}