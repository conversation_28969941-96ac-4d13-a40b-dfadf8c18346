"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decodeData = decodeData;
exports.decodeResult = decodeResult;
exports.encodeData = encodeData;
exports.encodeResult = encodeResult;
exports.format = format;
exports.from = from;
exports.fromAbi = fromAbi;
exports.getSelector = getSelector;
const abitype = require("abitype");
const AbiItem = require("./AbiItem.js");
const AbiParameters = require("./AbiParameters.js");
const Hex = require("./Hex.js");
function decodeData(abiFunction, data) {
    const { overloads } = abiFunction;
    if (Hex.size(data) < 4)
        throw new AbiItem.InvalidSelectorSizeError({ data });
    if (abiFunction.inputs.length === 0)
        return undefined;
    const item = overloads
        ? fromAbi([abiFunction, ...overloads], data)
        : abiFunction;
    if (Hex.size(data) <= 4)
        return undefined;
    return AbiParameters.decode(item.inputs, Hex.slice(data, 4));
}
function decodeResult(abiFunction, data, options = {}) {
    const values = AbiParameters.decode(abiFunction.outputs, data, options);
    if (values && Object.keys(values).length === 0)
        return undefined;
    if (values && Object.keys(values).length === 1) {
        if (Array.isArray(values))
            return values[0];
        return Object.values(values)[0];
    }
    return values;
}
function encodeData(abiFunction, ...args) {
    const { overloads } = abiFunction;
    const item = overloads
        ? fromAbi([abiFunction, ...overloads], abiFunction.name, {
            args: args[0],
        })
        : abiFunction;
    const selector = getSelector(item);
    const data = args.length > 0
        ? AbiParameters.encode(item.inputs, args[0])
        : undefined;
    return data ? Hex.concat(selector, data) : selector;
}
function encodeResult(abiFunction, output, options = {}) {
    const { as = 'Array' } = options;
    const values = (() => {
        if (abiFunction.outputs.length === 1)
            return [output];
        if (Array.isArray(output))
            return output;
        if (as === 'Object')
            return Object.values(output);
        return [output];
    })();
    return AbiParameters.encode(abiFunction.outputs, values);
}
function format(abiFunction) {
    return abitype.formatAbiItem(abiFunction);
}
function from(abiFunction, options = {}) {
    return AbiItem.from(abiFunction, options);
}
function fromAbi(abi, name, options) {
    const item = AbiItem.fromAbi(abi, name, options);
    if (item.type !== 'function')
        throw new AbiItem.NotFoundError({ name, type: 'function' });
    return item;
}
function getSelector(abiItem) {
    return AbiItem.getSelector(abiItem);
}
//# sourceMappingURL=AbiFunction.js.map