import type { Address } from 'abitype';
import type { Account } from '../../accounts/types.js';
import { type ParseAccountErrorType } from '../../accounts/utils/parseAccount.js';
import { type EstimateFeesPerGasErrorType } from '../../actions/public/estimateFeesPerGas.js';
import { type EstimateGasErrorType } from '../../actions/public/estimateGas.js';
import { type GetBlockErrorType } from '../../actions/public/getBlock.js';
import { type GetTransactionCountErrorType } from '../../actions/public/getTransactionCount.js';
import type { Client } from '../../clients/createClient.js';
import type { Transport } from '../../clients/transports/createTransport.js';
import type { AccountNotFoundErrorType } from '../../errors/account.js';
import type { DeriveAccount, GetAccountParameter } from '../../types/account.js';
import type { Chain, DeriveChain } from '../../types/chain.js';
import type { GetChainParameter } from '../../types/chain.js';
import type { GetTransactionRequestKzgParameter } from '../../types/kzg.js';
import type { TransactionRequest, TransactionRequestEIP1559, TransactionRequestEIP2930, TransactionRequestEIP4844, TransactionRequestEIP7702, TransactionRequestLegacy } from '../../types/transaction.js';
import type { ExactPartial, IsNever, Prettify, UnionOmit, UnionRequiredBy } from '../../types/utils.js';
import type { FormattedTransactionRequest } from '../../utils/formatters/transactionRequest.js';
import type { NonceManager } from '../../utils/nonceManager.js';
import { type AssertRequestErrorType } from '../../utils/transaction/assertRequest.js';
import { type GetTransactionType } from '../../utils/transaction/getTransactionType.js';
export declare const defaultParameters: readonly ["blobVersionedHashes", "chainId", "fees", "gas", "nonce", "type"];
/** @internal */
export declare const eip1559NetworkCache: Map<string, boolean>;
export type PrepareTransactionRequestParameterType = 'blobVersionedHashes' | 'chainId' | 'fees' | 'gas' | 'nonce' | 'sidecars' | 'type';
type ParameterTypeToParameters<parameterType extends PrepareTransactionRequestParameterType> = parameterType extends 'fees' ? 'maxFeePerGas' | 'maxPriorityFeePerGas' | 'gasPrice' : parameterType;
export type PrepareTransactionRequestRequest<chain extends Chain | undefined = Chain | undefined, chainOverride extends Chain | undefined = Chain | undefined, _derivedChain extends Chain | undefined = DeriveChain<chain, chainOverride>> = UnionOmit<FormattedTransactionRequest<_derivedChain>, 'from'> & GetTransactionRequestKzgParameter & {
    /**
     * Nonce manager to use for the transaction request.
     */
    nonceManager?: NonceManager | undefined;
    /**
     * Parameters to prepare for the transaction request.
     *
     * @default ['blobVersionedHashes', 'chainId', 'fees', 'gas', 'nonce', 'type']
     */
    parameters?: readonly PrepareTransactionRequestParameterType[] | undefined;
};
export type PrepareTransactionRequestParameters<chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined, chainOverride extends Chain | undefined = Chain | undefined, accountOverride extends Account | Address | undefined = Account | Address | undefined, request extends PrepareTransactionRequestRequest<chain, chainOverride> = PrepareTransactionRequestRequest<chain, chainOverride>> = request & GetAccountParameter<account, accountOverride, false, true> & GetChainParameter<chain, chainOverride> & GetTransactionRequestKzgParameter<request> & {
    chainId?: number | undefined;
};
export type PrepareTransactionRequestReturnType<chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined, chainOverride extends Chain | undefined = Chain | undefined, accountOverride extends Account | Address | undefined = Account | Address | undefined, request extends PrepareTransactionRequestRequest<chain, chainOverride> = PrepareTransactionRequestRequest<chain, chainOverride>, _derivedAccount extends Account | Address | undefined = DeriveAccount<account, accountOverride>, _derivedChain extends Chain | undefined = DeriveChain<chain, chainOverride>, _transactionType = request['type'] extends string | undefined ? request['type'] : GetTransactionType<request> extends 'legacy' ? unknown : GetTransactionType<request>, _transactionRequest extends TransactionRequest = (_transactionType extends 'legacy' ? TransactionRequestLegacy : never) | (_transactionType extends 'eip1559' ? TransactionRequestEIP1559 : never) | (_transactionType extends 'eip2930' ? TransactionRequestEIP2930 : never) | (_transactionType extends 'eip4844' ? TransactionRequestEIP4844 : never) | (_transactionType extends 'eip7702' ? TransactionRequestEIP7702 : never)> = Prettify<UnionRequiredBy<Extract<UnionOmit<FormattedTransactionRequest<_derivedChain>, 'from'> & (_derivedChain extends Chain ? {
    chain: _derivedChain;
} : {
    chain?: undefined;
}) & (_derivedAccount extends Account ? {
    account: _derivedAccount;
    from: Address;
} : {
    account?: undefined;
    from?: undefined;
}), IsNever<_transactionRequest> extends true ? unknown : ExactPartial<_transactionRequest>> & {
    chainId?: number | undefined;
}, ParameterTypeToParameters<request['parameters'] extends readonly PrepareTransactionRequestParameterType[] ? request['parameters'][number] : (typeof defaultParameters)[number]>> & (unknown extends request['kzg'] ? {} : Pick<request, 'kzg'>)>;
export type PrepareTransactionRequestErrorType = AccountNotFoundErrorType | AssertRequestErrorType | ParseAccountErrorType | GetBlockErrorType | GetTransactionCountErrorType | EstimateGasErrorType | EstimateFeesPerGasErrorType;
/**
 * Prepares a transaction request for signing.
 *
 * - Docs: https://viem.sh/docs/actions/wallet/prepareTransactionRequest
 *
 * @param args - {@link PrepareTransactionRequestParameters}
 * @returns The transaction request. {@link PrepareTransactionRequestReturnType}
 *
 * @example
 * import { createWalletClient, custom } from 'viem'
 * import { mainnet } from 'viem/chains'
 * import { prepareTransactionRequest } from 'viem/actions'
 *
 * const client = createWalletClient({
 *   chain: mainnet,
 *   transport: custom(window.ethereum),
 * })
 * const request = await prepareTransactionRequest(client, {
 *   account: '******************************************',
 *   to: '******************************************',
 *   value: 1n,
 * })
 *
 * @example
 * // Account Hoisting
 * import { createWalletClient, http } from 'viem'
 * import { privateKeyToAccount } from 'viem/accounts'
 * import { mainnet } from 'viem/chains'
 * import { prepareTransactionRequest } from 'viem/actions'
 *
 * const client = createWalletClient({
 *   account: privateKeyToAccount('0x…'),
 *   chain: mainnet,
 *   transport: custom(window.ethereum),
 * })
 * const request = await prepareTransactionRequest(client, {
 *   to: '******************************************',
 *   value: 1n,
 * })
 */
export declare function prepareTransactionRequest<chain extends Chain | undefined, account extends Account | undefined, const request extends PrepareTransactionRequestRequest<chain, chainOverride>, accountOverride extends Account | Address | undefined = undefined, chainOverride extends Chain | undefined = undefined>(client: Client<Transport, chain, account>, args: PrepareTransactionRequestParameters<chain, account, chainOverride, accountOverride, request>): Promise<PrepareTransactionRequestReturnType<chain, account, chainOverride, accountOverride, request>>;
export {};
//# sourceMappingURL=prepareTransactionRequest.d.ts.map