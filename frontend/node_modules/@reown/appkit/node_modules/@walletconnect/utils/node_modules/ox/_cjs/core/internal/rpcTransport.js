"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.create = create;
const RpcRequest = require("../RpcRequest.js");
const RpcResponse = require("../RpcResponse.js");
function create(transport, options_root) {
    const requestStore = RpcRequest.createStore();
    return {
        request: async ({ method, params }, options = {}) => {
            const body = requestStore.prepare({ method, params });
            const data = await transport.request(body, options);
            return RpcResponse.parse(data, {
                raw: options.raw ?? options_root?.raw,
            });
        },
    };
}
//# sourceMappingURL=rpcTransport.js.map