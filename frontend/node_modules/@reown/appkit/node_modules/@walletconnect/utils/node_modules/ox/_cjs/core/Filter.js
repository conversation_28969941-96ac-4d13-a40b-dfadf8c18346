"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromRpc = fromRpc;
exports.toRpc = toRpc;
const Hex = require("./Hex.js");
function fromRpc(filter) {
    const { fromBlock, toBlock } = filter;
    return {
        ...filter,
        ...(fromBlock && {
            fromBlock: Hex.validate(fromBlock, { strict: false })
                ? BigInt(fromBlock)
                : fromBlock,
        }),
        ...(toBlock && {
            toBlock: Hex.validate(toBlock, { strict: false })
                ? BigInt(toBlock)
                : toBlock,
        }),
    };
}
function toRpc(filter) {
    const { address, topics, fromBlock, toBlock } = filter;
    return {
        ...(address && { address }),
        ...(topics && { topics }),
        ...(typeof fromBlock !== 'undefined'
            ? {
                fromBlock: typeof fromBlock === 'bigint'
                    ? Hex.fromNumber(fromBlock)
                    : fromBlock,
            }
            : {}),
        ...(typeof toBlock !== 'undefined'
            ? {
                toBlock: typeof toBlock === 'bigint' ? Hex.fromNumber(toBlock) : toBlock,
            }
            : {}),
    };
}
//# sourceMappingURL=Filter.js.map