"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.contains = contains;
exports.validate = validate;
const Bytes = require("./Bytes.js");
const Hash = require("./Hash.js");
const Hex = require("./Hex.js");
function contains(bloom, input) {
    const filter = Bytes.fromHex(bloom);
    const hash = Hash.keccak256(input, { as: 'Bytes' });
    for (const i of [0, 2, 4]) {
        const bit = (hash[i + 1] + (hash[i] << 8)) & 0x7ff;
        if ((filter[256 - 1 - Math.floor(bit / 8)] & (1 << (bit % 8))) === 0)
            return false;
    }
    return true;
}
function validate(value) {
    return Hex.validate(value) && Hex.size(value) === 256;
}
//# sourceMappingURL=Bloom.js.map