import type { Client } from '../../clients/createClient.js';
import type { Transport } from '../../clients/transports/createTransport.js';
import type { ErrorType } from '../../errors/utils.js';
import type { BlockTag } from '../../types/block.js';
import type { Chain } from '../../types/chain.js';
import type { Hash } from '../../types/misc.js';
import type { RequestErrorType } from '../../utils/buildRequest.js';
import { type HexToNumberErrorType } from '../../utils/encoding/fromHex.js';
import { type NumberToHexErrorType } from '../../utils/encoding/toHex.js';
export type GetBlockTransactionCountParameters = {
    /** Hash of the block. */
    blockHash?: Hash | undefined;
    blockNumber?: undefined;
    blockTag?: undefined;
} | {
    blockHash?: undefined;
    /** The block number. */
    blockNumber?: bigint | undefined;
    blockTag?: undefined;
} | {
    blockHash?: undefined;
    blockNumber?: undefined;
    /** The block tag. Defaults to 'latest'. */
    blockTag?: BlockTag | undefined;
};
export type GetBlockTransactionCountReturnType = number;
export type GetBlockTransactionCountErrorType = NumberToHexErrorType | HexToNumberErrorType | RequestErrorType | ErrorType;
/**
 * Returns the number of Transactions at a block number, hash, or tag.
 *
 * - Docs: https://viem.sh/docs/actions/public/getBlockTransactionCount
 * - JSON-RPC Methods:
 *   - Calls [`eth_getBlockTransactionCountByNumber`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_getblocktransactioncountbynumber) for `blockNumber` & `blockTag`.
 *   - Calls [`eth_getBlockTransactionCountByHash`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_getblocktransactioncountbyhash) for `blockHash`.
 *
 * @param client - Client to use
 * @param parameters - {@link GetBlockTransactionCountParameters}
 * @returns The block transaction count. {@link GetBlockTransactionCountReturnType}
 *
 * @example
 * import { createPublicClient, http } from 'viem'
 * import { mainnet } from 'viem/chains'
 * import { getBlockTransactionCount } from 'viem/public'
 *
 * const client = createPublicClient({
 *   chain: mainnet,
 *   transport: http(),
 * })
 * const count = await getBlockTransactionCount(client)
 */
export declare function getBlockTransactionCount<chain extends Chain | undefined>(client: Client<Transport, chain>, { blockHash, blockNumber, blockTag, }?: GetBlockTransactionCountParameters): Promise<GetBlockTransactionCountReturnType>;
//# sourceMappingURL=getBlockTransactionCount.d.ts.map