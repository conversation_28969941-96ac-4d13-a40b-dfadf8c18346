import type { Address } from 'abitype';
import type { TestClient, TestClientMode } from '../../clients/createTestClient.js';
import type { Transport } from '../../clients/transports/createTransport.js';
import type { ErrorType } from '../../errors/utils.js';
import type { Account } from '../../types/account.js';
import type { Chain } from '../../types/chain.js';
import type { RequestErrorType } from '../../utils/buildRequest.js';
export type SetBalanceParameters = {
    /** The account address. */
    address: Address;
    /** Amount (in wei) to set */
    value: bigint;
};
export type SetBalanceErrorType = RequestErrorType | ErrorType;
/**
 * Modifies the balance of an account.
 *
 * - Docs: https://viem.sh/docs/actions/test/setBalance
 *
 * @param client - Client to use
 * @param parameters – {@link SetBalanceParameters}
 *
 * @example
 * import { createTestClient, http, parseEther } from 'viem'
 * import { foundry } from 'viem/chains'
 * import { setBalance } from 'viem/test'
 *
 * const client = createTestClient({
 *   mode: 'anvil',
 *   chain: 'foundry',
 *   transport: http(),
 * })
 * await setBalance(client, {
 *   address: '******************************************',
 *   value: parseEther('1'),
 * })
 */
export declare function setBalance<chain extends Chain | undefined, account extends Account | undefined>(client: TestClient<TestClientMode, Transport, chain, account, false>, { address, value }: SetBalanceParameters): Promise<void>;
//# sourceMappingURL=setBalance.d.ts.map