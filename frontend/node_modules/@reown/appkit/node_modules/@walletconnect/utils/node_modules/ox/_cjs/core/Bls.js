"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.noble = void 0;
exports.aggregate = aggregate;
exports.getPublicKey = getPublicKey;
exports.randomPrivateKey = randomPrivateKey;
exports.sign = sign;
exports.verify = verify;
const bls12_381_1 = require("@noble/curves/bls12-381");
const Bytes = require("./Bytes.js");
const Hex = require("./Hex.js");
exports.noble = bls12_381_1.bls12_381;
function aggregate(points) {
    const group = typeof points[0]?.x === 'bigint' ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    const point = points.reduce((acc, point) => acc.add(new group.ProjectivePoint(point.x, point.y, point.z)), group.ProjectivePoint.ZERO);
    return {
        x: point.px,
        y: point.py,
        z: point.pz,
    };
}
function getPublicKey(options) {
    const { privateKey, size = 'short-key:long-sig' } = options;
    const group = size === 'short-key:long-sig' ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    const { px, py, pz } = group.ProjectivePoint.fromPrivateKey(Hex.from(privateKey).slice(2));
    return { x: px, y: py, z: pz };
}
function randomPrivateKey(options = {}) {
    const { as = 'Hex' } = options;
    const bytes = bls12_381_1.bls12_381.utils.randomPrivateKey();
    if (as === 'Hex')
        return Hex.fromBytes(bytes);
    return bytes;
}
function sign(options) {
    const { payload, privateKey, suite, size = 'short-key:long-sig' } = options;
    const payloadGroup = size === 'short-key:long-sig' ? bls12_381_1.bls12_381.G2 : bls12_381_1.bls12_381.G1;
    const payloadPoint = payloadGroup.hashToCurve(Bytes.from(payload), suite ? { DST: Bytes.fromString(suite) } : undefined);
    const privateKeyGroup = size === 'short-key:long-sig' ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    const signature = payloadPoint.multiply(privateKeyGroup.normPrivateKeyToScalar(privateKey.slice(2)));
    return {
        x: signature.px,
        y: signature.py,
        z: signature.pz,
    };
}
function verify(options) {
    const { payload, suite } = options;
    const publicKey = options.publicKey;
    const signature = options.signature;
    const isShortSig = typeof signature.x === 'bigint';
    const group = isShortSig ? bls12_381_1.bls12_381.G1 : bls12_381_1.bls12_381.G2;
    const payloadPoint = group.hashToCurve(Bytes.from(payload), suite ? { DST: Bytes.fromString(suite) } : undefined);
    const shortSigPairing = () => bls12_381_1.bls12_381.pairingBatch([
        {
            g1: payloadPoint,
            g2: new bls12_381_1.bls12_381.G2.ProjectivePoint(publicKey.x, publicKey.y, publicKey.z),
        },
        {
            g1: new bls12_381_1.bls12_381.G1.ProjectivePoint(signature.x, signature.y, signature.z),
            g2: bls12_381_1.bls12_381.G2.ProjectivePoint.BASE.negate(),
        },
    ]);
    const longSigPairing = () => bls12_381_1.bls12_381.pairingBatch([
        {
            g1: new bls12_381_1.bls12_381.G1.ProjectivePoint(publicKey.x, publicKey.y, publicKey.z).negate(),
            g2: payloadPoint,
        },
        {
            g1: bls12_381_1.bls12_381.G1.ProjectivePoint.BASE,
            g2: new bls12_381_1.bls12_381.G2.ProjectivePoint(signature.x, signature.y, signature.z),
        },
    ]);
    return bls12_381_1.bls12_381.fields.Fp12.eql(isShortSig ? shortSigPairing() : longSigPairing(), bls12_381_1.bls12_381.fields.Fp12.ONE);
}
//# sourceMappingURL=Bls.js.map