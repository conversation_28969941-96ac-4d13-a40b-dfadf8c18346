"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.traditionalChinese = exports.spanish = exports.simplifiedChinese = exports.portuguese = exports.korean = exports.japanese = exports.italian = exports.french = exports.czech = exports.english = exports.path = void 0;
exports.random = random;
exports.toHdKey = toHdKey;
exports.toPrivateKey = toPrivateKey;
exports.toSeed = toSeed;
exports.validate = validate;
const bip39_1 = require("@scure/bip39");
const Bytes = require("./Bytes.js");
const HdKey = require("./HdKey.js");
var HdKey_js_1 = require("./HdKey.js");
Object.defineProperty(exports, "path", { enumerable: true, get: function () { return HdKey_js_1.path; } });
var wordlists_js_1 = require("./internal/mnemonic/wordlists.js");
Object.defineProperty(exports, "english", { enumerable: true, get: function () { return wordlists_js_1.english; } });
Object.defineProperty(exports, "czech", { enumerable: true, get: function () { return wordlists_js_1.czech; } });
Object.defineProperty(exports, "french", { enumerable: true, get: function () { return wordlists_js_1.french; } });
Object.defineProperty(exports, "italian", { enumerable: true, get: function () { return wordlists_js_1.italian; } });
Object.defineProperty(exports, "japanese", { enumerable: true, get: function () { return wordlists_js_1.japanese; } });
Object.defineProperty(exports, "korean", { enumerable: true, get: function () { return wordlists_js_1.korean; } });
Object.defineProperty(exports, "portuguese", { enumerable: true, get: function () { return wordlists_js_1.portuguese; } });
Object.defineProperty(exports, "simplifiedChinese", { enumerable: true, get: function () { return wordlists_js_1.simplifiedChinese; } });
Object.defineProperty(exports, "spanish", { enumerable: true, get: function () { return wordlists_js_1.spanish; } });
Object.defineProperty(exports, "traditionalChinese", { enumerable: true, get: function () { return wordlists_js_1.traditionalChinese; } });
function random(wordlist, options = {}) {
    const { strength = 128 } = options;
    return (0, bip39_1.generateMnemonic)(wordlist, strength);
}
function toHdKey(mnemonic, options = {}) {
    const { passphrase } = options;
    const seed = toSeed(mnemonic, { passphrase });
    return HdKey.fromSeed(seed);
}
function toPrivateKey(mnemonic, options = {}) {
    const { path = HdKey.path(), passphrase } = options;
    const hdKey = toHdKey(mnemonic, { passphrase }).derive(path);
    if (options.as === 'Bytes')
        return Bytes.from(hdKey.privateKey);
    return hdKey.privateKey;
}
function toSeed(mnemonic, options = {}) {
    const { passphrase } = options;
    const seed = (0, bip39_1.mnemonicToSeedSync)(mnemonic, passphrase);
    if (options.as === 'Hex')
        return Bytes.toHex(seed);
    return seed;
}
function validate(mnemonic, wordlist) {
    return (0, bip39_1.validateMnemonic)(mnemonic, wordlist);
}
//# sourceMappingURL=Mnemonic.js.map