'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const zipWith = require('../_chunk/zipWith-Bdyzuy.js');
const AbortError = require('../_chunk/AbortError-Cg4ZQ1.js');
const error_index = require('../error/index.js');
const unary = require('../_chunk/unary-c1NFA5.js');
const noop = require('../_chunk/noop-2IwLUk.js');
const range$1 = require('../_chunk/range-HnEIT7.js');
const randomInt = require('../_chunk/randomInt-CF7bZK.js');
const toMerged = require('../_chunk/toMerged-CwnQF6.js');
const isPlainObject$1 = require('../_chunk/isPlainObject-Xaozpc.js');
const isWeakSet$1 = require('../_chunk/isWeakSet-TIM260.js');
const promise_index = require('../promise/index.js');
const upperFirst$1 = require('../_chunk/upperFirst-nA5L7X.js');
const util_index = require('../util/index.js');

function castArray(value) {
    if (arguments.length === 0) {
        return [];
    }
    return Array.isArray(value) ? value : [value];
}

function toArray$1(value) {
    return Array.isArray(value) ? value : Array.from(value);
}

function isArrayLike(value) {
    return value != null && typeof value !== 'function' && isWeakSet$1.isLength(value.length);
}

function chunk(arr, size = 1) {
    size = Math.max(Math.floor(size), 0);
    if (size === 0 || !isArrayLike(arr)) {
        return [];
    }
    return zipWith.chunk(toArray$1(arr), size);
}

function compact(arr) {
    if (!isArrayLike(arr)) {
        return [];
    }
    return zipWith.compact(Array.from(arr));
}

function concat(...values) {
    return zipWith.flatten(values);
}

function isArrayLikeObject(value) {
    return toMerged.isObjectLike(value) && isArrayLike(value);
}

function difference(arr, ...values) {
    if (!isArrayLikeObject(arr)) {
        return [];
    }
    const arr1 = toArray$1(arr);
    const arr2 = [];
    for (let i = 0; i < values.length; i++) {
        const value = values[i];
        if (isArrayLikeObject(value)) {
            arr2.push(...Array.from(value));
        }
    }
    return zipWith.difference(arr1, arr2);
}

function last(array) {
    if (!isArrayLike(array)) {
        return undefined;
    }
    return zipWith.last(toArray$1(array));
}

function flattenArrayLike(values) {
    const result = [];
    for (let i = 0; i < values.length; i++) {
        const arrayLike = values[i];
        if (!isArrayLikeObject(arrayLike)) {
            continue;
        }
        for (let j = 0; j < arrayLike.length; j++) {
            result.push(arrayLike[j]);
        }
    }
    return result;
}

function isDeepKey(key) {
    switch (typeof key) {
        case 'number':
        case 'symbol': {
            return false;
        }
        case 'string': {
            return key.includes('.') || key.includes('[') || key.includes(']');
        }
    }
}

function toKey(value) {
    if (Object.is(value, -0)) {
        return '-0';
    }
    return value.toString();
}

function toPath(deepKey) {
    const result = [];
    const length = deepKey.length;
    if (length === 0) {
        return result;
    }
    let index = 0;
    let key = '';
    let quoteChar = '';
    let bracket = false;
    if (deepKey.charCodeAt(0) === 46) {
        result.push('');
        index++;
    }
    while (index < length) {
        const char = deepKey[index];
        if (quoteChar) {
            if (char === '\\' && index + 1 < length) {
                index++;
                key += deepKey[index];
            }
            else if (char === quoteChar) {
                quoteChar = '';
            }
            else {
                key += char;
            }
        }
        else if (bracket) {
            if (char === '"' || char === "'") {
                quoteChar = char;
            }
            else if (char === ']') {
                bracket = false;
                result.push(key);
                key = '';
            }
            else {
                key += char;
            }
        }
        else {
            if (char === '[') {
                bracket = true;
                if (key) {
                    result.push(key);
                    key = '';
                }
            }
            else if (char === '.') {
                if (key) {
                    result.push(key);
                    key = '';
                }
            }
            else {
                key += char;
            }
        }
        index++;
    }
    if (key) {
        result.push(key);
    }
    return result;
}

function get(object, path, defaultValue) {
    if (object == null) {
        return defaultValue;
    }
    switch (typeof path) {
        case 'string': {
            const result = object[path];
            if (result === undefined) {
                if (isDeepKey(path)) {
                    return get(object, toPath(path), defaultValue);
                }
                else {
                    return defaultValue;
                }
            }
            return result;
        }
        case 'number':
        case 'symbol': {
            if (typeof path === 'number') {
                path = toKey(path);
            }
            const result = object[path];
            if (result === undefined) {
                return defaultValue;
            }
            return result;
        }
        default: {
            if (Array.isArray(path)) {
                return getWithPath(object, path, defaultValue);
            }
            if (Object.is(path?.valueOf(), -0)) {
                path = '-0';
            }
            else {
                path = String(path);
            }
            const result = object[path];
            if (result === undefined) {
                return defaultValue;
            }
            return result;
        }
    }
}
function getWithPath(object, path, defaultValue) {
    if (path.length === 0) {
        return defaultValue;
    }
    let current = object;
    for (let index = 0; index < path.length; index++) {
        if (current == null) {
            return defaultValue;
        }
        current = current[path[index]];
    }
    if (current === undefined) {
        return defaultValue;
    }
    return current;
}

function property(path) {
    return function (object) {
        return get(object, path);
    };
}

function isObject(value) {
    return value !== null && (typeof value === 'object' || typeof value === 'function');
}

function isMatch(target, source) {
    if (source === target) {
        return true;
    }
    switch (typeof source) {
        case 'object': {
            if (source == null) {
                return true;
            }
            const keys = Object.keys(source);
            if (target == null) {
                return keys.length === 0;
            }
            if (Array.isArray(source)) {
                return isArrayMatch(target, source);
            }
            if (source instanceof Map) {
                return isMapMatch(target, source);
            }
            if (source instanceof Set) {
                return isSetMatch(target, source);
            }
            for (let i = 0; i < keys.length; i++) {
                const key = keys[i];
                if (!isPlainObject$1.isPrimitive(target) && !(key in target)) {
                    return false;
                }
                if (source[key] === undefined && target[key] !== undefined) {
                    return false;
                }
                if (source[key] === null && target[key] !== null) {
                    return false;
                }
                if (!isMatch(target[key], source[key])) {
                    return false;
                }
            }
            return true;
        }
        case 'function': {
            if (Object.keys(source).length > 0) {
                return isMatch(target, { ...source });
            }
            return false;
        }
        default: {
            if (!isObject(target)) {
                return isWeakSet$1.eq(target, source);
            }
            return !source;
        }
    }
}
function isMapMatch(target, source) {
    if (source.size === 0) {
        return true;
    }
    if (!(target instanceof Map)) {
        return false;
    }
    for (const [key, value] of source.entries()) {
        if (!isMatch(target.get(key), value)) {
            return false;
        }
    }
    return true;
}
function isArrayMatch(target, source) {
    if (source.length === 0) {
        return true;
    }
    if (!Array.isArray(target)) {
        return false;
    }
    const countedIndex = new Set();
    for (let i = 0; i < source.length; i++) {
        const sourceItem = source[i];
        const index = target.findIndex((targetItem, index) => {
            return isMatch(targetItem, sourceItem) && !countedIndex.has(index);
        });
        if (index === -1) {
            return false;
        }
        countedIndex.add(index);
    }
    return true;
}
function isSetMatch(target, source) {
    if (source.size === 0) {
        return true;
    }
    if (!(target instanceof Set)) {
        return false;
    }
    return isArrayMatch([...target], [...source]);
}

function matches(source) {
    source = toMerged.cloneDeep(source);
    return (target) => {
        return isMatch(target, source);
    };
}

function cloneDeepWith(obj, cloneValue) {
    return toMerged.cloneDeepWith(obj, (value, key, object, stack) => {
        const cloned = cloneValue?.(value, key, object, stack);
        if (cloned != null) {
            return cloned;
        }
        if (typeof obj !== 'object') {
            return undefined;
        }
        switch (Object.prototype.toString.call(obj)) {
            case isPlainObject$1.numberTag:
            case isPlainObject$1.stringTag:
            case isPlainObject$1.booleanTag: {
                const result = new obj.constructor(obj?.valueOf());
                toMerged.copyProperties(result, obj);
                return result;
            }
            case isPlainObject$1.argumentsTag: {
                const result = {};
                toMerged.copyProperties(result, obj);
                result.length = obj.length;
                result[Symbol.iterator] = obj[Symbol.iterator];
                return result;
            }
            default: {
                return undefined;
            }
        }
    });
}

function cloneDeep(obj) {
    return cloneDeepWith(obj);
}

const IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\d*)$/;
function isIndex(value, length = Number.MAX_SAFE_INTEGER) {
    switch (typeof value) {
        case 'number': {
            return Number.isInteger(value) && value >= 0 && value < length;
        }
        case 'symbol': {
            return false;
        }
        case 'string': {
            return IS_UNSIGNED_INTEGER.test(value);
        }
    }
}

function isArguments(value) {
    return value !== null && typeof value === 'object' && isPlainObject$1.getTag(value) === '[object Arguments]';
}

function has(object, path) {
    let resolvedPath;
    if (Array.isArray(path)) {
        resolvedPath = path;
    }
    else if (typeof path === 'string' && isDeepKey(path) && object?.[path] == null) {
        resolvedPath = toPath(path);
    }
    else {
        resolvedPath = [path];
    }
    if (resolvedPath.length === 0) {
        return false;
    }
    let current = object;
    for (let i = 0; i < resolvedPath.length; i++) {
        const key = resolvedPath[i];
        if (current == null || !Object.hasOwn(current, key)) {
            const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
            if (!isSparseIndex) {
                return false;
            }
        }
        current = current[key];
    }
    return true;
}

function matchesProperty(property, source) {
    switch (typeof property) {
        case 'object': {
            if (Object.is(property?.valueOf(), -0)) {
                property = '-0';
            }
            break;
        }
        case 'number': {
            property = toKey(property);
            break;
        }
    }
    source = cloneDeep(source);
    return function (target) {
        const result = get(target, property);
        if (result === undefined) {
            return has(target, property);
        }
        if (source === undefined) {
            return result === undefined;
        }
        return isMatch(result, source);
    };
}

function iteratee(value) {
    if (value == null) {
        return unary.identity;
    }
    switch (typeof value) {
        case 'function': {
            return value;
        }
        case 'object': {
            if (Array.isArray(value) && value.length === 2) {
                return matchesProperty(value[0], value[1]);
            }
            return matches(value);
        }
        case 'string':
        case 'symbol':
        case 'number': {
            return property(value);
        }
    }
}

function differenceBy(arr, ..._values) {
    if (!isArrayLikeObject(arr)) {
        return [];
    }
    const iteratee$1 = last(_values);
    const values = flattenArrayLike(_values);
    if (isArrayLikeObject(iteratee$1)) {
        return zipWith.difference(Array.from(arr), values);
    }
    return zipWith.differenceBy(Array.from(arr), values, iteratee(iteratee$1));
}

function differenceWith(array, ...values) {
    if (!isArrayLikeObject(array)) {
        return [];
    }
    const comparator = last(values);
    const flattenedValues = flattenArrayLike(values);
    if (typeof comparator === 'function') {
        return zipWith.differenceWith(Array.from(array), flattenedValues, comparator);
    }
    return zipWith.difference(Array.from(array), flattenedValues);
}

function isSymbol(value) {
    return typeof value === 'symbol' || value instanceof Symbol;
}

function toNumber(value) {
    if (isSymbol(value)) {
        return NaN;
    }
    return Number(value);
}

function toFinite(value) {
    if (!value) {
        return value === 0 ? value : 0;
    }
    value = toNumber(value);
    if (value === Infinity || value === -Infinity) {
        const sign = value < 0 ? -1 : 1;
        return sign * Number.MAX_VALUE;
    }
    return value === value ? value : 0;
}

function toInteger(value) {
    const finite = toFinite(value);
    const remainder = finite % 1;
    return remainder ? finite - remainder : finite;
}

function drop(collection, itemsCount = 1, guard) {
    if (!isArrayLike(collection)) {
        return [];
    }
    itemsCount = guard ? 1 : toInteger(itemsCount);
    return zipWith.drop(toArray$1(collection), itemsCount);
}

function dropRight(collection, itemsCount = 1, guard) {
    if (!isArrayLike(collection)) {
        return [];
    }
    itemsCount = guard ? 1 : toInteger(itemsCount);
    return zipWith.dropRight(toArray$1(collection), itemsCount);
}

function dropRightWhile(arr, predicate) {
    if (!isArrayLike(arr)) {
        return [];
    }
    return dropRightWhileImpl(Array.from(arr), predicate);
}
function dropRightWhileImpl(arr, predicate) {
    switch (typeof predicate) {
        case 'function': {
            return zipWith.dropRightWhile(arr, (item, index, arr) => Boolean(predicate(item, index, arr)));
        }
        case 'object': {
            if (Array.isArray(predicate) && predicate.length === 2) {
                const key = predicate[0];
                const value = predicate[1];
                return zipWith.dropRightWhile(arr, matchesProperty(key, value));
            }
            else {
                return zipWith.dropRightWhile(arr, matches(predicate));
            }
        }
        case 'symbol':
        case 'number':
        case 'string': {
            return zipWith.dropRightWhile(arr, property(predicate));
        }
    }
}

function dropWhile(arr, predicate) {
    if (!isArrayLike(arr)) {
        return [];
    }
    return dropWhileImpl(toArray$1(arr), predicate);
}
function dropWhileImpl(arr, predicate) {
    switch (typeof predicate) {
        case 'function': {
            return zipWith.dropWhile(arr, (item, index, arr) => Boolean(predicate(item, index, arr)));
        }
        case 'object': {
            if (Array.isArray(predicate) && predicate.length === 2) {
                const key = predicate[0];
                const value = predicate[1];
                return zipWith.dropWhile(arr, matchesProperty(key, value));
            }
            else {
                return zipWith.dropWhile(arr, matches(predicate));
            }
        }
        case 'number':
        case 'symbol':
        case 'string': {
            return zipWith.dropWhile(arr, property(predicate));
        }
    }
}

function isIterateeCall(value, index, object) {
    if (!isObject(object)) {
        return false;
    }
    if ((typeof index === 'number' && isArrayLike(object) && isIndex(index) && index < object.length) ||
        (typeof index === 'string' && index in object)) {
        return isWeakSet$1.eq(object[index], value);
    }
    return false;
}

function every(source, doesMatch, guard) {
    if (!source) {
        return true;
    }
    const values = Array.isArray(source) ? source : Object.values(source);
    if (guard && isIterateeCall(source, doesMatch, guard)) {
        doesMatch = undefined;
    }
    if (!doesMatch) {
        doesMatch = unary.identity;
    }
    switch (typeof doesMatch) {
        case 'function': {
            if (!Array.isArray(source)) {
                const keys = Object.keys(source);
                for (let i = 0; i < keys.length; i++) {
                    const key = keys[i];
                    const value = source[key];
                    if (!doesMatch(value, key, source)) {
                        return false;
                    }
                }
                return true;
            }
            return values.every(doesMatch);
        }
        case 'object': {
            if (Array.isArray(doesMatch) && doesMatch.length === 2) {
                const key = doesMatch[0];
                const value = doesMatch[1];
                return values.every(matchesProperty(key, value));
            }
            else {
                return values.every(matches(doesMatch));
            }
        }
        case 'symbol':
        case 'number':
        case 'string': {
            return values.every(property(doesMatch));
        }
    }
}

function isString(value) {
    return typeof value === 'string' || value instanceof String;
}

function fill(array, value, start = 0, end = array ? array.length : 0) {
    if (!isArrayLike(array)) {
        return [];
    }
    if (isString(array)) {
        return array;
    }
    start = Math.floor(start);
    end = Math.floor(end);
    if (!start) {
        start = 0;
    }
    if (!end) {
        end = 0;
    }
    return zipWith.fill(array, value, start, end);
}

function isArray(value) {
    return Array.isArray(value);
}

function filter(source, predicate) {
    if (!source) {
        return [];
    }
    if (!predicate) {
        predicate = unary.identity;
    }
    const collection = isArray(source) ? source : Object.values(source);
    switch (typeof predicate) {
        case 'function': {
            if (!Array.isArray(source)) {
                const result = [];
                const keys = Object.keys(source);
                for (let i = 0; i < keys.length; i++) {
                    const key = keys[i];
                    const value = source[key];
                    if (predicate(value, key, source)) {
                        result.push(value);
                    }
                }
                return result;
            }
            return collection.filter(predicate);
        }
        case 'object': {
            return isArray(predicate)
                ? collection.filter(matchesProperty(predicate[0], predicate[1]))
                : collection.filter(matches(predicate));
        }
        case 'symbol':
        case 'number':
        case 'string': {
            return collection.filter(property(predicate));
        }
    }
}

function find(source, _doesMatch, fromIndex = 0) {
    if (!source) {
        return undefined;
    }
    if (fromIndex < 0) {
        fromIndex = Math.max(source.length + fromIndex, 0);
    }
    const doesMatch = iteratee(_doesMatch);
    const values = Array.isArray(source) ? source.slice(fromIndex) : Object.values(source).slice(fromIndex);
    if (typeof doesMatch === 'function' && !Array.isArray(source)) {
        const keys = Object.keys(source).slice(fromIndex);
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            const value = source[key];
            if (doesMatch(value, key, source)) {
                return value;
            }
        }
        return undefined;
    }
    return values.find(doesMatch);
}

function findIndex(arr, doesMatch, fromIndex = 0) {
    if (!arr) {
        return -1;
    }
    if (fromIndex < 0) {
        fromIndex = Math.max(arr.length + fromIndex, 0);
    }
    const subArray = Array.from(arr).slice(fromIndex);
    let index = -1;
    switch (typeof doesMatch) {
        case 'function': {
            index = subArray.findIndex(doesMatch);
            break;
        }
        case 'object': {
            if (Array.isArray(doesMatch) && doesMatch.length === 2) {
                const key = doesMatch[0];
                const value = doesMatch[1];
                index = subArray.findIndex(matchesProperty(key, value));
            }
            else {
                index = subArray.findIndex(matches(doesMatch));
            }
            break;
        }
        case 'number':
        case 'symbol':
        case 'string': {
            index = subArray.findIndex(property(doesMatch));
        }
    }
    return index === -1 ? -1 : index + fromIndex;
}

function findLast(source, _doesMatch, fromIndex) {
    if (!source) {
        return undefined;
    }
    const length = Array.isArray(source) ? source.length : Object.keys(source).length;
    fromIndex = toInteger(fromIndex ?? length - 1);
    if (fromIndex < 0) {
        fromIndex = Math.max(length + fromIndex, 0);
    }
    else {
        fromIndex = Math.min(fromIndex, length - 1);
    }
    const doesMatch = iteratee(_doesMatch);
    const values = Array.isArray(source) ? source.slice(0, fromIndex + 1) : Object.values(source).slice(0, fromIndex + 1);
    if (typeof doesMatch === 'function' && !Array.isArray(source)) {
        const keys = Object.keys(source).slice(0, fromIndex + 1);
        for (let i = fromIndex; i >= 0; i--) {
            const key = keys[i];
            const value = source[key];
            if (doesMatch(value, key, source)) {
                return value;
            }
        }
        return undefined;
    }
    return values.findLast(doesMatch);
}

function findLastIndex(arr, doesMatch, fromIndex = arr ? arr.length - 1 : 0) {
    if (!arr) {
        return -1;
    }
    if (fromIndex < 0) {
        fromIndex = Math.max(arr.length + fromIndex, 0);
    }
    else {
        fromIndex = Math.min(fromIndex, arr.length - 1);
    }
    const subArray = toArray$1(arr).slice(0, fromIndex + 1);
    switch (typeof doesMatch) {
        case 'function': {
            return subArray.findLastIndex(doesMatch);
        }
        case 'object': {
            if (Array.isArray(doesMatch) && doesMatch.length === 2) {
                const key = doesMatch[0];
                const value = doesMatch[1];
                return subArray.findLastIndex(matchesProperty(key, value));
            }
            else {
                return subArray.findLastIndex(matches(doesMatch));
            }
        }
        case 'number':
        case 'symbol':
        case 'string': {
            return subArray.findLastIndex(property(doesMatch));
        }
    }
}

function flatten(value, depth = 1) {
    const result = [];
    const flooredDepth = Math.floor(depth);
    if (!isArrayLike(value)) {
        return result;
    }
    const recursive = (arr, currentDepth) => {
        for (let i = 0; i < arr.length; i++) {
            const item = arr[i];
            if (currentDepth < flooredDepth &&
                (Array.isArray(item) ||
                    Boolean(item?.[Symbol.isConcatSpreadable]) ||
                    (item !== null && typeof item === 'object' && Object.prototype.toString.call(item) === '[object Arguments]'))) {
                if (Array.isArray(item)) {
                    recursive(item, currentDepth + 1);
                }
                else {
                    recursive(Array.from(item), currentDepth + 1);
                }
            }
            else {
                result.push(item);
            }
        }
    };
    recursive(Array.from(value), 0);
    return result;
}

function flattenDeep(value) {
    return flatten(value, Infinity);
}

function flattenDepth(value, depth = 1) {
    return flatten(value, depth);
}

function forEach(collection, callback = unary.identity) {
    if (!collection) {
        return collection;
    }
    const keys = isArrayLike(collection) || Array.isArray(collection) ? range$1.range(0, collection.length) : Object.keys(collection);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = collection[key];
        const result = callback(value, key, collection);
        if (result === false) {
            break;
        }
    }
    return collection;
}

function head(arr) {
    if (!isArrayLike(arr)) {
        return undefined;
    }
    return zipWith.head(toArray$1(arr));
}

function includes(source, target, fromIndex, guard) {
    if (source == null) {
        return false;
    }
    if (guard || !fromIndex) {
        fromIndex = 0;
    }
    else {
        fromIndex = toInteger(fromIndex);
    }
    if (isString(source)) {
        if (fromIndex > source.length || target instanceof RegExp) {
            return false;
        }
        if (fromIndex < 0) {
            fromIndex = Math.max(0, source.length + fromIndex);
        }
        return source.includes(target, fromIndex);
    }
    if (Array.isArray(source)) {
        return source.includes(target, fromIndex);
    }
    const keys = Object.keys(source);
    if (fromIndex < 0) {
        fromIndex = Math.max(0, keys.length + fromIndex);
    }
    for (let i = fromIndex; i < keys.length; i++) {
        const value = Reflect.get(source, keys[i]);
        if (isWeakSet$1.eq(value, target)) {
            return true;
        }
    }
    return false;
}

function indexOf(array, searchElement, fromIndex) {
    if (!isArrayLike(array)) {
        return -1;
    }
    if (Number.isNaN(searchElement)) {
        fromIndex = fromIndex ?? 0;
        if (fromIndex < 0) {
            fromIndex = Math.max(0, array.length + fromIndex);
        }
        for (let i = fromIndex; i < array.length; i++) {
            if (Number.isNaN(array[i])) {
                return i;
            }
        }
        return -1;
    }
    return Array.from(array).indexOf(searchElement, fromIndex);
}

function intersection(...arrays) {
    if (arrays.length === 0) {
        return [];
    }
    if (!isArrayLikeObject(arrays[0])) {
        return [];
    }
    let result = zipWith.uniq(Array.from(arrays[0]));
    for (let i = 1; i < arrays.length; i++) {
        const array = arrays[i];
        if (!isArrayLikeObject(array)) {
            return [];
        }
        result = zipWith.intersection(result, Array.from(array));
    }
    return result;
}

function intersectionBy(array, ...values) {
    if (!isArrayLikeObject(array)) {
        return [];
    }
    const lastValue = zipWith.last(values);
    if (lastValue === undefined) {
        return Array.from(array);
    }
    let result = zipWith.uniq(Array.from(array));
    const count = isArrayLikeObject(lastValue) ? values.length : values.length - 1;
    for (let i = 0; i < count; ++i) {
        const value = values[i];
        if (!isArrayLikeObject(value)) {
            return [];
        }
        if (isArrayLikeObject(lastValue)) {
            result = zipWith.intersectionBy(result, Array.from(value), unary.identity);
        }
        else if (typeof lastValue === 'function') {
            result = zipWith.intersectionBy(result, Array.from(value), value => lastValue(value));
        }
        else if (typeof lastValue === 'string') {
            result = zipWith.intersectionBy(result, Array.from(value), property(lastValue));
        }
    }
    return result;
}

function uniq(arr) {
    if (!isArrayLike(arr)) {
        return [];
    }
    return zipWith.uniq(Array.from(arr));
}

function intersectionWith(firstArr, ...otherArrs) {
    console.log(firstArr);
    if (firstArr == null) {
        return [];
    }
    const _comparator = last(otherArrs);
    let comparator = isWeakSet$1.eq;
    let uniq$1 = uniq;
    if (typeof _comparator === 'function') {
        comparator = _comparator;
        uniq$1 = uniqPreserve0;
        otherArrs.pop();
    }
    let result = uniq$1(Array.from(firstArr));
    for (let i = 0; i < otherArrs.length; ++i) {
        const otherArr = otherArrs[i];
        if (otherArr == null) {
            return [];
        }
        result = zipWith.intersectionWith(result, Array.from(otherArr), comparator);
    }
    return result;
}
function uniqPreserve0(arr) {
    const result = [];
    const added = new Set();
    for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (added.has(item)) {
            continue;
        }
        result.push(item);
        added.add(item);
    }
    return result;
}

function join(array, separator = ',') {
    if (!isArrayLike(array)) {
        return '';
    }
    return Array.from(array).join(separator);
}

function lastIndexOf(array, searchElement, fromIndex) {
    if (!isArrayLike(array) || array.length === 0) {
        return -1;
    }
    const length = array.length;
    let index = fromIndex ?? length - 1;
    if (fromIndex != null) {
        index = index < 0 ? Math.max(length + index, 0) : Math.min(index, length - 1);
    }
    if (Number.isNaN(searchElement)) {
        for (let i = index; i >= 0; i--) {
            if (Number.isNaN(array[i])) {
                return i;
            }
        }
    }
    return Array.from(array).lastIndexOf(searchElement, index);
}

function map(collection, _iteratee) {
    if (!collection) {
        return [];
    }
    const keys = isArrayLike(collection) || Array.isArray(collection) ? range$1.range(0, collection.length) : Object.keys(collection);
    const iteratee$1 = iteratee(_iteratee ?? unary.identity);
    const result = new Array(keys.length);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = collection[key];
        result[i] = iteratee$1(value, key, collection);
    }
    return result;
}

function nth(array, n = 0) {
    if (!isArrayLikeObject(array) || array.length === 0) {
        return undefined;
    }
    n = toInteger(n);
    if (n < 0) {
        n += array.length;
    }
    return array[n];
}

function getPriority(a) {
    if (typeof a === 'symbol') {
        return 1;
    }
    if (a === null) {
        return 2;
    }
    if (a === undefined) {
        return 3;
    }
    if (a !== a) {
        return 4;
    }
    return 0;
}
const compareValues = (a, b, order) => {
    if (a !== b) {
        if (typeof a === 'string' && typeof b === 'string') {
            return order === 'desc' ? b.localeCompare(a) : a.localeCompare(b);
        }
        const aPriority = getPriority(a);
        const bPriority = getPriority(b);
        if (aPriority === bPriority && aPriority === 0) {
            if (a < b) {
                return order === 'desc' ? 1 : -1;
            }
            if (a > b) {
                return order === 'desc' ? -1 : 1;
            }
        }
        return order === 'desc' ? bPriority - aPriority : aPriority - bPriority;
    }
    return 0;
};

const regexIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
const regexIsPlainProp = /^\w*$/;
function isKey(value, object) {
    if (Array.isArray(value)) {
        return false;
    }
    if (typeof value === 'number' || typeof value === 'boolean' || value == null || isSymbol(value)) {
        return true;
    }
    return ((typeof value === 'string' && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value))) ||
        (object != null));
}

function orderBy(collection, criteria, orders, guard) {
    if (collection == null) {
        return [];
    }
    orders = guard ? undefined : orders;
    if (!Array.isArray(collection)) {
        collection = Object.values(collection);
    }
    if (!Array.isArray(criteria)) {
        criteria = criteria == null ? [null] : [criteria];
    }
    if (criteria.length === 0) {
        criteria = [null];
    }
    if (!Array.isArray(orders)) {
        orders = orders == null ? [] : [orders];
    }
    orders = orders.map(order => String(order));
    const getValueByNestedPath = (object, path) => {
        let target = object;
        for (let i = 0; i < path.length && target != null; ++i) {
            target = target[path[i]];
        }
        return target;
    };
    const getValueByCriterion = (criterion, object) => {
        if (object == null || criterion == null) {
            return object;
        }
        if (typeof criterion === 'object' && 'key' in criterion) {
            if (Object.hasOwn(object, criterion.key)) {
                return object[criterion.key];
            }
            return getValueByNestedPath(object, criterion.path);
        }
        if (typeof criterion === 'function') {
            return criterion(object);
        }
        if (Array.isArray(criterion)) {
            return getValueByNestedPath(object, criterion);
        }
        if (typeof object === 'object') {
            return object[criterion];
        }
        return object;
    };
    const preparedCriteria = criteria.map(criterion => {
        if (Array.isArray(criterion) && criterion.length === 1) {
            criterion = criterion[0];
        }
        if (criterion == null || typeof criterion === 'function' || Array.isArray(criterion) || isKey(criterion)) {
            return criterion;
        }
        return { key: criterion, path: toPath(criterion) };
    });
    const preparedCollection = collection.map(item => ({
        original: item,
        criteria: preparedCriteria.map(criterion => getValueByCriterion(criterion, item)),
    }));
    return preparedCollection
        .slice()
        .sort((a, b) => {
        for (let i = 0; i < preparedCriteria.length; i++) {
            const comparedResult = compareValues(a.criteria[i], b.criteria[i], orders[i]);
            if (comparedResult !== 0) {
                return comparedResult;
            }
        }
        return 0;
    })
        .map(item => item.original);
}

function pull(arr, ...valuesToRemove) {
    return zipWith.pull(arr, valuesToRemove);
}

function pullAll(arr, valuesToRemove = []) {
    return zipWith.pull(arr, Array.from(valuesToRemove));
}

function pullAllBy(arr, valuesToRemove, _getValue) {
    const getValue = iteratee(_getValue);
    const valuesSet = new Set(Array.from(valuesToRemove).map(x => getValue(x)));
    let resultIndex = 0;
    for (let i = 0; i < arr.length; i++) {
        const value = getValue(arr[i]);
        if (valuesSet.has(value)) {
            continue;
        }
        if (!Object.hasOwn(arr, i)) {
            delete arr[resultIndex++];
            continue;
        }
        arr[resultIndex++] = arr[i];
    }
    arr.length = resultIndex;
    return arr;
}

function reduce(collection, iteratee = unary.identity, accumulator) {
    if (!collection) {
        return accumulator;
    }
    let keys;
    let startIndex = 0;
    if (isArrayLike(collection)) {
        keys = range$1.range(0, collection.length);
        if (accumulator == null && collection.length > 0) {
            accumulator = collection[0];
            startIndex += 1;
        }
    }
    else {
        keys = Object.keys(collection);
        if (accumulator == null) {
            accumulator = collection[keys[0]];
            startIndex += 1;
        }
    }
    for (let i = startIndex; i < keys.length; i++) {
        const key = keys[i];
        const value = collection[key];
        accumulator = iteratee(accumulator, value, key, collection);
    }
    return accumulator;
}

function reduceRight(collection, iteratee = unary.identity, accumulator) {
    if (!collection) {
        return accumulator;
    }
    let keys;
    let startIndex;
    if (isArrayLike(collection)) {
        keys = range$1.range(0, collection.length).reverse();
        if (accumulator == null && collection.length > 0) {
            accumulator = collection[collection.length - 1];
            startIndex = 1;
        }
        else {
            startIndex = 0;
        }
    }
    else {
        keys = Object.keys(collection).reverse();
        if (accumulator == null) {
            accumulator = collection[keys[0]];
            startIndex = 1;
        }
        else {
            startIndex = 0;
        }
    }
    for (let i = startIndex; i < keys.length; i++) {
        const key = keys[i];
        const value = collection[key];
        accumulator = iteratee(accumulator, value, key, collection);
    }
    return accumulator;
}

function remove(arr, shouldRemoveElement) {
    return zipWith.remove(arr, iteratee(shouldRemoveElement));
}

function reverse(array) {
    if (array == null) {
        return array;
    }
    return array.reverse();
}

function sample(collection) {
    if (collection == null) {
        return undefined;
    }
    if (isArrayLike(collection)) {
        return zipWith.sample(toArray$1(collection));
    }
    return zipWith.sample(Object.values(collection));
}

function size(target) {
    if (isWeakSet$1.isNil(target)) {
        return 0;
    }
    if (target instanceof Map || target instanceof Set) {
        return target.size;
    }
    return Object.keys(target).length;
}

function slice(array, start, end) {
    if (!isArrayLike(array)) {
        return [];
    }
    const length = array.length;
    if (end === undefined) {
        end = length;
    }
    else if (typeof end !== 'number' && isIterateeCall(array, start, end)) {
        start = 0;
        end = length;
    }
    start = toInteger(start);
    end = toInteger(end);
    if (start < 0) {
        start = Math.max(length + start, 0);
    }
    else {
        start = Math.min(start, length);
    }
    if (end < 0) {
        end = Math.max(length + end, 0);
    }
    else {
        end = Math.min(end, length);
    }
    const resultLength = Math.max(end - start, 0);
    const result = new Array(resultLength);
    for (let i = 0; i < resultLength; ++i) {
        result[i] = array[start + i];
    }
    return result;
}

function some(source, predicate, guard) {
    if (!source) {
        return false;
    }
    if (guard != null) {
        predicate = undefined;
    }
    if (!predicate) {
        predicate = unary.identity;
    }
    const values = Array.isArray(source) ? source : Object.values(source);
    switch (typeof predicate) {
        case 'function': {
            if (!Array.isArray(source)) {
                const keys = Object.keys(source);
                for (let i = 0; i < keys.length; i++) {
                    const key = keys[i];
                    const value = source[key];
                    if (predicate(value, key, source)) {
                        return true;
                    }
                }
                return false;
            }
            return values.some(predicate);
        }
        case 'object': {
            if (Array.isArray(predicate) && predicate.length === 2) {
                const key = predicate[0];
                const value = predicate[1];
                return values.some(matchesProperty(key, value));
            }
            else {
                return values.some(matches(predicate));
            }
        }
        case 'number':
        case 'symbol':
        case 'string': {
            return values.some(property(predicate));
        }
    }
}

function sortBy(collection, ...criteria) {
    const length = criteria.length;
    if (length > 1 && isIterateeCall(collection, criteria[0], criteria[1])) {
        criteria = [];
    }
    else if (length > 2 && isIterateeCall(criteria[0], criteria[1], criteria[2])) {
        criteria = [criteria[0]];
    }
    return orderBy(collection, zipWith.flatten(criteria), ['asc']);
}

function isNaN(value) {
    return Number.isNaN(value);
}

function isNil(x) {
    return x == null;
}

const MAX_ARRAY_LENGTH$2 = 4294967295;
const MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH$2 - 1;
function sortedIndexBy(array, value, iteratee, retHighest) {
    let low = 0;
    let high = array == null ? 0 : array.length;
    if (high === 0 || isNil(array)) {
        return 0;
    }
    const transformedValue = iteratee?.(value);
    const valIsNaN = isNaN(transformedValue);
    const valIsNull = isWeakSet$1.isNull(transformedValue);
    const valIsSymbol = isSymbol(transformedValue);
    const valIsUndefined = isWeakSet$1.isUndefined(transformedValue);
    while (low < high) {
        let setLow;
        const mid = Math.floor((low + high) / 2);
        const computed = iteratee?.(array[mid]);
        const othIsDefined = !isWeakSet$1.isUndefined(computed);
        const othIsNull = isWeakSet$1.isNull(computed);
        const othIsReflexive = !isNaN(computed);
        const othIsSymbol = isSymbol(computed);
        if (valIsNaN) {
            setLow = retHighest || othIsReflexive;
        }
        else if (valIsUndefined) {
            setLow = othIsReflexive && (retHighest || othIsDefined);
        }
        else if (valIsNull) {
            setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);
        }
        else if (valIsSymbol) {
            setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);
        }
        else if (othIsNull || othIsSymbol) {
            setLow = false;
        }
        else {
            setLow = retHighest ? computed <= transformedValue : computed < transformedValue;
        }
        if (setLow) {
            low = mid + 1;
        }
        else {
            high = mid;
        }
    }
    return Math.min(high, MAX_ARRAY_INDEX);
}

function isNumber(value) {
    return typeof value === 'number' || value instanceof Number;
}

const MAX_ARRAY_LENGTH$1 = 4294967295;
const HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH$1 >>> 1;
function sortedIndex(array, value) {
    if (isWeakSet$1.isNil(array)) {
        return 0;
    }
    let low = 0, high = isWeakSet$1.isNil(array) ? low : array.length;
    if (isNumber(value) && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
        while (low < high) {
            const mid = (low + high) >>> 1;
            const compute = array[mid];
            if (!isWeakSet$1.isNull(compute) && !isWeakSet$1.isSymbol(compute) && compute < value) {
                low = mid + 1;
            }
            else {
                high = mid;
            }
        }
        return high;
    }
    return sortedIndexBy(array, value, value => value);
}

function tail(arr) {
    if (!isArrayLike(arr)) {
        return [];
    }
    return zipWith.tail(toArray$1(arr));
}

function take(arr, count = 1, guard) {
    count = guard ? 1 : toInteger(count);
    if (count < 1 || !isArrayLike(arr)) {
        return [];
    }
    return zipWith.take(toArray$1(arr), count);
}

function takeRight(arr, count = 1, guard) {
    count = guard ? 1 : toInteger(count);
    if (count <= 0 || !isArrayLike(arr)) {
        return [];
    }
    return zipWith.takeRight(toArray$1(arr), count);
}

function takeRightWhile(_array, predicate) {
    if (!isArrayLikeObject(_array)) {
        return [];
    }
    const array = toArray$1(_array);
    const index = array.findLastIndex(unary.negate(iteratee(predicate)));
    return array.slice(index + 1);
}

function union(...arrays) {
    const validArrays = arrays.filter(isArrayLikeObject);
    const flattened = flatten(validArrays, 1);
    return zipWith.uniq(flattened);
}

function uniqBy(array, iteratee$1) {
    if (!isArrayLikeObject(array)) {
        return [];
    }
    return zipWith.uniqBy(Array.from(array), iteratee(iteratee$1));
}

function unzip(array) {
    if (!isArrayLikeObject(array) || !array.length) {
        return [];
    }
    if (Array.isArray(array)) {
        return zipWith.unzip(array);
    }
    return zipWith.unzip(Array.from(array, value => Array.from(value)));
}

function without(array, ...values) {
    if (!isArrayLikeObject(array)) {
        return [];
    }
    return zipWith.without(Array.from(array), ...values);
}

function zip(...arrays) {
    if (!arrays.length) {
        return [];
    }
    return zipWith.zip(...arrays.filter(group => isArrayLikeObject(group)));
}

function set(obj, path, value) {
    const resolvedPath = Array.isArray(path) ? path : typeof path === 'string' ? toPath(path) : [path];
    let current = obj;
    for (let i = 0; i < resolvedPath.length - 1; i++) {
        const key = resolvedPath[i];
        const nextKey = resolvedPath[i + 1];
        if (current[key] == null) {
            current[key] = isIndex(nextKey) ? [] : {};
        }
        current = current[key];
    }
    const lastKey = resolvedPath[resolvedPath.length - 1];
    current[lastKey] = value;
    return obj;
}

function zipObjectDeep(keys, values) {
    const result = {};
    if (!isArrayLike(keys)) {
        return result;
    }
    if (!isArrayLike(values)) {
        values = [];
    }
    const zipped = zipWith.zip(Array.from(keys), Array.from(values));
    for (let i = 0; i < zipped.length; i++) {
        const [key, value] = zipped[i];
        if (key != null) {
            set(result, key, value);
        }
    }
    return result;
}

function after(n, func) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    n = toInteger(n);
    return function (...args) {
        if (--n < 1) {
            return func.apply(this, args);
        }
    };
}

function ary(func, n = func.length, guard) {
    if (guard) {
        n = func.length;
    }
    if (Number.isNaN(n) || n < 0) {
        n = 0;
    }
    return unary.ary(func, n);
}

function attempt(func, ...args) {
    try {
        return func(...args);
    }
    catch (e) {
        return e instanceof Error ? e : new Error(e);
    }
}

function before(n, func) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    let result;
    n = toInteger(n);
    return function (...args) {
        if (--n > 0) {
            result = func.apply(this, args);
        }
        if (n <= 1 && func) {
            func = undefined;
        }
        return result;
    };
}

function bind(func, thisObj, ...partialArgs) {
    const bound = function (...providedArgs) {
        const args = [];
        let startIndex = 0;
        for (let i = 0; i < partialArgs.length; i++) {
            const arg = partialArgs[i];
            if (arg === bind.placeholder) {
                args.push(providedArgs[startIndex++]);
            }
            else {
                args.push(arg);
            }
        }
        for (let i = startIndex; i < providedArgs.length; i++) {
            args.push(providedArgs[i]);
        }
        if (this instanceof bound) {
            return new func(...args);
        }
        return func.apply(thisObj, args);
    };
    return bound;
}
const bindPlaceholder = Symbol('bind.placeholder');
bind.placeholder = bindPlaceholder;

function bindKey(object, key, ...partialArgs) {
    const bound = function (...providedArgs) {
        const args = [];
        let startIndex = 0;
        for (let i = 0; i < partialArgs.length; i++) {
            const arg = partialArgs[i];
            if (arg === bindKey.placeholder) {
                args.push(providedArgs[startIndex++]);
            }
            else {
                args.push(arg);
            }
        }
        for (let i = startIndex; i < providedArgs.length; i++) {
            args.push(providedArgs[i]);
        }
        if (this instanceof bound) {
            return new object[key](...args);
        }
        return object[key].apply(object, args);
    };
    return bound;
}
const bindKeyPlaceholder = Symbol('bindKey.placeholder');
bindKey.placeholder = bindKeyPlaceholder;

function curry(func, arity = func.length, guard) {
    arity = guard ? func.length : arity;
    arity = Number.parseInt(arity, 10);
    if (Number.isNaN(arity) || arity < 1) {
        arity = 0;
    }
    const wrapper = function (...partialArgs) {
        const holders = partialArgs.filter(item => item === curry.placeholder);
        const length = partialArgs.length - holders.length;
        if (length < arity) {
            return makeCurry(func, arity - length, partialArgs);
        }
        if (this instanceof wrapper) {
            return new func(...partialArgs);
        }
        return func.apply(this, partialArgs);
    };
    wrapper.placeholder = curryPlaceholder;
    return wrapper;
}
function makeCurry(func, arity, partialArgs) {
    function wrapper(...providedArgs) {
        const holders = providedArgs.filter(item => item === curry.placeholder);
        const length = providedArgs.length - holders.length;
        providedArgs = composeArgs$1(providedArgs, partialArgs);
        if (length < arity) {
            return makeCurry(func, arity - length, providedArgs);
        }
        if (this instanceof wrapper) {
            return new func(...providedArgs);
        }
        return func.apply(this, providedArgs);
    }
    wrapper.placeholder = curryPlaceholder;
    return wrapper;
}
function composeArgs$1(providedArgs, partialArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
        const arg = partialArgs[i];
        if (arg === curry.placeholder && startIndex < providedArgs.length) {
            args.push(providedArgs[startIndex++]);
        }
        else {
            args.push(arg);
        }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
        args.push(providedArgs[i]);
    }
    return args;
}
const curryPlaceholder = Symbol('curry.placeholder');
curry.placeholder = curryPlaceholder;

function curryRight(func, arity = func.length, guard) {
    arity = guard ? func.length : arity;
    arity = Number.parseInt(arity, 10);
    if (Number.isNaN(arity) || arity < 1) {
        arity = 0;
    }
    const wrapper = function (...partialArgs) {
        const holders = partialArgs.filter(item => item === curryRight.placeholder);
        const length = partialArgs.length - holders.length;
        if (length < arity) {
            return makeCurryRight(func, arity - length, partialArgs);
        }
        if (this instanceof wrapper) {
            return new func(...partialArgs);
        }
        return func.apply(this, partialArgs);
    };
    wrapper.placeholder = curryRightPlaceholder;
    return wrapper;
}
function makeCurryRight(func, arity, partialArgs) {
    function wrapper(...providedArgs) {
        const holders = providedArgs.filter(item => item === curryRight.placeholder);
        const length = providedArgs.length - holders.length;
        providedArgs = composeArgs(providedArgs, partialArgs);
        if (length < arity) {
            return makeCurryRight(func, arity - length, providedArgs);
        }
        if (this instanceof wrapper) {
            return new func(...providedArgs);
        }
        return func.apply(this, providedArgs);
    }
    wrapper.placeholder = curryRightPlaceholder;
    return wrapper;
}
function composeArgs(providedArgs, partialArgs) {
    const placeholderLength = partialArgs.filter(arg => arg === curryRight.placeholder).length;
    const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
    const args = [];
    let providedIndex = 0;
    for (let i = 0; i < rangeLength; i++) {
        args.push(providedArgs[providedIndex++]);
    }
    for (let i = 0; i < partialArgs.length; i++) {
        const arg = partialArgs[i];
        if (arg === curryRight.placeholder) {
            if (providedIndex < providedArgs.length) {
                args.push(providedArgs[providedIndex++]);
            }
            else {
                args.push(arg);
            }
        }
        else {
            args.push(arg);
        }
    }
    return args;
}
const curryRightPlaceholder = Symbol('curryRight.placeholder');
curryRight.placeholder = curryRightPlaceholder;

function debounce(func, debounceMs = 0, options = {}) {
    if (typeof options !== 'object') {
        options = {};
    }
    const { signal, leading = false, trailing = true, maxWait } = options;
    const edges = Array(2);
    if (leading) {
        edges[0] = 'leading';
    }
    if (trailing) {
        edges[1] = 'trailing';
    }
    let result = undefined;
    let pendingAt = null;
    const _debounced = unary.debounce(function (...args) {
        result = func.apply(this, args);
        pendingAt = null;
    }, debounceMs, { signal, edges });
    const debounced = function (...args) {
        if (maxWait != null) {
            if (pendingAt === null) {
                pendingAt = Date.now();
            }
            else {
                if (Date.now() - pendingAt >= maxWait) {
                    result = func.apply(this, args);
                    pendingAt = Date.now();
                    _debounced.cancel();
                    _debounced.schedule();
                    return result;
                }
            }
        }
        _debounced.apply(this, args);
        return result;
    };
    const flush = () => {
        _debounced.flush();
        return result;
    };
    debounced.cancel = _debounced.cancel;
    debounced.flush = flush;
    return debounced;
}

function defer(func, ...args) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    return setTimeout(func, 1, ...args);
}

function delay(func, wait, ...args) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    return setTimeout(func, toNumber(wait) || 0, ...args);
}

function flip(func) {
    return function (...args) {
        return func.apply(this, args.reverse());
    };
}

function flow(...funcs) {
    const flattenFuncs = zipWith.flatten(funcs, 1);
    if (flattenFuncs.some(func => typeof func !== 'function')) {
        throw new TypeError('Expected a function');
    }
    return unary.flow(...flattenFuncs);
}

function flowRight(...funcs) {
    const flattenFuncs = zipWith.flatten(funcs, 1);
    if (flattenFuncs.some(func => typeof func !== 'function')) {
        throw new TypeError('Expected a function');
    }
    return unary.flowRight(...flattenFuncs);
}

function negate(func) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    return function (...args) {
        return !func.apply(this, args);
    };
}

function nthArg(n = 0) {
    return function (...args) {
        return args.at(toInteger(n));
    };
}

function rearg(func, ...indices) {
    const flattenIndices = flatten(indices);
    return function (...args) {
        const reorderedArgs = flattenIndices.map(i => args[i]).slice(0, args.length);
        for (let i = reorderedArgs.length; i < args.length; i++) {
            reorderedArgs.push(args[i]);
        }
        return func.apply(this, reorderedArgs);
    };
}

function rest(func, start = func.length - 1) {
    start = Number.parseInt(start, 10);
    if (Number.isNaN(start) || start < 0) {
        start = func.length - 1;
    }
    return unary.rest(func, start);
}

function spread(func, argsIndex = 0) {
    argsIndex = Number.parseInt(argsIndex, 10);
    if (Number.isNaN(argsIndex) || argsIndex < 0) {
        argsIndex = 0;
    }
    return function (...args) {
        const array = args[argsIndex];
        const params = args.slice(0, argsIndex);
        if (array) {
            params.push(...array);
        }
        return func.apply(this, params);
    };
}

function throttle(func, throttleMs = 0, options = {}) {
    if (typeof options !== 'object') {
        options = {};
    }
    const { leading = true, trailing = true, signal } = options;
    return debounce(func, throttleMs, {
        leading,
        trailing,
        signal,
        maxWait: throttleMs,
    });
}

function add(value, other) {
    return value + other;
}

function decimalAdjust(type, number, precision = 0) {
    number = Number(number);
    if (Object.is(number, -0)) {
        number = '-0';
    }
    precision = Math.min(Number.parseInt(precision, 10), 292);
    if (precision) {
        const [magnitude, exponent = 0] = number.toString().split('e');
        let adjustedValue = Math[type](Number(`${magnitude}e${Number(exponent) + precision}`));
        if (Object.is(adjustedValue, -0)) {
            adjustedValue = '-0';
        }
        const [newMagnitude, newExponent = 0] = adjustedValue.toString().split('e');
        return Number(`${newMagnitude}e${Number(newExponent) - precision}`);
    }
    return Math[type](Number(number));
}

function ceil(number, precision = 0) {
    return decimalAdjust('ceil', number, precision);
}

function clamp(value, bound1, bound2) {
    if (Number.isNaN(bound1)) {
        bound1 = 0;
    }
    if (Number.isNaN(bound2)) {
        bound2 = 0;
    }
    return range$1.clamp(value, bound1, bound2);
}

function toString(value) {
    if (value == null) {
        return '';
    }
    if (Array.isArray(value)) {
        return value.map(toString).join(',');
    }
    const result = String(value);
    if (result === '0' && Object.is(Number(value), -0)) {
        return '-0';
    }
    return result;
}

function divide(value, other) {
    console.log(value, other);
    if (value === undefined && other === undefined) {
        return 1;
    }
    if (value === undefined || other === undefined) {
        return value ?? other;
    }
    if (typeof value === 'string' || typeof other === 'string') {
        value = toString(value);
        other = toString(other);
    }
    else {
        value = toNumber(value);
        other = toNumber(other);
    }
    return value / other;
}

function floor(number, precision = 0) {
    return decimalAdjust('floor', number, precision);
}

function inRange(value, minimum, maximum) {
    if (!minimum) {
        minimum = 0;
    }
    if (maximum != null && !maximum) {
        maximum = 0;
    }
    if (minimum != null && typeof minimum !== 'number') {
        minimum = Number(minimum);
    }
    if (maximum == null && minimum === 0) {
        return false;
    }
    if (maximum != null && typeof maximum !== 'number') {
        maximum = Number(maximum);
    }
    if (maximum != null && minimum > maximum) {
        [minimum, maximum] = [maximum, minimum];
    }
    if (minimum === maximum) {
        return false;
    }
    return range$1.inRange(value, minimum, maximum);
}

function max(items = []) {
    let maxElement = items[0];
    let max = undefined;
    for (let i = 0; i < items.length; i++) {
        const element = items[i];
        if (max == null || element > max) {
            max = element;
            maxElement = element;
        }
    }
    return maxElement;
}

function maxBy(items, iteratee$1) {
    if (items == null) {
        return undefined;
    }
    return zipWith.maxBy(Array.from(items), iteratee(iteratee$1));
}

function min(items = []) {
    let minElement = items[0];
    let min = undefined;
    for (let i = 0; i < items.length; i++) {
        const element = items[i];
        if (min == null || element < min) {
            min = element;
            minElement = element;
        }
    }
    return minElement;
}

function multiply(value, other) {
    if (value === undefined && other === undefined) {
        return 1;
    }
    if (value === undefined || other === undefined) {
        return value ?? other;
    }
    if (typeof value === 'string' || typeof other === 'string') {
        value = toString(value);
        other = toString(other);
    }
    else {
        value = toNumber(value);
        other = toNumber(other);
    }
    return value * other;
}

function parseInt(string, radix = 0, guard) {
    if (guard) {
        radix = 0;
    }
    return Number.parseInt(string, radix);
}

function random(...args) {
    let minimum = 0;
    let maximum = 1;
    let floating = false;
    switch (args.length) {
        case 1: {
            if (typeof args[0] === 'boolean') {
                floating = args[0];
            }
            else {
                maximum = args[0];
            }
            break;
        }
        case 2: {
            if (typeof args[1] === 'boolean') {
                maximum = args[0];
                floating = args[1];
            }
            else {
                minimum = args[0];
                maximum = args[1];
            }
        }
        case 3: {
            if (typeof args[2] === 'object' && args[2] != null && args[2][args[1]] === args[0]) {
                minimum = 0;
                maximum = args[0];
                floating = false;
            }
            else {
                minimum = args[0];
                maximum = args[1];
                floating = args[2];
            }
        }
    }
    if (typeof minimum !== 'number') {
        minimum = Number(minimum);
    }
    if (typeof maximum !== 'number') {
        minimum = Number(maximum);
    }
    if (!minimum) {
        minimum = 0;
    }
    if (!maximum) {
        maximum = 0;
    }
    if (minimum > maximum) {
        [minimum, maximum] = [maximum, minimum];
    }
    minimum = clamp(minimum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
    maximum = clamp(maximum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
    if (minimum === maximum) {
        return minimum;
    }
    if (floating) {
        return randomInt.random(minimum, maximum + 1);
    }
    else {
        return randomInt.randomInt(minimum, maximum + 1);
    }
}

function range(start, end, step) {
    if (step && typeof step !== 'number' && isIterateeCall(start, end, step)) {
        end = step = undefined;
    }
    start = toFinite(start);
    if (end === undefined) {
        end = start;
        start = 0;
    }
    else {
        end = toFinite(end);
    }
    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);
    const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
    const result = new Array(length);
    for (let index = 0; index < length; index++) {
        result[index] = start;
        start += step;
    }
    return result;
}

function rangeRight(start, end, step) {
    if (step && typeof step !== 'number' && isIterateeCall(start, end, step)) {
        end = step = undefined;
    }
    start = toFinite(start);
    if (end === undefined) {
        end = start;
        start = 0;
    }
    else {
        end = toFinite(end);
    }
    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);
    const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
    const result = new Array(length);
    for (let index = length - 1; index >= 0; index--) {
        result[index] = start;
        start += step;
    }
    return result;
}

function round(number, precision = 0) {
    return decimalAdjust('round', number, precision);
}

function subtract(value, other) {
    return value - other;
}

function sumBy(array, iteratee$1) {
    if (!array || !array.length) {
        return 0;
    }
    if (iteratee$1 != null) {
        iteratee$1 = iteratee(iteratee$1);
    }
    let result = undefined;
    for (let i = 0; i < array.length; i++) {
        const current = iteratee$1 ? iteratee$1(array[i]) : array[i];
        if (current !== undefined) {
            if (result === undefined) {
                result = current;
            }
            else {
                result += current;
            }
        }
    }
    return result;
}

function sum(array) {
    return sumBy(array);
}

function isPrototype(value) {
    const constructor = value?.constructor;
    const prototype = typeof constructor === 'function' ? constructor.prototype : Object.prototype;
    return value === prototype;
}

function isTypedArray(x) {
    return isPlainObject$1.isTypedArray(x);
}

function times(n, getValue) {
    n = toInteger(n);
    if (n < 1 || !Number.isSafeInteger(n)) {
        return [];
    }
    const result = new Array(n);
    for (let i = 0; i < n; i++) {
        result[i] = typeof getValue === 'function' ? getValue(i) : i;
    }
    return result;
}

function keysIn(object) {
    if (object == null) {
        return [];
    }
    switch (typeof object) {
        case 'object':
        case 'function': {
            if (isArrayLike(object)) {
                return arrayLikeKeysIn(object);
            }
            if (isPrototype(object)) {
                return prototypeKeysIn(object);
            }
            return keysInImpl(object);
        }
        default: {
            return keysInImpl(Object(object));
        }
    }
}
function keysInImpl(object) {
    const result = [];
    for (const key in object) {
        result.push(key);
    }
    return result;
}
function prototypeKeysIn(object) {
    const keys = keysInImpl(object);
    return keys.filter(key => key !== 'constructor');
}
function arrayLikeKeysIn(object) {
    const indices = times(object.length, index => `${index}`);
    const filteredKeys = new Set(indices);
    if (isWeakSet$1.isBuffer(object)) {
        filteredKeys.add('offset');
        filteredKeys.add('parent');
    }
    if (isTypedArray(object)) {
        filteredKeys.add('buffer');
        filteredKeys.add('byteLength');
        filteredKeys.add('byteOffset');
    }
    return [...indices, ...keysInImpl(object).filter(key => !filteredKeys.has(key))];
}

function assignIn(object, ...sources) {
    for (let i = 0; i < sources.length; i++) {
        assignInImpl(object, sources[i]);
    }
    return object;
}
function assignInImpl(object, source) {
    const keys = keysIn(source);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        if (!(key in object) || !isWeakSet$1.eq(object[key], source[key])) {
            object[key] = source[key];
        }
    }
}

function defaults(object, ...sources) {
    object = Object(object);
    const objectProto = Object.prototype;
    for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        const keys = Object.keys(source);
        for (let j = 0; j < keys.length; j++) {
            const key = keys[j];
            const value = object[key];
            if (value === undefined ||
                (!Object.hasOwn(object, key) && isWeakSet$1.eq(value, objectProto[key]))) {
                object[key] = source[key];
            }
        }
    }
    return object;
}

function findKey(obj, predicate) {
    if (!isObject(obj)) {
        return undefined;
    }
    return findKeyImpl(obj, predicate);
}
function findKeyImpl(obj, predicate) {
    if (typeof predicate === 'function') {
        return toMerged.findKey(obj, predicate);
    }
    if (typeof predicate === 'object') {
        if (Array.isArray(predicate)) {
            const key = predicate[0];
            const value = predicate[1];
            return toMerged.findKey(obj, matchesProperty(key, value));
        }
        return toMerged.findKey(obj, matches(predicate));
    }
    if (typeof predicate === 'string') {
        return toMerged.findKey(obj, property(predicate));
    }
}

function fromPairs(pairs) {
    if (!isArrayLike(pairs) && !(pairs instanceof Map)) {
        return {};
    }
    const result = {};
    for (const [key, value] of pairs) {
        result[key] = value;
    }
    return result;
}

function invertBy(object, iteratee) {
    const result = {};
    if (isWeakSet$1.isNil(object)) {
        return result;
    }
    if (iteratee == null) {
        iteratee = unary.identity;
    }
    const keys = Object.keys(object);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = object[key];
        const valueStr = iteratee(value);
        if (Array.isArray(result[valueStr])) {
            result[valueStr].push(key);
        }
        else {
            result[valueStr] = [key];
        }
    }
    return result;
}

function keys(object) {
    if (isArrayLike(object)) {
        return arrayLikeKeys(object);
    }
    const result = Object.keys(Object(object));
    if (!isPrototype(object)) {
        return result;
    }
    return result.filter(key => key !== 'constructor');
}
function arrayLikeKeys(object) {
    const indices = times(object.length, index => `${index}`);
    const filteredKeys = new Set(indices);
    if (isWeakSet$1.isBuffer(object)) {
        filteredKeys.add('offset');
        filteredKeys.add('parent');
    }
    if (isTypedArray(object)) {
        filteredKeys.add('buffer');
        filteredKeys.add('byteLength');
        filteredKeys.add('byteOffset');
    }
    return [...indices, ...Object.keys(object).filter(key => !filteredKeys.has(key))];
}

function mapKeys(object, getNewKey) {
    getNewKey = getNewKey ?? unary.identity;
    switch (typeof getNewKey) {
        case 'string':
        case 'symbol':
        case 'number':
        case 'object': {
            return toMerged.mapKeys(object, property(getNewKey));
        }
        case 'function': {
            return toMerged.mapKeys(object, getNewKey);
        }
    }
}

function mapValues(object, getNewValue) {
    getNewValue = getNewValue ?? unary.identity;
    switch (typeof getNewValue) {
        case 'string':
        case 'symbol':
        case 'number':
        case 'object': {
            return toMerged.mapValues(object, property(getNewValue));
        }
        case 'function': {
            return toMerged.mapValues(object, getNewValue);
        }
    }
}

function isPlainObject(object) {
    if (typeof object !== 'object') {
        return false;
    }
    if (object == null) {
        return false;
    }
    if (Object.getPrototypeOf(object) === null) {
        return true;
    }
    if (Object.prototype.toString.call(object) !== '[object Object]') {
        const tag = object[Symbol.toStringTag];
        if (tag == null) {
            return false;
        }
        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;
        if (isTagReadonly) {
            return false;
        }
        return object.toString() === `[object ${tag}]`;
    }
    let proto = object;
    while (Object.getPrototypeOf(proto) !== null) {
        proto = Object.getPrototypeOf(proto);
    }
    return Object.getPrototypeOf(object) === proto;
}

function mergeWith(object, ...otherArgs) {
    const sources = otherArgs.slice(0, -1);
    const merge = otherArgs[otherArgs.length - 1];
    let result = object;
    for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        result = mergeWithDeep(result, source, merge, new Map());
    }
    return result;
}
function mergeWithDeep(target, source, merge, stack) {
    if (isPlainObject$1.isPrimitive(target)) {
        target = Object(target);
    }
    if (source == null || typeof source !== 'object') {
        return target;
    }
    if (stack.has(source)) {
        return toMerged.clone(stack.get(source));
    }
    stack.set(source, target);
    if (Array.isArray(source)) {
        source = source.slice();
        for (let i = 0; i < source.length; i++) {
            source[i] = source[i] ?? undefined;
        }
    }
    const sourceKeys = [...Object.keys(source), ...isPlainObject$1.getSymbols(source)];
    for (let i = 0; i < sourceKeys.length; i++) {
        const key = sourceKeys[i];
        let sourceValue = source[key];
        let targetValue = target[key];
        if (isArguments(sourceValue)) {
            sourceValue = { ...sourceValue };
        }
        if (isArguments(targetValue)) {
            targetValue = { ...targetValue };
        }
        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(sourceValue)) {
            sourceValue = cloneDeep(sourceValue);
        }
        if (Array.isArray(sourceValue)) {
            if (typeof targetValue === 'object' && targetValue != null) {
                const cloned = [];
                const targetKeys = Reflect.ownKeys(targetValue);
                for (let i = 0; i < targetKeys.length; i++) {
                    const targetKey = targetKeys[i];
                    cloned[targetKey] = targetValue[targetKey];
                }
                targetValue = cloned;
            }
            else {
                targetValue = [];
            }
        }
        const merged = merge(targetValue, sourceValue, key, target, source, stack);
        if (merged != null) {
            target[key] = merged;
        }
        else if (Array.isArray(sourceValue)) {
            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);
        }
        else if (toMerged.isObjectLike(targetValue) && toMerged.isObjectLike(sourceValue)) {
            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);
        }
        else if (targetValue == null && isPlainObject(sourceValue)) {
            target[key] = mergeWithDeep({}, sourceValue, merge, stack);
        }
        else if (targetValue == null && isTypedArray(sourceValue)) {
            target[key] = cloneDeep(sourceValue);
        }
        else if (targetValue === undefined || sourceValue !== undefined) {
            target[key] = sourceValue;
        }
    }
    return target;
}

function merge(object, ...sources) {
    return mergeWith(object, ...sources, noop.noop);
}

function unset(obj, path) {
    if (obj == null) {
        return true;
    }
    switch (typeof path) {
        case 'symbol':
        case 'number':
        case 'object': {
            if (Array.isArray(path)) {
                return unsetWithPath(obj, path);
            }
            if (typeof path === 'number') {
                path = toKey(path);
            }
            else if (typeof path === 'object') {
                if (Object.is(path?.valueOf(), -0)) {
                    path = '-0';
                }
                else {
                    path = String(path);
                }
            }
            if (obj?.[path] === undefined) {
                return true;
            }
            try {
                delete obj[path];
                return true;
            }
            catch {
                return false;
            }
        }
        case 'string': {
            if (obj?.[path] === undefined && isDeepKey(path)) {
                return unsetWithPath(obj, toPath(path));
            }
            try {
                delete obj[path];
                return true;
            }
            catch {
                return false;
            }
        }
    }
}
function unsetWithPath(obj, path) {
    const parent = get(obj, path.slice(0, -1), obj);
    const lastKey = path[path.length - 1];
    if (parent?.[lastKey] === undefined) {
        return true;
    }
    try {
        delete parent[lastKey];
        return true;
    }
    catch {
        return false;
    }
}

function omit(obj, ...keysArr) {
    if (obj == null) {
        return {};
    }
    const result = toMerged.cloneDeep(obj);
    for (let i = 0; i < keysArr.length; i++) {
        let keys = keysArr[i];
        switch (typeof keys) {
            case 'object': {
                if (!Array.isArray(keys)) {
                    keys = Array.from(keys);
                }
                for (let j = 0; j < keys.length; j++) {
                    const key = keys[j];
                    unset(result, key);
                }
                break;
            }
            case 'string':
            case 'symbol':
            case 'number': {
                unset(result, keys);
                break;
            }
        }
    }
    return result;
}

function pick(obj, ...keysArr) {
    if (isNil(obj)) {
        return {};
    }
    const result = {};
    for (let i = 0; i < keysArr.length; i++) {
        let keys = keysArr[i];
        switch (typeof keys) {
            case 'object': {
                if (!Array.isArray(keys)) {
                    keys = Array.from(keys);
                }
                break;
            }
            case 'string':
            case 'symbol':
            case 'number': {
                keys = [keys];
                break;
            }
        }
        for (const key of keys) {
            const value = get(obj, key);
            if (value === undefined && !has(obj, key)) {
                continue;
            }
            if (typeof key === 'string' && Object.hasOwn(obj, key)) {
                result[key] = value;
            }
            else {
                set(result, key, value);
            }
        }
    }
    return result;
}

function pickBy(obj, shouldPick) {
    if (obj == null) {
        return {};
    }
    const result = {};
    if (shouldPick == null) {
        return obj;
    }
    const keys = isArrayLike(obj) ? range$1.range(0, obj.length) : Object.keys(obj);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i].toString();
        const value = obj[key];
        if (shouldPick(value, key, obj)) {
            result[key] = value;
        }
    }
    return result;
}

function propertyOf(object) {
    return function (path) {
        return get(object, path);
    };
}

function toDefaulted(object, ...sources) {
    const cloned = cloneDeep(object);
    return defaults(cloned, ...sources);
}

function values(object) {
    return Object.values(object);
}

function valuesIn(object) {
    const keys = keysIn(object);
    const result = new Array(keys.length);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        result[i] = object[key];
    }
    return result;
}

function conformsTo(target, source) {
    if (source == null) {
        return true;
    }
    if (target == null) {
        return Object.keys(source).length === 0;
    }
    const keys = Object.keys(source);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const predicate = source[key];
        const value = target[key];
        if ((value === undefined && !(key in target)) || !predicate(value)) {
            return false;
        }
    }
    return true;
}

function conforms(source) {
    source = toMerged.cloneDeep(source);
    return function (object) {
        return conformsTo(object, source);
    };
}

function isArrayBuffer(value) {
    return isWeakSet$1.isArrayBuffer(value);
}

function isBoolean(value) {
    return typeof value === 'boolean' || value instanceof Boolean;
}

function isBuffer(x) {
    return isWeakSet$1.isBuffer(x);
}

function isDate(value) {
    return isWeakSet$1.isDate(value);
}

function isElement(value) {
    return toMerged.isObjectLike(value) && value.nodeType === 1 && !isPlainObject(value);
}

function isEmpty(value) {
    if (value == null) {
        return true;
    }
    if (isArrayLike(value)) {
        if (typeof value.splice !== 'function' &&
            typeof value !== 'string' &&
            (typeof Buffer === 'undefined' || !Buffer.isBuffer(value)) &&
            !isTypedArray(value) &&
            !isArguments(value)) {
            return false;
        }
        return value.length === 0;
    }
    if (typeof value === 'object') {
        if (value instanceof Map || value instanceof Set) {
            return value.size === 0;
        }
        const keys = Object.keys(value);
        if (isPrototype(value)) {
            return keys.filter(x => x !== 'constructor').length === 0;
        }
        return keys.length === 0;
    }
    return true;
}

function isEqualWith(a, b, areValuesEqual = noop.noop) {
    if (typeof areValuesEqual !== 'function') {
        areValuesEqual = noop.noop;
    }
    return isWeakSet$1.isEqualWith(a, b, (...args) => {
        const result = areValuesEqual(...args);
        if (result !== undefined) {
            return Boolean(result);
        }
        if (a instanceof Map && b instanceof Map) {
            return isEqualWith(Array.from(a), Array.from(b), unary.after(2, areValuesEqual));
        }
        if (a instanceof Set && b instanceof Set) {
            return isEqualWith(Array.from(a), Array.from(b), unary.after(2, areValuesEqual));
        }
    });
}

function isError(value) {
    return isPlainObject$1.getTag(value) === '[object Error]';
}

function isFinite(value) {
    return Number.isFinite(value);
}

function isInteger(value) {
    return Number.isInteger(value);
}

function isMap(value) {
    return isWeakSet$1.isMap(value);
}

function isRegExp(value) {
    return isWeakSet$1.isRegExp(value);
}

function isSafeInteger(value) {
    return Number.isSafeInteger(value);
}

function isSet(value) {
    return isWeakSet$1.isSet(value);
}

function isWeakMap(value) {
    return isWeakSet$1.isWeakMap(value);
}

function isWeakSet(value) {
    return isWeakSet$1.isWeakSet(value);
}

function normalizeForCase(str) {
    if (typeof str !== 'string') {
        str = toString(str);
    }
    return str.replace(/['\u2019]/g, '');
}

function camelCase(str) {
    return upperFirst$1.camelCase(normalizeForCase(str));
}

function deburr(str) {
    return upperFirst$1.deburr(toString(str));
}

function endsWith(str, target, position = str.length) {
    return str.endsWith(target, position);
}

function escape(string) {
    return upperFirst$1.escape(toString(string));
}

function escapeRegExp(str) {
    return upperFirst$1.escapeRegExp(toString(str));
}

function kebabCase(str) {
    return upperFirst$1.kebabCase(normalizeForCase(str));
}

function lowerCase(str) {
    return upperFirst$1.lowerCase(normalizeForCase(str));
}

function lowerFirst(str) {
    return upperFirst$1.lowerFirst(toString(str));
}

function pad(str, length, chars = ' ') {
    return upperFirst$1.pad(toString(str), length, chars);
}

function padEnd(str, length = 0, chars = ' ') {
    return toString(str).padEnd(length, chars);
}

function padStart(str, length = 0, chars = ' ') {
    return toString(str).padStart(length, chars);
}

function repeat(str, n) {
    return str.repeat(n);
}

function replace(target = '', pattern, replacement) {
    if (arguments.length < 3) {
        return toString(target);
    }
    return toString(target).replace(pattern, replacement);
}

function snakeCase(str) {
    return upperFirst$1.snakeCase(normalizeForCase(str));
}

function startCase(str) {
    const words = upperFirst$1.words(normalizeForCase(str).trim());
    let result = '';
    for (let i = 0; i < words.length; i++) {
        const word = words[i];
        if (result) {
            result += ' ';
        }
        if (word === word.toUpperCase()) {
            result += word;
        }
        else {
            result += word[0].toUpperCase() + word.slice(1).toLowerCase();
        }
    }
    return result;
}

function startsWith(str, target, position = 0) {
    return str.startsWith(target, position);
}

const esTemplateRegExp = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;
const unEscapedRegExp = /['\n\r\u2028\u2029\\]/g;
const noMatchExp = /($^)/;
const escapeMap = new Map([
    ['\\', '\\'],
    ["'", "'"],
    ['\n', 'n'],
    ['\r', 'r'],
    ['\u2028', 'u2028'],
    ['\u2029', 'u2029'],
]);
function escapeString(match) {
    return `\\${escapeMap.get(match)}`;
}
const templateSettings = {
    escape: /<%-([\s\S]+?)%>/g,
    evaluate: /<%([\s\S]+?)%>/g,
    interpolate: /<%=([\s\S]+?)%>/g,
    variable: '',
    imports: {
        _: {
            escape,
            template,
        },
    },
};
function template(string, options, guard) {
    string = toString(string);
    if (guard) {
        options = templateSettings;
    }
    options = defaults({ ...options }, templateSettings);
    const delimitersRegExp = new RegExp([
        options.escape?.source ?? noMatchExp.source,
        options.interpolate?.source ?? noMatchExp.source,
        options.interpolate ? esTemplateRegExp.source : noMatchExp.source,
        options.evaluate?.source ?? noMatchExp.source,
        '$',
    ].join('|'), 'g');
    let lastIndex = 0;
    let isEvaluated = false;
    let source = `__p += ''`;
    for (const match of string.matchAll(delimitersRegExp)) {
        const [fullMatch, escapeValue, interpolateValue, esTemplateValue, evaluateValue] = match;
        const { index } = match;
        source += ` + '${string.slice(lastIndex, index).replace(unEscapedRegExp, escapeString)}'`;
        if (escapeValue) {
            source += ` + _.escape(${escapeValue})`;
        }
        if (interpolateValue) {
            source += ` + ((${interpolateValue}) == null ? '' : ${interpolateValue})`;
        }
        else if (esTemplateValue) {
            source += ` + ((${esTemplateValue}) == null ? '' : ${esTemplateValue})`;
        }
        if (evaluateValue) {
            source += `;\n${evaluateValue};\n __p += ''`;
            isEvaluated = true;
        }
        lastIndex = index + fullMatch.length;
    }
    const imports = defaults({ ...options.imports }, templateSettings.imports);
    const importsKeys = Object.keys(imports);
    const importValues = Object.values(imports);
    const sourceURL = `//# sourceURL=${options.sourceURL ? String(options.sourceURL).replace(/[\r\n]/g, ' ') : `es-toolkit.templateSource[${Date.now()}]`}\n`;
    const compiledFunction = `function(${options.variable || 'obj'}) {
    let __p = '';
    ${options.variable ? '' : 'if (obj == null) { obj = {}; }'}
    ${isEvaluated ? `function print() { __p += Array.prototype.join.call(arguments, ''); }` : ''}
    ${options.variable ? source : `with(obj) {\n${source}\n}`}
    return __p;
  }`;
    const result = attempt(() => new Function(...importsKeys, `${sourceURL}return ${compiledFunction}`)(...importValues));
    result.source = compiledFunction;
    if (result instanceof Error) {
        throw result;
    }
    return result;
}

function toLower(value) {
    return toString(value).toLowerCase();
}

function toUpper(value) {
    return toString(value).toUpperCase();
}

function trim(str, chars, guard) {
    if (str == null) {
        return '';
    }
    if (guard != null || chars == null) {
        return str.toString().trim();
    }
    switch (typeof chars) {
        case 'string': {
            return upperFirst$1.trim(str, chars.toString().split(''));
        }
        case 'object': {
            if (Array.isArray(chars)) {
                return upperFirst$1.trim(str, chars.flatMap(x => x.toString().split('')));
            }
            else {
                return upperFirst$1.trim(str, chars.toString().split(''));
            }
        }
    }
}

function trimEnd(str, chars, guard) {
    if (str == null) {
        return '';
    }
    if (guard != null || chars == null) {
        return str.toString().trimEnd();
    }
    switch (typeof chars) {
        case 'string': {
            return upperFirst$1.trimEnd(str, chars.toString().split(''));
        }
        case 'object': {
            if (Array.isArray(chars)) {
                return upperFirst$1.trimEnd(str, chars.flatMap(x => x.toString().split('')));
            }
            else {
                return upperFirst$1.trimEnd(str, chars.toString().split(''));
            }
        }
    }
}

function trimStart(str, chars, guard) {
    if (str == null) {
        return '';
    }
    if (guard != null || chars == null) {
        return str.toString().trimStart();
    }
    switch (typeof chars) {
        case 'string': {
            return upperFirst$1.trimStart(str, chars.toString().split(''));
        }
        case 'object': {
            if (Array.isArray(chars)) {
                return upperFirst$1.trimStart(str, chars.flatMap(x => x.toString().split('')));
            }
            else {
                return upperFirst$1.trimStart(str, chars.toString().split(''));
            }
        }
    }
}

function unescape(str) {
    return upperFirst$1.unescape(toString(str));
}

function upperCase(str) {
    return upperFirst$1.upperCase(normalizeForCase(str));
}

function upperFirst(str) {
    return upperFirst$1.upperFirst(toString(str));
}

function words(str, pattern = upperFirst$1.CASE_SPLIT_PATTERN) {
    const input = toString(str);
    const words = Array.from(input.match(pattern) ?? []);
    return words.filter(x => x !== '');
}

function constant(value) {
    return () => value;
}

function defaultTo(value, defaultValue) {
    if (value == null || Number.isNaN(value)) {
        return defaultValue;
    }
    return value;
}

function gt(value, other) {
    if (typeof value === 'string' && typeof other === 'string') {
        return value > other;
    }
    return toNumber(value) > toNumber(other);
}

function gte(value, other) {
    if (typeof value === 'string' && typeof other === 'string') {
        return value >= other;
    }
    return toNumber(value) >= toNumber(other);
}

function invoke(object, path, args = []) {
    if (object == null) {
        return;
    }
    switch (typeof path) {
        case 'string': {
            if (typeof object === 'object' && Object.hasOwn(object, path)) {
                return invokeImpl(object, [path], args);
            }
            return invokeImpl(object, toPath(path), args);
        }
        case 'number':
        case 'symbol': {
            return invokeImpl(object, [path], args);
        }
        default: {
            if (Array.isArray(path)) {
                return invokeImpl(object, path, args);
            }
            else {
                return invokeImpl(object, [path], args);
            }
        }
    }
}
function invokeImpl(object, path, args) {
    const parent = get(object, path.slice(0, -1), object);
    if (parent == null) {
        return undefined;
    }
    let lastKey = last(path);
    let lastValue = lastKey?.valueOf();
    if (typeof lastValue === 'number') {
        lastKey = toKey(lastValue);
    }
    else {
        lastKey = String(lastKey);
    }
    const func = get(parent, lastKey);
    return func?.apply(parent, args);
}

function lt(value, other) {
    if (typeof value === 'string' && typeof other === 'string') {
        return value < other;
    }
    return toNumber(value) < toNumber(other);
}

function lte(value, other) {
    if (typeof value === 'string' && typeof other === 'string') {
        return value <= other;
    }
    return toNumber(value) <= toNumber(other);
}

function method(path, ...args) {
    return function (object) {
        return invoke(object, path, args);
    };
}

function methodOf(object, ...args) {
    return function (path) {
        return invoke(object, path, args);
    };
}

function now() {
    return Date.now();
}

function stubArray() {
    return [];
}

function stubFalse() {
    return false;
}

function stubObject() {
    return {};
}

function stubString() {
    return '';
}

function stubTrue() {
    return true;
}

function toArray(value) {
    if (value == null) {
        return [];
    }
    if (isArrayLike(value) || isMap(value)) {
        return Array.from(value);
    }
    if (typeof value === 'object') {
        return Object.values(value);
    }
    return [];
}

const MAX_ARRAY_LENGTH = 4_294_967_295;

function toLength(value) {
    if (value == null) {
        return 0;
    }
    const length = Math.floor(Number(value));
    return clamp(length, 0, MAX_ARRAY_LENGTH);
}

function toPlainObject(value) {
    const plainObject = {};
    const valueKeys = keysIn(value);
    for (let i = 0; i < valueKeys.length; i++) {
        const key = valueKeys[i];
        const objValue = value[key];
        if (key === '__proto__') {
            Object.defineProperty(plainObject, key, {
                configurable: true,
                enumerable: true,
                value: objValue,
                writable: true,
            });
        }
        else {
            plainObject[key] = objValue;
        }
    }
    return plainObject;
}

const MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER;

function toSafeInteger(value) {
    if (value == null) {
        return 0;
    }
    return clamp(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);
}

let idCounter = 0;
function uniqueId(prefix = '') {
    const id = ++idCounter;
    return `${prefix}${id}`;
}

exports.at = zipWith.at;
exports.countBy = zipWith.countBy;
exports.flatMap = zipWith.flatMap;
exports.flatMapDeep = zipWith.flatMapDeep;
exports.forEachRight = zipWith.forEachRight;
exports.groupBy = zipWith.groupBy;
exports.initial = zipWith.initial;
exports.isSubset = zipWith.isSubset;
exports.isSubsetWith = zipWith.isSubsetWith;
exports.keyBy = zipWith.keyBy;
exports.minBy = zipWith.minBy;
exports.partition = zipWith.partition;
exports.pullAt = zipWith.pullAt;
exports.sampleSize = zipWith.sampleSize;
exports.shuffle = zipWith.shuffle;
exports.takeWhile = zipWith.takeWhile;
exports.toFilled = zipWith.toFilled;
exports.unionBy = zipWith.unionBy;
exports.unionWith = zipWith.unionWith;
exports.uniqWith = zipWith.uniqWith;
exports.unzipWith = zipWith.unzipWith;
exports.windowed = zipWith.windowed;
exports.xor = zipWith.xor;
exports.xorBy = zipWith.xorBy;
exports.xorWith = zipWith.xorWith;
exports.zipObject = zipWith.zipObject;
exports.zipWith = zipWith.zipWith;
exports.AbortError = AbortError.AbortError;
exports.TimeoutError = error_index.TimeoutError;
exports.asyncNoop = unary.asyncNoop;
exports.identity = unary.identity;
exports.memoize = unary.memoize;
exports.once = unary.once;
exports.partial = unary.partial;
exports.partialRight = unary.partialRight;
exports.retry = unary.retry;
exports.unary = unary.unary;
exports.noop = noop.noop;
exports.mean = range$1.mean;
exports.meanBy = range$1.meanBy;
exports.median = range$1.median;
exports.medianBy = range$1.medianBy;
exports.randomInt = randomInt.randomInt;
exports.clone = toMerged.clone;
exports.flattenObject = toMerged.flattenObject;
exports.invert = toMerged.invert;
exports.isObjectLike = toMerged.isObjectLike;
exports.omitBy = toMerged.omitBy;
exports.toMerged = toMerged.toMerged;
exports.isPrimitive = isPlainObject$1.isPrimitive;
exports.eq = isWeakSet$1.eq;
exports.isBlob = isWeakSet$1.isBlob;
exports.isEqual = isWeakSet$1.isEqual;
exports.isFile = isWeakSet$1.isFile;
exports.isFunction = isWeakSet$1.isFunction;
exports.isJSON = isWeakSet$1.isJSON;
exports.isJSONArray = isWeakSet$1.isJSONArray;
exports.isJSONObject = isWeakSet$1.isJSONObject;
exports.isJSONValue = isWeakSet$1.isJSONValue;
exports.isLength = isWeakSet$1.isLength;
exports.isNotNil = isWeakSet$1.isNotNil;
exports.isNull = isWeakSet$1.isNull;
exports.isPromise = isWeakSet$1.isPromise;
exports.isUndefined = isWeakSet$1.isUndefined;
exports.Mutex = promise_index.Mutex;
exports.Semaphore = promise_index.Semaphore;
exports.timeout = promise_index.timeout;
exports.withTimeout = promise_index.withTimeout;
exports.capitalize = upperFirst$1.capitalize;
exports.constantCase = upperFirst$1.constantCase;
exports.pascalCase = upperFirst$1.pascalCase;
exports.reverseString = upperFirst$1.reverseString;
exports.invariant = util_index.invariant;
exports.add = add;
exports.after = after;
exports.ary = ary;
exports.assignIn = assignIn;
exports.attempt = attempt;
exports.before = before;
exports.bind = bind;
exports.bindKey = bindKey;
exports.camelCase = camelCase;
exports.castArray = castArray;
exports.ceil = ceil;
exports.chunk = chunk;
exports.clamp = clamp;
exports.cloneDeep = cloneDeep;
exports.cloneDeepWith = cloneDeepWith;
exports.compact = compact;
exports.concat = concat;
exports.conforms = conforms;
exports.conformsTo = conformsTo;
exports.constant = constant;
exports.curry = curry;
exports.curryRight = curryRight;
exports.debounce = debounce;
exports.deburr = deburr;
exports.defaultTo = defaultTo;
exports.defaults = defaults;
exports.defer = defer;
exports.delay = delay;
exports.difference = difference;
exports.differenceBy = differenceBy;
exports.differenceWith = differenceWith;
exports.divide = divide;
exports.drop = drop;
exports.dropRight = dropRight;
exports.dropRightWhile = dropRightWhile;
exports.dropWhile = dropWhile;
exports.each = forEach;
exports.endsWith = endsWith;
exports.escape = escape;
exports.escapeRegExp = escapeRegExp;
exports.every = every;
exports.extend = assignIn;
exports.fill = fill;
exports.filter = filter;
exports.find = find;
exports.findIndex = findIndex;
exports.findKey = findKey;
exports.findLast = findLast;
exports.findLastIndex = findLastIndex;
exports.first = head;
exports.flatten = flatten;
exports.flattenDeep = flattenDeep;
exports.flattenDepth = flattenDepth;
exports.flip = flip;
exports.floor = floor;
exports.flow = flow;
exports.flowRight = flowRight;
exports.forEach = forEach;
exports.fromPairs = fromPairs;
exports.get = get;
exports.gt = gt;
exports.gte = gte;
exports.has = has;
exports.head = head;
exports.inRange = inRange;
exports.includes = includes;
exports.indexOf = indexOf;
exports.intersection = intersection;
exports.intersectionBy = intersectionBy;
exports.intersectionWith = intersectionWith;
exports.invertBy = invertBy;
exports.invoke = invoke;
exports.isArguments = isArguments;
exports.isArray = isArray;
exports.isArrayBuffer = isArrayBuffer;
exports.isArrayLike = isArrayLike;
exports.isArrayLikeObject = isArrayLikeObject;
exports.isBoolean = isBoolean;
exports.isBuffer = isBuffer;
exports.isDate = isDate;
exports.isElement = isElement;
exports.isEmpty = isEmpty;
exports.isEqualWith = isEqualWith;
exports.isError = isError;
exports.isFinite = isFinite;
exports.isInteger = isInteger;
exports.isMap = isMap;
exports.isMatch = isMatch;
exports.isNaN = isNaN;
exports.isNil = isNil;
exports.isNumber = isNumber;
exports.isObject = isObject;
exports.isPlainObject = isPlainObject;
exports.isRegExp = isRegExp;
exports.isSafeInteger = isSafeInteger;
exports.isSet = isSet;
exports.isString = isString;
exports.isSymbol = isSymbol;
exports.isTypedArray = isTypedArray;
exports.isWeakMap = isWeakMap;
exports.isWeakSet = isWeakSet;
exports.iteratee = iteratee;
exports.join = join;
exports.kebabCase = kebabCase;
exports.keys = keys;
exports.keysIn = keysIn;
exports.last = last;
exports.lastIndexOf = lastIndexOf;
exports.lowerCase = lowerCase;
exports.lowerFirst = lowerFirst;
exports.lt = lt;
exports.lte = lte;
exports.map = map;
exports.mapKeys = mapKeys;
exports.mapValues = mapValues;
exports.matches = matches;
exports.matchesProperty = matchesProperty;
exports.max = max;
exports.maxBy = maxBy;
exports.merge = merge;
exports.mergeWith = mergeWith;
exports.method = method;
exports.methodOf = methodOf;
exports.min = min;
exports.multiply = multiply;
exports.negate = negate;
exports.now = now;
exports.nth = nth;
exports.nthArg = nthArg;
exports.omit = omit;
exports.orderBy = orderBy;
exports.pad = pad;
exports.padEnd = padEnd;
exports.padStart = padStart;
exports.parseInt = parseInt;
exports.pick = pick;
exports.pickBy = pickBy;
exports.property = property;
exports.propertyOf = propertyOf;
exports.pull = pull;
exports.pullAll = pullAll;
exports.pullAllBy = pullAllBy;
exports.random = random;
exports.range = range;
exports.rangeRight = rangeRight;
exports.rearg = rearg;
exports.reduce = reduce;
exports.reduceRight = reduceRight;
exports.remove = remove;
exports.repeat = repeat;
exports.replace = replace;
exports.rest = rest;
exports.reverse = reverse;
exports.round = round;
exports.sample = sample;
exports.set = set;
exports.size = size;
exports.slice = slice;
exports.snakeCase = snakeCase;
exports.some = some;
exports.sortBy = sortBy;
exports.sortedIndex = sortedIndex;
exports.sortedIndexBy = sortedIndexBy;
exports.spread = spread;
exports.startCase = startCase;
exports.startsWith = startsWith;
exports.stubArray = stubArray;
exports.stubFalse = stubFalse;
exports.stubObject = stubObject;
exports.stubString = stubString;
exports.stubTrue = stubTrue;
exports.subtract = subtract;
exports.sum = sum;
exports.sumBy = sumBy;
exports.tail = tail;
exports.take = take;
exports.takeRight = takeRight;
exports.takeRightWhile = takeRightWhile;
exports.template = template;
exports.templateSettings = templateSettings;
exports.throttle = throttle;
exports.times = times;
exports.toArray = toArray;
exports.toDefaulted = toDefaulted;
exports.toFinite = toFinite;
exports.toInteger = toInteger;
exports.toLength = toLength;
exports.toLower = toLower;
exports.toNumber = toNumber;
exports.toPath = toPath;
exports.toPlainObject = toPlainObject;
exports.toSafeInteger = toSafeInteger;
exports.toString = toString;
exports.toUpper = toUpper;
exports.trim = trim;
exports.trimEnd = trimEnd;
exports.trimStart = trimStart;
exports.unescape = unescape;
exports.union = union;
exports.uniq = uniq;
exports.uniqBy = uniqBy;
exports.uniqueId = uniqueId;
exports.unset = unset;
exports.unzip = unzip;
exports.upperCase = upperCase;
exports.upperFirst = upperFirst;
exports.values = values;
exports.valuesIn = valuesIn;
exports.without = without;
exports.words = words;
exports.zip = zip;
exports.zipObjectDeep = zipObjectDeep;
