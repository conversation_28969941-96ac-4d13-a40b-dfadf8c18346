/**
 * Creates a new object that reverses the keys and values of the given object, similar to the invert.
 *
 * The `iteratee` function specifies how the values are reversed into keys. If no `iteratee` function is provided, the values are used as keys as-is.
 *
 * The values of the new object are arrays of keys that correspond to the value returned by the `iteratee` function.
 *
 * @param {Record<K, V>} object - The object to iterate over.
 * @param {(value: V) => string} [iteratee] - Optional. A function that generates a key based on each value in the object.
 * If not provided, the function defaults to using the value as a string.
 *
 * @returns {Record<string, K[]>} An object where the keys are generated by the iteratee, and the values
 * are arrays of property names (keys) from the input object that correspond to those keys.
 *
 * @example
 * const obj = { a: 1, b: 2, c: 1 };
 * const result = invertBy(obj);
 * // result => { '1': ['a', 'c'], '2': ['b'] }
 *
 * @example
 * const obj = { a: 1, b: 2, c: 1 };
 * const result = invertBy(obj, value => `group${value}`);
 * // result => { 'group1': ['a', 'c'], 'group2': ['b'] }
 */
declare function invertBy<K extends PropertyKey, V>(object: Record<K, V>, iteratee?: (value: V) => string): Record<string, K[]>;

export { invertBy };
