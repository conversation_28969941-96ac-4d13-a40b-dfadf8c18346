'use strict';

function isPrimitive(value) {
    return value == null || (typeof value !== 'object' && typeof value !== 'function');
}

function isTypedArray(x) {
    return ArrayBuffer.isView(x) && !(x instanceof DataView);
}

function getSymbols(object) {
    return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));
}

function getTag(value) {
    if (value == null) {
        return value === undefined ? '[object Undefined]' : '[object Null]';
    }
    return Object.prototype.toString.call(value);
}

const regexpTag = '[object RegExp]';
const stringTag = '[object String]';
const numberTag = '[object Number]';
const booleanTag = '[object Boolean]';
const argumentsTag = '[object Arguments]';
const symbolTag = '[object Symbol]';
const dateTag = '[object Date]';
const mapTag = '[object Map]';
const setTag = '[object Set]';
const arrayTag = '[object Array]';
const functionTag = '[object Function]';
const arrayBufferTag = '[object ArrayBuffer]';
const objectTag = '[object Object]';
const errorTag = '[object Error]';
const dataViewTag = '[object DataView]';
const uint8ArrayTag = '[object Uint8Array]';
const uint8ClampedArrayTag = '[object Uint8ClampedArray]';
const uint16ArrayTag = '[object Uint16Array]';
const uint32ArrayTag = '[object Uint32Array]';
const bigUint64ArrayTag = '[object BigUint64Array]';
const int8ArrayTag = '[object Int8Array]';
const int16ArrayTag = '[object Int16Array]';
const int32ArrayTag = '[object Int32Array]';
const bigInt64ArrayTag = '[object BigInt64Array]';
const float32ArrayTag = '[object Float32Array]';
const float64ArrayTag = '[object Float64Array]';

function isPlainObject(value) {
    if (!value || typeof value !== 'object') {
        return false;
    }
    const proto = Object.getPrototypeOf(value);
    const hasObjectPrototype = proto === null ||
        proto === Object.prototype ||
        Object.getPrototypeOf(proto) === null;
    if (!hasObjectPrototype) {
        return false;
    }
    return Object.prototype.toString.call(value) === '[object Object]';
}

exports.argumentsTag = argumentsTag;
exports.arrayBufferTag = arrayBufferTag;
exports.arrayTag = arrayTag;
exports.bigInt64ArrayTag = bigInt64ArrayTag;
exports.bigUint64ArrayTag = bigUint64ArrayTag;
exports.booleanTag = booleanTag;
exports.dataViewTag = dataViewTag;
exports.dateTag = dateTag;
exports.errorTag = errorTag;
exports.float32ArrayTag = float32ArrayTag;
exports.float64ArrayTag = float64ArrayTag;
exports.functionTag = functionTag;
exports.getSymbols = getSymbols;
exports.getTag = getTag;
exports.int16ArrayTag = int16ArrayTag;
exports.int32ArrayTag = int32ArrayTag;
exports.int8ArrayTag = int8ArrayTag;
exports.isPlainObject = isPlainObject;
exports.isPrimitive = isPrimitive;
exports.isTypedArray = isTypedArray;
exports.mapTag = mapTag;
exports.numberTag = numberTag;
exports.objectTag = objectTag;
exports.regexpTag = regexpTag;
exports.setTag = setTag;
exports.stringTag = stringTag;
exports.symbolTag = symbolTag;
exports.uint16ArrayTag = uint16ArrayTag;
exports.uint32ArrayTag = uint32ArrayTag;
exports.uint8ArrayTag = uint8ArrayTag;
exports.uint8ClampedArrayTag = uint8ClampedArrayTag;
