{"version": 3, "file": "account-signer-meta.d.ts", "sourceRoot": "", "sources": ["../../src/account-signer-meta.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACnG,OAAO,EACH,sBAAsB,EACtB,+BAA+B,EAC/B,kBAAkB,EACrB,MAAM,8BAA8B,CAAC;AAGtC,OAAO,EAAE,qCAAqC,EAAE,MAAM,oBAAoB,CAAC;AAC3E,OAAO,EAAuB,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAE9E;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,WAAW,kBAAkB,CAC/B,QAAQ,SAAS,MAAM,GAAG,MAAM,EAChC,OAAO,SAAS,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAC3E,SAAQ,YAAY,CAAC,QAAQ,CAAC;IAC5B,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IACzE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;CAC5B;AAED;;GAEG;AACH,KAAK,sBAAsB,CAAC,OAAO,SAAS,iBAAiB,GAAG,iBAAiB,IAC3E,kBAAkB,GAClB,YAAY,GACZ,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAE1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,MAAM,uBAAuB,CAC/B,OAAO,SAAS,iBAAiB,GAAG,iBAAiB,EACrD,SAAS,SAAS,SAAS,sBAAsB,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,sBAAsB,CAAC,OAAO,CAAC,EAAE,IACzG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;AAEtD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,MAAM,8BAA8B,CACtC,QAAQ,SAAS,MAAM,GAAG,MAAM,EAChC,OAAO,SAAS,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,EACzE,SAAS,SAAS,SAAS,sBAAsB,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,sBAAsB,CAAC,OAAO,CAAC,EAAE,IACzG,OAAO,CAAC,+BAA+B,CAAC,QAAQ,CAAC,GAAG,qCAAqC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,GAC7G,IAAI,CACA,sBAAsB,CAAC,kBAAkB,EAAE,YAAY,GAAG,uBAAuB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EACtG,cAAc,CACjB,CAAC;AAEN;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAgB,yBAAyB,CAAC,OAAO,SAAS,iBAAiB,GAAG,iBAAiB,EAC3F,WAAW,EAAE,uBAAuB,CAAC,OAAO,CAAC,GAC9C,SAAS,OAAO,EAAE,CAIpB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,wBAAgB,gCAAgC,CAC5C,QAAQ,SAAS,MAAM,GAAG,MAAM,EAChC,OAAO,SAAS,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,EACzE,mBAAmB,SAAS,8BAA8B,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,8BAA8B,CAC1G,QAAQ,EACR,OAAO,CACV,EACH,WAAW,EAAE,mBAAmB,GAAG,SAAS,OAAO,EAAE,CAKtD"}