{"version": 3, "sources": ["../src/deduplicate-signers.ts", "../src/transaction-modifying-signer.ts", "../src/transaction-partial-signer.ts", "../src/transaction-sending-signer.ts", "../src/transaction-signer.ts", "../src/account-signer-meta.ts", "../src/add-signers.ts", "../src/fee-payer-signer.ts", "../src/message-partial-signer.ts", "../src/keypair-signer.ts", "../src/message-modifying-signer.ts", "../src/message-signer.ts", "../src/noop-signer.ts", "../src/transaction-with-single-sending-signer.ts", "../src/sign-transaction.ts", "../../text-encoding-impl/src/index.browser.ts", "../src/signable-message.ts"], "names": ["SolanaError", "SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS", "SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER", "SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER", "SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER", "SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER", "isSignerRole", "SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER", "SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER", "getAddressFromPublicKey", "signBytes", "transactions", "partiallySignTransaction", "generateKeyPair", "createKeyPairFromBytes", "createKeyPairFromPrivateKeyBytes", "is<PERSON>dd<PERSON>", "SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER", "SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER", "SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING", "SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS", "assertTransactionIsFullySigned", "compileTransaction", "transaction", "TextEncoder"], "mappings": ";;;;;;;;;AAaO,SAAS,mBACZ,OACkB,EAAA;AAClB,EAAA,MAAM,eAAyC,EAAC;AAChD,EAAA,OAAA,CAAQ,QAAQ,CAAU,MAAA,KAAA;AACtB,IAAA,IAAI,CAAC,YAAA,CAAa,MAAO,CAAA,OAAO,CAAG,EAAA;AAC/B,MAAa,YAAA,CAAA,MAAA,CAAO,OAAO,CAAI,GAAA,MAAA;AAAA,KACxB,MAAA,IAAA,YAAA,CAAa,MAAO,CAAA,OAAO,MAAM,MAAQ,EAAA;AAChD,MAAM,MAAA,IAAIA,mBAAYC,iEAA4D,EAAA;AAAA,QAC9E,SAAS,MAAO,CAAA;AAAA,OACnB,CAAA;AAAA;AACL,GACH,CAAA;AACD,EAAO,OAAA,MAAA,CAAO,OAAO,YAAY,CAAA;AACrC;ACoDO,SAAS,6BAAsD,KAGpB,EAAA;AAC9C,EAAA,OAAO,2BAA+B,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,yBAA8B,KAAA,UAAA;AAC9F;AAmBO,SAAS,mCAA4D,KAGlB,EAAA;AACtD,EAAI,IAAA,CAAC,4BAA6B,CAAA,KAAK,CAAG,EAAA;AACtC,IAAM,MAAA,IAAID,mBAAYE,kEAA6D,EAAA;AAAA,MAC/E,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;ACvCO,SAAS,2BAAoD,KAGpB,EAAA;AAC5C,EAAA,OAAO,kBAAsB,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,gBAAqB,KAAA,UAAA;AAC5E;AAmBO,SAAS,iCAA0D,KAGlB,EAAA;AACpD,EAAI,IAAA,CAAC,0BAA2B,CAAA,KAAK,CAAG,EAAA;AACpC,IAAM,MAAA,IAAIF,mBAAYG,gEAA2D,EAAA;AAAA,MAC7E,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;ACrBO,SAAS,2BAAoD,KAGpB,EAAA;AAC5C,EAAA,OAAO,yBAA6B,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,uBAA4B,KAAA,UAAA;AAC1F;AAmBO,SAAS,iCAA0D,KAGlB,EAAA;AACpD,EAAI,IAAA,CAAC,0BAA2B,CAAA,KAAK,CAAG,EAAA;AACpC,IAAM,MAAA,IAAIH,mBAAYI,gEAA2D,EAAA;AAAA,MAC7E,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;;;AC9EO,SAAS,oBAA6C,KAGpB,EAAA;AACrC,EAAA,OACI,2BAA2B,KAAK,CAAA,IAAK,6BAA6B,KAAK,CAAA,IAAK,2BAA2B,KAAK,CAAA;AAEpH;AAqBO,SAAS,0BAAmD,KAGlB,EAAA;AAC7C,EAAI,IAAA,CAAC,mBAAoB,CAAA,KAAK,CAAG,EAAA;AAC7B,IAAM,MAAA,IAAIJ,mBAAYK,wDAAmD,EAAA;AAAA,MACrE,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;;;AC2EO,SAAS,0BACZ,WACkB,EAAA;AAClB,EAAO,OAAA,kBAAA;AAAA,IACF,CAAA,WAAA,CAAY,QAAY,IAAA,EAAI,EAAA,OAAA,CAAQ,CAAY,OAAA,KAAA,QAAA,IAAY,OAAU,GAAA,OAAA,CAAQ,MAAS,GAAA,EAAG;AAAA,GAC/F;AACJ;AAuCO,SAAS,iCAOd,WAAsD,EAAA;AACpD,EAAA,OAAO,kBAAmB,CAAA;AAAA,IACtB,GAAI,WAAY,CAAA,QAAA,IAAY,mBAAoB,CAAA,WAAA,CAAY,QAAQ,CAAA,GAAI,CAAC,WAAA,CAAY,QAAmB,CAAA,GAAI,EAAC;AAAA,IAC7G,GAAG,WAAA,CAAY,YAAa,CAAA,OAAA,CAAQ,yBAAyB;AAAA,GAChE,CAAA;AACL;ACtKO,SAAS,uBAAA,CACZ,SACA,WACsC,EAAA;AACtC,EAAA,IAAI,CAAC,WAAY,CAAA,QAAA,IAAY,WAAY,CAAA,QAAA,CAAS,WAAW,CAAG,EAAA;AAC5D,IAAO,OAAA,WAAA;AAAA;AAGX,EAAA,MAAM,eAAkB,GAAA,IAAI,GAAI,CAAA,kBAAA,CAAmB,OAAO,CAAA,CAAE,GAAI,CAAA,CAAA,MAAA,KAAU,CAAC,MAAA,CAAO,OAAS,EAAA,MAAM,CAAC,CAAC,CAAA;AACnG,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,WAAA;AAAA,IACH,QAAU,EAAA,WAAA,CAAY,QAAS,CAAA,GAAA,CAAI,CAAW,OAAA,KAAA;AAC1C,MAAA,MAAM,MAAS,GAAA,eAAA,CAAgB,GAAI,CAAA,OAAA,CAAQ,OAAO,CAAA;AAClD,MAAI,IAAA,CAACC,0BAAa,OAAQ,CAAA,IAAI,KAAK,QAAY,IAAA,OAAA,IAAW,CAAC,MAAQ,EAAA;AAC/D,QAAO,OAAA,OAAA;AAAA;AAEX,MAAA,OAAO,OAAO,MAAO,CAAA,EAAE,GAAG,OAAA,EAAS,QAA8B,CAAA;AAAA,KACpE;AAAA,GACJ,CAAA;AACL;AA4CO,SAAS,8BAAA,CACZ,SACA,kBACoD,EAAA;AACpD,EAAA,MAAM,cAAiB,GAAA,sBAAA,CAAuB,kBAAkB,CAAA,GAC1D,OAAQ,CAAA,IAAA,CAAK,CAAU,MAAA,KAAA,MAAA,CAAO,OAAY,KAAA,kBAAA,CAAmB,QAAS,CAAA,OAAO,CAC7E,GAAA,MAAA;AAEN,EAAA,IAAI,CAAC,cAAA,IAAkB,kBAAmB,CAAA,YAAA,CAAa,WAAW,CAAG,EAAA;AACjE,IAAO,OAAA,kBAAA;AAAA;AAGX,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,kBAAA;AAAA,IACH,GAAI,cAAA,GAAiB,EAAE,QAAA,EAAU,gBAAmB,GAAA,IAAA;AAAA,IACpD,YAAA,EAAc,mBAAmB,YAAa,CAAA,GAAA,CAAI,iBAAe,uBAAwB,CAAA,OAAA,EAAS,WAAW,CAAC;AAAA,GACjH,CAAA;AACL;AAEA,SAAS,uBACL,OACsE,EAAA;AACtE,EAAA,OACI,CAAC,CAAC,OAAA,IACF,UAAc,IAAA,OAAA,IACd,CAAC,CAAC,OAAA,CAAQ,QACV,IAAA,OAAO,QAAQ,QAAS,CAAA,OAAA,KAAY,YACpC,CAAC,mBAAA,CAAoB,QAAQ,QAAQ,CAAA;AAE7C;;;ACtFO,SAAS,mCAAA,CAKZ,UACA,kBAC+F,EAAA;AAC/F,EAAA,MAAA,CAAO,OAAO,QAAQ,CAAA;AACtB,EAAA,MAAM,GAAM,GAAA,EAAE,GAAG,kBAAA,EAAoB,QAAS,EAAA;AAC9C,EAAA,MAAA,CAAO,OAAO,GAAG,CAAA;AACjB,EAAO,OAAA,GAAA;AACX;ACaO,SAAS,uBAAgD,KAGpB,EAAA;AACxC,EAAA,OAAO,cAAkB,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,YAAiB,KAAA,UAAA;AACpE;AAmBO,SAAS,6BAAsD,KAGlB,EAAA;AAChD,EAAI,IAAA,CAAC,sBAAuB,CAAA,KAAK,CAAG,EAAA;AAChC,IAAM,MAAA,IAAIN,mBAAYO,4DAAuD,EAAA;AAAA,MACzE,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;;;ACtDO,SAAS,gBAAyC,KAGpB,EAAA;AACjC,EACI,OAAA,SAAA,IAAa,KACb,IAAA,OAAO,KAAM,CAAA,OAAA,KAAY,YACzB,sBAAuB,CAAA,KAAK,CAC5B,IAAA,0BAAA,CAA2B,KAAK,CAAA;AAExC;AAgBO,SAAS,sBAA+C,KAGlB,EAAA;AACzC,EAAI,IAAA,CAAC,eAAgB,CAAA,KAAK,CAAG,EAAA;AACzB,IAAM,MAAA,IAAIP,mBAAYQ,qDAAgD,EAAA;AAAA,MAClE,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;AAuBA,eAAsB,wBAAwB,OAAgD,EAAA;AAC1F,EAAA,MAAM,OAAU,GAAA,MAAMC,iCAAwB,CAAA,OAAA,CAAQ,SAAS,CAAA;AAC/D,EAAA,MAAM,GAAqB,GAAA;AAAA,IACvB,OAAA;AAAA,IACA,OAAA;AAAA,IACA,YAAA,EAAc,cACV,OAAQ,CAAA,GAAA;AAAA,MACJ,QAAS,CAAA,GAAA;AAAA,QAAI,OAAM,OAAA,KACf,MAAO,CAAA,MAAA,CAAO,EAAE,CAAC,OAAO,GAAG,MAAMC,eAAU,OAAQ,CAAA,UAAA,EAAY,OAAQ,CAAA,OAAO,GAAG;AAAA;AACrF,KACJ;AAAA,IACJ,gBAAA,EAAkB,oBACd,OAAQ,CAAA,GAAA;AAAA,MACJC,cAAA,CAAa,GAAI,CAAA,OAAM,WAAe,KAAA;AAClC,QAAA,MAAM,oBAAoB,MAAMC,qCAAA,CAAyB,CAAC,OAAO,GAAG,WAAW,CAAA;AAE/E,QAAO,OAAA,MAAA,CAAO,MAAO,CAAA,EAAE,CAAC,OAAO,GAAG,iBAAkB,CAAA,UAAA,CAAW,OAAO,CAAA,EAAI,CAAA;AAAA,OAC7E;AAAA;AACL,GACR;AAEA,EAAO,OAAA,MAAA,CAAO,OAAO,GAAG,CAAA;AAC5B;AAeA,eAAsB,qBAAgD,GAAA;AAClE,EAAA,OAAO,MAAM,uBAAA,CAAwB,MAAMC,oBAAA,EAAiB,CAAA;AAChE;AAoBA,eAAsB,4BAAA,CAClB,OACA,WACsB,EAAA;AACtB,EAAA,OAAO,MAAM,uBAAwB,CAAA,MAAMC,2BAAuB,CAAA,KAAA,EAAO,WAAW,CAAC,CAAA;AACzF;AAkBA,eAAsB,sCAAA,CAClB,OACA,WACsB,EAAA;AACtB,EAAA,OAAO,MAAM,uBAAwB,CAAA,MAAMC,qCAAiC,CAAA,KAAA,EAAO,WAAW,CAAC,CAAA;AACnG;ACvHO,SAAS,yBAAkD,KAGpB,EAAA;AAC1C,EACI,OAAAC,mBAAA,CAAU,MAAM,OAAO,CAAA,IACvB,2BAA2B,KAC3B,IAAA,OAAO,MAAM,qBAA0B,KAAA,UAAA;AAE/C;AAmBO,SAAS,+BAAwD,KAGlB,EAAA;AAClD,EAAI,IAAA,CAAC,wBAAyB,CAAA,KAAK,CAAG,EAAA;AAClC,IAAM,MAAA,IAAIhB,mBAAYiB,8DAAyD,EAAA;AAAA,MAC3E,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;AChFO,SAAS,gBAAyC,KAGpB,EAAA;AACjC,EAAA,OAAO,sBAAuB,CAAA,KAAK,CAAK,IAAA,wBAAA,CAAyB,KAAK,CAAA;AAC1E;AAoBO,SAAS,sBAA+C,KAGlB,EAAA;AACzC,EAAI,IAAA,CAAC,eAAgB,CAAA,KAAK,CAAG,EAAA;AACzB,IAAM,MAAA,IAAIjB,mBAAYkB,oDAA+C,EAAA;AAAA,MACjE,SAAS,KAAM,CAAA;AAAA,KAClB,CAAA;AAAA;AAET;;;AClBO,SAAS,iBAAmD,OAAkD,EAAA;AACjH,EAAA,MAAM,GAA4B,GAAA;AAAA,IAC9B,OAAA;AAAA,IACA,YAAc,EAAA,CAAA,QAAA,KAAY,OAAQ,CAAA,OAAA,CAAQ,QAAS,CAAA,GAAA,CAAI,MAAM,MAAA,CAAO,MAAO,CAAA,EAAE,CAAC,CAAC,CAAA;AAAA,IAC/E,gBAAkB,EAAA,CAAA,YAAA,KAAgB,OAAQ,CAAA,OAAA,CAAQ,YAAa,CAAA,GAAA,CAAI,MAAM,MAAA,CAAO,MAAO,CAAA,EAAE,CAAC,CAAC;AAAA,GAC/F;AAEA,EAAO,OAAA,MAAA,CAAO,OAAO,GAAG,CAAA;AAC5B;ACKO,SAAS,4CACZ,WAC+E,EAAA;AAC/E,EAAI,IAAA;AACA,IAAA,iDAAA,CAAkD,WAAW,CAAA;AAC7D,IAAO,OAAA,IAAA;AAAA,GACH,CAAA,MAAA;AACJ,IAAO,OAAA,KAAA;AAAA;AAEf;AAwBO,SAAS,kDAGZ,WACuF,EAAA;AACvF,EAAM,MAAA,OAAA,GAAU,iCAAiC,WAAW,CAAA;AAC5D,EAAM,MAAA,cAAA,GAAiB,OAAQ,CAAA,MAAA,CAAO,0BAA0B,CAAA;AAEhE,EAAI,IAAA,cAAA,CAAe,WAAW,CAAG,EAAA;AAC7B,IAAM,MAAA,IAAIlB,mBAAYmB,+DAAwD,CAAA;AAAA;AAMlF,EAAA,MAAM,qBAAqB,cAAe,CAAA,MAAA;AAAA,IACtC,YAAU,CAAC,0BAAA,CAA2B,MAAM,CAAK,IAAA,CAAC,6BAA6B,MAAM;AAAA,GACzF;AAEA,EAAI,IAAA,kBAAA,CAAmB,SAAS,CAAG,EAAA;AAC/B,IAAM,MAAA,IAAInB,mBAAYoB,6EAAsE,CAAA;AAAA;AAEpG;;;ACtBA,eAAsB,0CAAA,CAGlB,oBACA,MACwD,EAAA;AACxD,EAAM,MAAA,EAAE,cAAgB,EAAA,gBAAA,EAAqB,GAAA,4BAAA;AAAA,IACzC,mBAAmB,gCAAiC,CAAA,kBAAkB,CAAE,CAAA,MAAA,CAAO,mBAAmB,CAAC,CAAA;AAAA,IACnG,EAAE,uBAAuB,KAAM;AAAA,GACnC;AAEA,EAAA,OAAO,MAAM,yCAAA;AAAA,IACT,kBAAA;AAAA,IACA,gBAAA;AAAA,IACA,cAAA;AAAA,IACA;AAAA,GACJ;AACJ;AAqDA,eAAsB,iCAAA,CAGlB,oBACA,MACmE,EAAA;AACnE,EAAA,MAAM,iBAAoB,GAAA,MAAM,0CAA2C,CAAA,kBAAA,EAAoB,MAAM,CAAA;AACrG,EAAAC,2CAAA,CAA+B,iBAAiB,CAAA;AAChD,EAAO,OAAA,iBAAA;AACX;AAoDA,eAAsB,wCAAA,CAEpB,aAAkC,MAAkE,EAAA;AAClG,EAAA,iDAAA,CAAkD,WAAW,CAAA;AAE7D,EAAA,MAAM,cAAc,MAAQ,EAAA,WAAA;AAC5B,EAAA,MAAM,EAAE,cAAA,EAAgB,gBAAkB,EAAA,aAAA,EAAkB,GAAA,4BAAA;AAAA,IACxD,mBAAmB,gCAAiC,CAAA,WAAW,CAAE,CAAA,MAAA,CAAO,mBAAmB,CAAC;AAAA,GAChG;AAEA,EAAA,WAAA,EAAa,cAAe,EAAA;AAC5B,EAAA,MAAM,oBAAoB,MAAM,yCAAA;AAAA,IAC5B,WAAA;AAAA,IACA,gBAAA;AAAA,IACA,cAAA;AAAA,IACA;AAAA,GACJ;AAEA,EAAA,IAAI,CAAC,aAAe,EAAA;AAChB,IAAM,MAAA,IAAIrB,mBAAYmB,+DAAwD,CAAA;AAAA;AAGlF,EAAA,WAAA,EAAa,cAAe,EAAA;AAC5B,EAAM,MAAA,CAAC,SAAS,CAAI,GAAA,MAAM,cAAc,uBAAwB,CAAA,CAAC,iBAAiB,CAAA,EAAG,MAAM,CAAA;AAC3F,EAAA,WAAA,EAAa,cAAe,EAAA;AAE5B,EAAO,OAAA,SAAA;AACX;AAUA,SAAS,4BACL,CAAA,OAAA,EACA,MAA8C,GAAA,EAK/C,EAAA;AAEC,EAAM,MAAA,qBAAA,GAAwB,OAAO,qBAAyB,IAAA,IAAA;AAC9D,EAAA,MAAM,aAAgB,GAAA,qBAAA,GAAwB,gCAAiC,CAAA,OAAO,CAAI,GAAA,IAAA;AAK1F,EAAA,MAAM,eAAe,OAAQ,CAAA,MAAA;AAAA,IACzB,CAAC,WACG,MAAW,KAAA,aAAA,KAAkB,6BAA6B,MAAM,CAAA,IAAK,2BAA2B,MAAM,CAAA;AAAA,GAC9G;AAGA,EAAM,MAAA,gBAAA,GAAmB,oCAAoC,YAAY,CAAA;AAGzE,EAAM,MAAA,cAAA,GAAiB,YAClB,CAAA,MAAA,CAAO,0BAA0B,CAAA,CACjC,MAAO,CAAA,CAAA,MAAA,KAAU,CAAE,gBAAA,CAAyC,QAAS,CAAA,MAAM,CAAC,CAAA;AAEjF,EAAA,OAAO,OAAO,MAAO,CAAA,EAAE,gBAAkB,EAAA,cAAA,EAAgB,eAAe,CAAA;AAC5E;AAGA,SAAS,iCAAiC,OAAwE,EAAA;AAE9G,EAAM,MAAA,cAAA,GAAiB,OAAQ,CAAA,MAAA,CAAO,0BAA0B,CAAA;AAChE,EAAI,IAAA,cAAA,CAAe,MAAW,KAAA,CAAA,EAAU,OAAA,IAAA;AAGxC,EAAA,MAAM,qBAAqB,cAAe,CAAA,MAAA;AAAA,IACtC,YAAU,CAAC,4BAAA,CAA6B,MAAM,CAAK,IAAA,CAAC,2BAA2B,MAAM;AAAA,GACzF;AACA,EAAI,IAAA,kBAAA,CAAmB,SAAS,CAAG,EAAA;AAC/B,IAAA,OAAO,mBAAmB,CAAC,CAAA;AAAA;AAI/B,EAAA,OAAO,eAAe,CAAC,CAAA;AAC3B;AAGA,SAAS,oCACL,OACqC,EAAA;AAErC,EAAM,MAAA,gBAAA,GAAmB,OAAQ,CAAA,MAAA,CAAO,4BAA4B,CAAA;AACpE,EAAA,IAAI,gBAAiB,CAAA,MAAA,KAAW,CAAG,EAAA,OAAO,EAAC;AAG3C,EAAA,MAAM,oBAAoB,gBAAiB,CAAA,MAAA,CAAO,YAAU,CAAC,0BAAA,CAA2B,MAAM,CAAC,CAAA;AAC/F,EAAI,IAAA,iBAAA,CAAkB,MAAS,GAAA,CAAA,EAAU,OAAA,iBAAA;AAGzC,EAAO,OAAA,CAAC,gBAAiB,CAAA,CAAC,CAAC,CAAA;AAC/B;AAMA,eAAe,yCAAA,CAGX,oBACA,gBAA0D,GAAA,IAC1D,cAAsD,GAAA,IACtD,MACwD,EAAA;AAExD,EAAM,MAAA,WAAA,GAAcG,gCAAmB,kBAAkB,CAAA;AAGzD,EAAM,MAAA,mBAAA,GAAsB,MAAM,gBAAiB,CAAA,MAAA;AAAA,IAC/C,OAAOC,cAAa,eAAoB,KAAA;AACpC,MAAA,MAAA,EAAQ,aAAa,cAAe,EAAA;AACpC,MAAM,MAAA,CAAC,EAAE,CAAA,GAAI,MAAM,eAAA,CAAgB,0BAA0B,CAAC,MAAMA,YAAW,CAAA,EAAG,MAAM,CAAA;AACxF,MAAO,OAAA,MAAA,CAAO,OAAO,EAAE,CAAA;AAAA,KAC3B;AAAA,IACA,OAAA,CAAQ,QAAQ,WAAW;AAAA,GAC/B;AAGA,EAAA,MAAA,EAAQ,aAAa,cAAe,EAAA;AACpC,EAAM,MAAA,qBAAA,GAAwB,MAAM,OAAQ,CAAA,GAAA;AAAA,IACxC,cAAA,CAAe,GAAI,CAAA,OAAM,aAAiB,KAAA;AACtC,MAAM,MAAA,CAAC,UAAU,CAAI,GAAA,MAAM,cAAc,gBAAiB,CAAA,CAAC,mBAAmB,CAAA,EAAG,MAAM,CAAA;AACvF,MAAO,OAAA,UAAA;AAAA,KACV;AAAA,GACL;AACA,EAAA,MAAM,iBAAqE,GAAA;AAAA,IACvE,GAAG,mBAAA;AAAA,IACH,YAAY,MAAO,CAAA,MAAA;AAAA,MACf,qBAAsB,CAAA,MAAA,CAAO,CAAC,UAAA,EAAY,mBAAwB,KAAA;AAC9D,QAAA,OAAO,EAAE,GAAG,UAAY,EAAA,GAAG,mBAAoB,EAAA;AAAA,OAChD,EAAA,mBAAA,CAAoB,UAAc,IAAA,EAAE;AAAA;AAC3C,GACJ;AAEA,EAAO,OAAA,MAAA,CAAO,OAAO,iBAAiB,CAAA;AAC1C;ACtXO,IACMC,IAAc,UAAW,CAAA,WAAA;;;AC2C/B,SAAS,qBACZ,CAAA,OAAA,EACA,UAAkC,GAAA,EACnB,EAAA;AACf,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,OAAA,EAAS,OAAO,OAAY,KAAA,QAAA,GAAW,IAAI,CAAY,EAAA,CAAE,MAAO,CAAA,OAAO,CAAI,GAAA,OAAA;AAAA,IAC3E,YAAY,MAAO,CAAA,MAAA,CAAO,EAAE,GAAG,YAAY;AAAA,GAC9C,CAAA;AACL", "file": "index.browser.cjs", "sourcesContent": ["import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS, SolanaError } from '@solana/errors';\n\nimport { MessageSigner } from './message-signer';\nimport { TransactionSigner } from './transaction-signer';\n\n/**\n * Removes all duplicated {@link MessageSigner | MessageSigners} and\n * {@link TransactionSigner | TransactionSigners} from a provided array\n * by comparing their {@link Address | addresses}.\n *\n * @internal\n */\nexport function deduplicateSigners<TSigner extends MessageSigner | TransactionSigner>(\n    signers: readonly TSigner[],\n): readonly TSigner[] {\n    const deduplicated: Record<Address, TSigner> = {};\n    signers.forEach(signer => {\n        if (!deduplicated[signer.address]) {\n            deduplicated[signer.address] = signer;\n        } else if (deduplicated[signer.address] !== signer) {\n            throw new SolanaError(SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS, {\n                address: signer.address,\n            });\n        }\n    });\n    return Object.values(deduplicated);\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER, SolanaError } from '@solana/errors';\nimport { Transaction } from '@solana/transactions';\n\nimport { BaseTransactionSignerConfig } from './types';\n\n/**\n * The configuration to optionally provide when calling the\n * {@link TransactionModifyingSigner#modifyAndSignTransactions | modifyAndSignTransactions} method.\n *\n * @see {@link BaseTransactionSignerConfig}\n */\nexport type TransactionModifyingSignerConfig = BaseTransactionSignerConfig;\n\n/**\n * A signer interface that potentially modifies the provided {@link Transaction | Transactions}\n * before signing them.\n *\n * For instance, this enables wallets to inject additional instructions into the\n * transaction before signing them. For each transaction, instead of returning a {@link SignatureDirectory},\n * its {@link TransactionModifyingSigner#modifyAndSignTransactions | modifyAndSignTransactions}\n * function returns an updated {@link Transaction} with a potentially\n * modified set of instructions and signature dictionary.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * const signer: TransactionModifyingSigner<'1234..5678'> = {\n *     address: address('1234..5678'),\n *     modifyAndSignTransactions: async <T extends Transaction>(\n *         transactions: T[]\n *     ): Promise<T[]> => {\n *         // My custom signing logic.\n *     },\n * };\n * ```\n *\n * @remarks\n * Here are the main characteristics of this signer interface:\n *\n * - **Sequential**. Contrary to partial signers, these cannot be executed in\n *   parallel as each call can modify the provided transactions.\n * - **First signers**. For a given transaction, a modifying signer must always\n *   be used before a partial signer as the former will likely modify the\n *   transaction and thus impact the outcome of the latter.\n * - **Potential conflicts**. If more than one modifying signer is provided,\n *   the second signer may invalidate the signature of the first one. However,\n *   modifying signers may decide not to modify a transaction based on the\n *   existence of signatures for that transaction.\n *\n * @see {@link isTransactionModifyingSigner}\n * @see {@link assertIsTransactionModifyingSigner}\n */\nexport type TransactionModifyingSigner<TAddress extends string = string> = Readonly<{\n    address: Address<TAddress>;\n    modifyAndSignTransactions<T extends Transaction>(\n        transactions: readonly T[],\n        config?: TransactionModifyingSignerConfig,\n    ): Promise<readonly T[]>;\n}>;\n\n/**\n * Checks whether the provided value implements the {@link TransactionModifyingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isTransactionModifyingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isTransactionModifyingSigner({ address, modifyAndSignTransactions: async () => {} }); // true\n * isTransactionModifyingSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsTransactionModifyingSigner}\n */\nexport function isTransactionModifyingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is TransactionModifyingSigner<TAddress> {\n    return 'modifyAndSignTransactions' in value && typeof value.modifyAndSignTransactions === 'function';\n}\n\n/**\n * Asserts that the provided value implements the {@link TransactionModifyingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsTransactionModifyingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsTransactionModifyingSigner({ address, modifyAndSignTransactions: async () => {} }); // void\n * assertIsTransactionModifyingSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isTransactionModifyingSigner}\n */\nexport function assertIsTransactionModifyingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is TransactionModifyingSigner<TAddress> {\n    if (!isTransactionModifyingSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER, SolanaError } from '@solana/errors';\nimport { Transaction } from '@solana/transactions';\n\nimport { BaseTransactionSignerConfig, SignatureDictionary } from './types';\n\n/**\n * The configuration to optionally provide when calling the\n * {@link TransactionPartialSigner#signTransactions | signTransactions} method.\n *\n * @see {@link BaseTransactionSignerConfig}\n */\nexport type TransactionPartialSignerConfig = BaseTransactionSignerConfig;\n\n/**\n * A signer interface that signs an array of {@link Transaction | Transactions}\n *  without modifying their content. It defines a\n * {@link TransactionPartialSigner#signTransactions | signTransactions}\n * function that returns a {@link SignatureDictionary} for each provided transaction.\n *\n * Such signature dictionaries are expected to be merged with the existing ones if any.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * const signer: TransactionPartialSigner<'1234..5678'> = {\n *     address: address('1234..5678'),\n *     signTransactions: async (\n *         transactions: Transaction[]\n *     ): Promise<SignatureDictionary[]> => {\n *         // My custom signing logic.\n *     },\n * };\n * ```\n *\n * @remarks\n * Here are the main characteristics of this signer interface:\n *\n * - **Parallel**. It returns a signature directory for each provided\n *   transaction without modifying them, making it possible for multiple\n *   partial signers to sign the same transaction in parallel.\n * - **Flexible order**. The order in which we use these signers for\n *   a given transaction doesn’t matter.\n *\n * @see {@link isTransactionPartialSigner}\n * @see {@link assertIsTransactionPartialSigner}\n */\nexport type TransactionPartialSigner<TAddress extends string = string> = Readonly<{\n    address: Address<TAddress>;\n    signTransactions(\n        transactions: readonly Transaction[],\n        config?: TransactionPartialSignerConfig,\n    ): Promise<readonly SignatureDictionary[]>;\n}>;\n\n/**\n * Checks whether the provided value implements the {@link TransactionPartialSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isTransactionPartialSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isTransactionPartialSigner({ address, signTransactions: async () => {} }); // true\n * isTransactionPartialSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsTransactionPartialSigner}\n */\nexport function isTransactionPartialSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is TransactionPartialSigner<TAddress> {\n    return 'signTransactions' in value && typeof value.signTransactions === 'function';\n}\n\n/**\n * Asserts that the provided value implements the {@link TransactionPartialSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsTransactionPartialSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsTransactionPartialSigner({ address, signTransactions: async () => {} }); // void\n * assertIsTransactionPartialSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isTransactionPartialSigner}\n */\nexport function assertIsTransactionPartialSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is TransactionPartialSigner<TAddress> {\n    if (!isTransactionPartialSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER, SolanaError } from '@solana/errors';\nimport { SignatureBytes } from '@solana/keys';\nimport { Transaction } from '@solana/transactions';\n\nimport { BaseTransactionSignerConfig } from './types';\n\n/**\n * The configuration to optionally provide when calling the\n * {@link TransactionSendingSignerConfig#signAndSendTransactions | signAndSendTransactions} method.\n *\n * @see {@link BaseTransactionSignerConfig}\n */\nexport type TransactionSendingSignerConfig = BaseTransactionSignerConfig;\n\n/**\n * A signer interface that signs one or multiple transactions\n * before sending them immediately to the blockchain.\n *\n * It defines a {@link TransactionSendingSignerConfig#signAndSendTransactions | signAndSendTransactions}\n * function that returns the transaction signature (i.e. its identifier) for each provided\n * {@link CompilableTransaction}.\n *\n * This interface is required for PDA wallets and other types of wallets that don't provide an\n * interface for signing transactions without sending them.\n *\n * Note that it is also possible for such signers to modify the provided transactions\n * before signing and sending them. This enables use cases where the modified transactions\n * cannot be shared with the app and thus must be sent directly.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * const myTransactionSendingSigner: TransactionSendingSigner<'1234..5678'> = {\n *     address: address('1234..5678'),\n *     signAndSendTransactions: async (transactions: Transaction[]): Promise<SignatureBytes[]> => {\n *         // My custom signing logic.\n *     },\n * };\n * ```\n *\n * @remarks\n * Here are the main characteristics of this signer interface:\n *\n * - **Single signer**. Since this signer also sends the provided transactions,\n *   we can only use a single {@link TransactionSendingSigner} for a given set of transactions.\n * - **Last signer**. Trivially, that signer must also be the last one used.\n * - **Potential conflicts**. Since signers may decide to modify the given\n *   transactions before sending them, they may invalidate previous signatures.\n *   However, signers may decide not to modify a transaction based\n *   on the existence of signatures for that transaction.\n * - **Potential confirmation**. Whilst this is not required by this interface,\n *   it is also worth noting that most wallets will also wait for the transaction\n *   to be confirmed (typically with a `confirmed` commitment)\n *   before notifying the app that they are done.\n *\n * @see {@link isTransactionSendingSigner}\n * @see {@link assertIsTransactionSendingSigner}\n */\nexport type TransactionSendingSigner<TAddress extends string = string> = Readonly<{\n    address: Address<TAddress>;\n    signAndSendTransactions(\n        transactions: readonly Transaction[],\n        config?: TransactionSendingSignerConfig,\n    ): Promise<readonly SignatureBytes[]>;\n}>;\n\n/**\n * Checks whether the provided value implements the {@link TransactionSendingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isTransactionSendingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isTransactionSendingSigner({ address, signAndSendTransactions: async () => {} }); // true\n * isTransactionSendingSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsTransactionSendingSigner}\n */\nexport function isTransactionSendingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is TransactionSendingSigner<TAddress> {\n    return 'signAndSendTransactions' in value && typeof value.signAndSendTransactions === 'function';\n}\n\n/**\n * Asserts that the provided value implements the {@link TransactionSendingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsTransactionSendingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsTransactionSendingSigner({ address, signAndSendTransactions: async () => {} }); // void\n * assertIsTransactionSendingSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isTransactionSendingSigner}\n */\nexport function assertIsTransactionSendingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is TransactionSendingSigner<TAddress> {\n    if (!isTransactionSendingSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER, SolanaError } from '@solana/errors';\n\nimport { isTransactionModifyingSigner, TransactionModifyingSigner } from './transaction-modifying-signer';\nimport { isTransactionPartialSigner, TransactionPartialSigner } from './transaction-partial-signer';\nimport { isTransactionSendingSigner, TransactionSendingSigner } from './transaction-sending-signer';\n\n/**\n * Defines a signer capable of signing transactions.\n *\n * @see {@link TransactionModifyingSigner} For signers that can modify transactions before signing them.\n * @see {@link TransactionPartialSigner} For signers that can be used in parallel.\n * @see {@link TransactionSendingSigner} For signers that send transactions after signing them.\n * @see {@link isTransactionSigner}\n * @see {@link assertIsTransactionSigner}\n */\nexport type TransactionSigner<TAddress extends string = string> =\n    | TransactionModifyingSigner<TAddress>\n    | TransactionPartialSigner<TAddress>\n    | TransactionSendingSigner<TAddress>;\n\n/**\n * Checks whether the provided value implements the {@link TransactionSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isTransactionSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isTransactionSigner({ address, signTransactions: async () => {} }); // true\n * isTransactionSigner({ address, modifyAndSignTransactions: async () => {} }); // true\n * isTransactionSigner({ address, signAndSendTransactions: async () => {} }); // true\n * isTransactionSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsTransactionSigner}\n */\nexport function isTransactionSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is TransactionSigner<TAddress> {\n    return (\n        isTransactionPartialSigner(value) || isTransactionModifyingSigner(value) || isTransactionSendingSigner(value)\n    );\n}\n\n/**\n * Asserts that the provided value implements the {@link TransactionSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsTransactionSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsTransactionSigner({ address, signTransactions: async () => {} }); // void\n * assertIsTransactionSigner({ address, modifyAndSignTransactions: async () => {} }); // void\n * assertIsTransactionSigner({ address, signAndSendTransactions: async () => {} }); // void\n * assertIsTransactionSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isTransactionSigner}\n */\nexport function assertIsTransactionSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is TransactionSigner<TAddress> {\n    if (!isTransactionSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { AccountRole, IAccount<PERSON><PERSON>up<PERSON>eta, IAccountMeta, IInstruction } from '@solana/instructions';\nimport {\n    BaseTransactionMessage,\n    ITransactionMessageWithFeePayer,\n    TransactionVersion,\n} from '@solana/transaction-messages';\n\nimport { deduplicateSigners } from './deduplicate-signers';\nimport { ITransactionMessageWithFeePayerSigner } from './fee-payer-signer';\nimport { isTransactionSigner, TransactionSigner } from './transaction-signer';\n\n/**\n * An extension of the {@link IAccountMeta} type that allows us to store {@link TransactionSigner | TransactionSigners} inside it.\n *\n * Note that, because this type represents a signer, it must use one the following two roles:\n * - {@link AccountRole.READONLY_SIGNER}\n * - {@link AccountRole.WRITABLE_SIGNER}\n *\n * @typeParam TAddress - Supply a string literal to define an account having a particular address.\n * @typeParam TSigner - Optionally provide a narrower type for the {@link TransactionSigner} to use within the account meta.\n *\n * @interface\n *\n * @example\n * ```ts\n * import { AccountRole } from '@solana/instructions';\n * import { generateKeyPairSigner, IAccountSignerMeta } from '@solana/signers';\n *\n * const signer = await generateKeyPairSigner();\n * const account: IAccountSignerMeta = {\n *     address: signer.address,\n *     role: AccountRole.READONLY_SIGNER,\n *     signer,\n * };\n * ```\n */\nexport interface IAccountSignerMeta<\n    TAddress extends string = string,\n    TSigner extends TransactionSigner<TAddress> = TransactionSigner<TAddress>,\n> extends IAccountMeta<TAddress> {\n    readonly role: AccountRole.READONLY_SIGNER | AccountRole.WRITABLE_SIGNER;\n    readonly signer: TSigner;\n}\n\n/**\n * A union type that supports base account metas as well as {@link IAccountSignerMeta | signer account metas}.\n */\ntype IAccountMetaWithSigner<TSigner extends TransactionSigner = TransactionSigner> =\n    | IAccountLookupMeta\n    | IAccountMeta\n    | IAccountSignerMeta<string, TSigner>;\n\n/**\n * Composable type that allows {@link IAccountSignerMeta | IAccountSignerMetas} to be used inside the instruction's `accounts` array\n *\n * @typeParam TSigner - Optionally provide a narrower type for {@link TransactionSigner | TransactionSigners}.\n * @typeParam TAccounts - Optionally provide a narrower type for the account metas.\n *\n * @interface\n *\n * @example\n * ```ts\n * import { AccountRole, IInstruction } from '@solana/instructions';\n * import { generateKeyPairSigner, IInstructionWithSigners } from '@solana/signers';\n *\n * const [authority, buffer] = await Promise.all([\n *     generateKeyPairSigner(),\n *     generateKeyPairSigner(),\n * ]);\n * const instruction: IInstruction & IInstructionWithSigners = {\n *     programAddress: address('1234..5678'),\n *     accounts: [\n *         // The authority is a signer account.\n *         {\n *             address: authority.address,\n *             role: AccountRole.READONLY_SIGNER,\n *             signer: authority,\n *         },\n *         // The buffer is a writable account.\n *         { address: buffer.address, role: AccountRole.WRITABLE },\n *     ],\n * };\n * ```\n */\nexport type IInstructionWithSigners<\n    TSigner extends TransactionSigner = TransactionSigner,\n    TAccounts extends readonly IAccountMetaWithSigner<TSigner>[] = readonly IAccountMetaWithSigner<TSigner>[],\n> = Pick<IInstruction<string, TAccounts>, 'accounts'>;\n\n/**\n * A {@link BaseTransactionMessage} type extension that accept {@link TransactionSigner | TransactionSigners}.\n *\n * Namely, it allows:\n * - a {@link TransactionSigner} to be used as the fee payer and\n * - {@link IInstructionWithSigners} to be used in its instructions.\n *\n *\n * @typeParam TAddress - Supply a string literal to define an account having a particular address.\n * @typeParam TSigner - Optionally provide a narrower type for {@link TransactionSigner | TransactionSigners}.\n * @typeParam TAccounts - Optionally provide a narrower type for the account metas.\n *\n * @example\n * ```ts\n * import { IInstruction } from '@solana/instructions';\n * import { BaseTransactionMessage } from '@solana/transaction-messages';\n * import { generateKeyPairSigner, IInstructionWithSigners, ITransactionMessageWithSigners } from '@solana/signers';\n *\n * const signer = await generateKeyPairSigner();\n * const firstInstruction: IInstruction = { ... };\n * const secondInstruction: IInstructionWithSigners = { ... };\n * const transactionMessage: BaseTransactionMessage & ITransactionMessageWithSigners = {\n *     feePayer: signer,\n *     instructions: [firstInstruction, secondInstruction],\n * }\n * ```\n */\nexport type ITransactionMessageWithSigners<\n    TAddress extends string = string,\n    TSigner extends TransactionSigner<TAddress> = TransactionSigner<TAddress>,\n    TAccounts extends readonly IAccountMetaWithSigner<TSigner>[] = readonly IAccountMetaWithSigner<TSigner>[],\n> = Partial<ITransactionMessageWithFeePayer<TAddress> | ITransactionMessageWithFeePayerSigner<TAddress, TSigner>> &\n    Pick<\n        BaseTransactionMessage<TransactionVersion, IInstruction & IInstructionWithSigners<TSigner, TAccounts>>,\n        'instructions'\n    >;\n\n/**\n * Extracts and deduplicates all {@link TransactionSigner | TransactionSigners} stored\n * inside the account metas of an {@link IInstructionWithSigners | instruction}.\n *\n * Any extracted signers that share the same {@link Address} will be de-duplicated.\n *\n * @typeParam TSigner - Optionally provide a narrower type for {@link TransactionSigner | TransactionSigners}.\n *\n * @example\n * ```ts\n * import { IInstructionWithSigners, getSignersFromInstruction } from '@solana/signers';\n *\n * const signerA = { address: address('1111..1111'), signTransactions: async () => {} };\n * const signerB = { address: address('2222..2222'), signTransactions: async () => {} };\n * const instructionWithSigners: IInstructionWithSigners = {\n *     accounts: [\n *         { address: signerA.address, signer: signerA, ... },\n *         { address: signerB.address, signer: signerB, ... },\n *         { address: signerA.address, signer: signerA, ... },\n *     ],\n * };\n *\n * const instructionSigners = getSignersFromInstruction(instructionWithSigners);\n * // ^ [signerA, signerB]\n * ```\n */\nexport function getSignersFromInstruction<TSigner extends TransactionSigner = TransactionSigner>(\n    instruction: IInstructionWithSigners<TSigner>,\n): readonly TSigner[] {\n    return deduplicateSigners(\n        (instruction.accounts ?? []).flatMap(account => ('signer' in account ? account.signer : [])),\n    );\n}\n\n/**\n * Extracts and deduplicates all {@link TransactionSigner | TransactionSigners} stored\n * inside a given {@link ITransactionMessageWithSigners | transaction message}.\n *\n * This includes any {@link TransactionSigner | TransactionSigners} stored\n * as the fee payer or in the instructions of the transaction message.\n *\n * Any extracted signers that share the same {@link Address} will be de-duplicated.\n *\n * @typeParam TAddress - Supply a string literal to define an account having a particular address.\n * @typeParam TSigner - Optionally provide a narrower type for {@link TransactionSigner | TransactionSigners}.\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import { IInstruction } from '@solana/instructions';\n * import { IInstructionWithSigners, ITransactionMessageWithSigners, getSignersFromTransactionMessage } from '@solana/signers';\n *\n * const signerA = { address: address('1111..1111'), signTransactions: async () => {} };\n * const signerB = { address: address('2222..2222'), signTransactions: async () => {} };\n * const firstInstruction: IInstruction & IInstructionWithSigners = {\n *     programAddress: address('1234..5678'),\n *     accounts: [{ address: signerA.address, signer: signerA, ... }],\n * };\n * const secondInstruction: IInstruction & IInstructionWithSigners = {\n *     programAddress: address('1234..5678'),\n *     accounts: [{ address: signerB.address, signer: signerB, ... }],\n * };\n * const transactionMessage: ITransactionMessageWithSigners = {\n *     feePayer: signerA,\n *     instructions: [firstInstruction, secondInstruction],\n * }\n *\n * const transactionSigners = getSignersFromTransactionMessage(transactionMessage);\n * // ^ [signerA, signerB]\n * ```\n */\nexport function getSignersFromTransactionMessage<\n    TAddress extends string = string,\n    TSigner extends TransactionSigner<TAddress> = TransactionSigner<TAddress>,\n    TTransactionMessage extends ITransactionMessageWithSigners<TAddress, TSigner> = ITransactionMessageWithSigners<\n        TAddress,\n        TSigner\n    >,\n>(transaction: TTransactionMessage): readonly TSigner[] {\n    return deduplicateSigners([\n        ...(transaction.feePayer && isTransactionSigner(transaction.feePayer) ? [transaction.feePayer as TSigner] : []),\n        ...transaction.instructions.flatMap(getSignersFromInstruction),\n    ]);\n}\n", "import { Address } from '@solana/addresses';\nimport { IInstruction, isSignerRole } from '@solana/instructions';\nimport { BaseTransactionMessage, ITransactionMessageWithFeePayer } from '@solana/transaction-messages';\n\nimport { IAccountSignerMeta, IInstructionWithSigners, ITransactionMessageWithSigners } from './account-signer-meta';\nimport { deduplicateSigners } from './deduplicate-signers';\nimport { isTransactionSigner, TransactionSigner } from './transaction-signer';\n\n/**\n * Attaches the provided {@link TransactionSigner | TransactionSigners} to the\n * account metas of an instruction when applicable.\n *\n * For an account meta to match a provided signer it:\n * - Must have a signer role ({@link AccountRole.READONLY_SIGNER} or {@link AccountRole.WRITABLE_SIGNER}).\n * - Must have the same address as the provided signer.\n * - Must not have an attached signer already.\n *\n * @typeParam TInstruction - The inferred type of the instruction provided.\n *\n * @example\n * ```ts\n * import { AccountRole, IInstruction } from '@solana/instructions';\n * import { addSignersToInstruction, TransactionSigner } from '@solana/signers';\n *\n * const instruction: IInstruction = {\n *     accounts: [\n *         { address: '1111' as Address, role: AccountRole.READONLY_SIGNER },\n *         { address: '2222' as Address, role: AccountRole.WRITABLE_SIGNER },\n *     ],\n *     // ...\n * };\n *\n * const signerA: TransactionSigner<'1111'>;\n * const signerB: TransactionSigner<'2222'>;\n * const instructionWithSigners = addSignersToInstruction(\n *     [signerA, signerB],\n *     instruction\n * );\n *\n * // instructionWithSigners.accounts[0].signer === signerA\n * // instructionWithSigners.accounts[1].signer === signerB\n * ```\n */\nexport function addSignersToInstruction<TInstruction extends IInstruction>(\n    signers: TransactionSigner[],\n    instruction: TInstruction | (IInstructionWithSigners & TInstruction),\n): IInstructionWithSigners & TInstruction {\n    if (!instruction.accounts || instruction.accounts.length === 0) {\n        return instruction as IInstructionWithSigners & TInstruction;\n    }\n\n    const signerByAddress = new Map(deduplicateSigners(signers).map(signer => [signer.address, signer]));\n    return Object.freeze({\n        ...instruction,\n        accounts: instruction.accounts.map(account => {\n            const signer = signerByAddress.get(account.address);\n            if (!isSignerRole(account.role) || 'signer' in account || !signer) {\n                return account;\n            }\n            return Object.freeze({ ...account, signer } as IAccountSignerMeta);\n        }),\n    });\n}\n\n/**\n * Attaches the provided {@link TransactionSigner | TransactionSigners} to the\n * account metas of all instructions inside a transaction message and/or\n * the transaction message fee payer, when applicable.\n *\n * For an account meta to match a provided signer it:\n * - Must have a signer role ({@link AccountRole.READONLY_SIGNER} or {@link AccountRole.WRITABLE_SIGNER}).\n * - Must have the same address as the provided signer.\n * - Must not have an attached signer already.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import { AccountRole, IInstruction } from '@solana/instructions';\n * import { BaseTransactionMessage } from '@solana/transaction-messages';\n * import { addSignersToTransactionMessage, TransactionSigner } from '@solana/signers';\n *\n * const instructionA: IInstruction = {\n *     accounts: [{ address: '1111' as Address, role: AccountRole.READONLY_SIGNER }],\n *     // ...\n * };\n * const instructionB: IInstruction = {\n *     accounts: [{ address: '2222' as Address, role: AccountRole.WRITABLE_SIGNER }],\n *     // ...\n * };\n * const transactionMessage: BaseTransactionMessage = {\n *     instructions: [instructionA, instructionB],\n *     // ...\n * }\n *\n * const signerA: TransactionSigner<'1111'>;\n * const signerB: TransactionSigner<'2222'>;\n * const transactionMessageWithSigners = addSignersToTransactionMessage(\n *     [signerA, signerB],\n *     transactionMessage\n * );\n *\n * // transactionMessageWithSigners.instructions[0].accounts[0].signer === signerA\n * // transactionMessageWithSigners.instructions[1].accounts[0].signer === signerB\n * ```\n */\nexport function addSignersToTransactionMessage<TTransactionMessage extends BaseTransactionMessage>(\n    signers: TransactionSigner[],\n    transactionMessage: TTransactionMessage | (ITransactionMessageWithSigners & TTransactionMessage),\n): ITransactionMessageWithSigners & TTransactionMessage {\n    const feePayerSigner = hasAddressOnlyFeePayer(transactionMessage)\n        ? signers.find(signer => signer.address === transactionMessage.feePayer.address)\n        : undefined;\n\n    if (!feePayerSigner && transactionMessage.instructions.length === 0) {\n        return transactionMessage as ITransactionMessageWithSigners & TTransactionMessage;\n    }\n\n    return Object.freeze({\n        ...transactionMessage,\n        ...(feePayerSigner ? { feePayer: feePayerSigner } : null),\n        instructions: transactionMessage.instructions.map(instruction => addSignersToInstruction(signers, instruction)),\n    });\n}\n\nfunction hasAddressOnlyFeePayer(\n    message: BaseTransactionMessage & Partial<ITransactionMessageWithFeePayer>,\n): message is BaseTransactionMessage & { feePayer: { address: Address } } {\n    return (\n        !!message &&\n        'feePayer' in message &&\n        !!message.feePayer &&\n        typeof message.feePayer.address === 'string' &&\n        !isTransactionSigner(message.feePayer)\n    );\n}\n", "import { BaseTransactionMessage, ITransactionMessageWithFeePayer } from '@solana/transaction-messages';\n\nimport { TransactionSigner } from './transaction-signer';\n\n/**\n * Alternative to {@link ITransactionMessageWithFeePayer} that uses a {@link TransactionSigner} for the fee payer.\n *\n * @typeParam TAddress - Supply a string literal to define a fee payer having a particular address.\n * @typeParam TSigner - Optionally provide a narrower type for the {@link TransactionSigner}.\n *\n * @example\n * ```ts\n * import { BaseTransactionMessage } from '@solana/transaction-messages';\n * import { generateKeyPairSigner, ITransactionMessageWithFeePayerSigner } from '@solana/signers';\n *\n * const transactionMessage: BaseTransactionMessage & ITransactionMessageWithFeePayerSigner = {\n *     feePayer: await generateKeyPairSigner(),\n *     instructions: [],\n *     version: 0,\n * };\n * ```\n */\nexport interface ITransactionMessageWithFeePayerSigner<\n    TAddress extends string = string,\n    T<PERSON><PERSON><PERSON> extends TransactionSigner<TAddress> = TransactionSigner<TAddress>,\n> {\n    readonly feePayer: TSigner;\n}\n\n/**\n * Sets the fee payer of a {@link BaseTransactionMessage | transaction message}\n * using a {@link TransactionSigner}.\n *\n * @typeParam TFeePayerAddress - Supply a string literal to define a fee payer having a particular address.\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import { pipe } from '@solana/functional';\n * import { generateKeyPairSigner, setTransactionMessageFeePayerSigner } from '@solana/signers';\n * import { createTransactionMessage } from '@solana/transaction-messages';\n *\n * const feePayer = await generateKeyPairSigner();\n * const transactionMessage = pipe(\n *     createTransactionMessage({ version: 0 }),\n *     message => setTransactionMessageFeePayerSigner(signer, message),\n * );\n * ```\n */\nexport function setTransactionMessageFeePayerSigner<\n    TFeePayerAddress extends string,\n    TTransactionMessage extends BaseTransactionMessage &\n        Partial<ITransactionMessageWithFeePayer | ITransactionMessageWithFeePayerSigner>,\n>(\n    feePayer: TransactionSigner<TFeePayerAddress>,\n    transactionMessage: TTransactionMessage,\n): ITransactionMessageWithFeePayerSigner<TFeePayerAddress> & Omit<TTransactionMessage, 'feePayer'> {\n    Object.freeze(feePayer);\n    const out = { ...transactionMessage, feePayer };\n    Object.freeze(out);\n    return out;\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER, SolanaError } from '@solana/errors';\n\nimport { SignableMessage } from './signable-message';\nimport { BaseSignerConfig, SignatureDictionary } from './types';\n\n/**\n * The configuration to optionally provide when calling the\n * {@link MessagePartialSigner#signMessages | signMessages} method.\n *\n * @see {@link BaseSignerConfig}\n */\nexport type MessagePartialSignerConfig = BaseSignerConfig;\n\n/**\n * A signer interface that signs an array of {@link SignableMessage | SignableMessages}\n * without modifying their content.\n *\n * It defines a {@link MessagePartialSigner#signMessages | signMessages} function\n * that returns a {@link SignatureDictionary} for each provided message.\n * Such signature dictionaries are expected to be merged with the existing ones if any.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * const signer: MessagePartialSigner<'1234..5678'> = {\n *     address: address('1234..5678'),\n *     signMessages: async (\n *         messages: SignableMessage[]\n *     ): Promise<SignatureDictionary[]> => {\n *         // My custom signing logic.\n *     },\n * };\n * ```\n *\n * @remarks\n * Here are the main characteristics of this signer interface:\n *\n * - **Parallel**. When multiple signers sign the same message, we can\n *   perform this operation in parallel to obtain all their signatures.\n * - **Flexible order**. The order in which we use these signers\n *   for a given message doesn’t matter.\n *\n * @see {@link SignableMessage}\n * @see {@link createSignableMessage}\n * @see {@link isMessagePartialSigner}\n * @see {@link assertIsMessagePartialSigner}\n */\nexport type MessagePartialSigner<TAddress extends string = string> = Readonly<{\n    address: Address<TAddress>;\n    signMessages(\n        messages: readonly SignableMessage[],\n        config?: MessagePartialSignerConfig,\n    ): Promise<readonly SignatureDictionary[]>;\n}>;\n\n/**\n * Checks whether the provided value implements the {@link MessagePartialSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isMessagePartialSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isMessagePartialSigner({ address, signMessages: async () => {} }); // true\n * isMessagePartialSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsMessagePartialSigner}\n */\nexport function isMessagePartialSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is MessagePartialSigner<TAddress> {\n    return 'signMessages' in value && typeof value.signMessages === 'function';\n}\n\n/**\n * Asserts that the provided value implements the {@link MessagePartialSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsMessagePartialSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsMessagePartialSigner({ address, signMessages: async () => {} }); // void\n * assertIsMessagePartialSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isMessagePartialSigner}\n */\nexport function assertIsMessagePartialSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is MessagePartialSigner<TAddress> {\n    if (!isMessagePartialSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address, getAddress<PERSON>rom<PERSON>ub<PERSON><PERSON><PERSON> } from '@solana/addresses';\nimport { ReadonlyUint8Array } from '@solana/codecs-core';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER, SolanaError } from '@solana/errors';\nimport { createKeyPairFromBytes, createKeyPairFromPrivateKeyBytes, generateKeyPair, signBytes } from '@solana/keys';\nimport { partiallySignTransaction } from '@solana/transactions';\n\nimport { isMessagePartialSigner, MessagePartialSigner } from './message-partial-signer';\nimport { isTransactionPartialSigner, TransactionPartialSigner } from './transaction-partial-signer';\n\n/**\n * Defines a signer that uses a {@link CryptoKeyPair} to sign messages and transactions.\n *\n * It implements both the {@link MessagePartialSigner} and {@link TransactionPartialSigner}\n * interfaces and keeps track of the {@link CryptoKeyPair} instance used\n * to sign messages and transactions.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * import { generateKeyPairSigner } from '@solana/signers';\n *\n * const signer = generateKeyPairSigner();\n * signer.address; // Address;\n * signer.keyPair; // CryptoKeyPair;\n * const [messageSignatures] = await signer.signMessages([message]);\n * const [transactionSignatures] = await signer.signTransactions([transaction]);\n * ```\n *\n * @see {@link generateKeyPairSigner}\n * @see {@link createSignerFromKeyPair}\n * @see {@link createKeyPairSignerFromBytes}\n * @see {@link createKeyPairSignerFromPrivateKeyBytes}\n * @see {@link isKeyPairSigner}\n * @see {@link assertIsKeyPairSigner}\n */\nexport type KeyPairSigner<TAddress extends string = string> = MessagePartialSigner<TAddress> &\n    TransactionPartialSigner<TAddress> & { keyPair: CryptoKeyPair };\n\n/**\n * Checks whether the provided value implements the {@link KeyPairSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { generateKeyPairSigner, isKeyPairSigner } from '@solana/signers';\n *\n * const signer = await generateKeyPairSigner();\n * isKeyPairSigner(signer); // true\n * isKeyPairSigner({ address: address('1234..5678') }); // false\n * ```\n */\nexport function isKeyPairSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is KeyPairSigner<TAddress> {\n    return (\n        'keyPair' in value &&\n        typeof value.keyPair === 'object' &&\n        isMessagePartialSigner(value) &&\n        isTransactionPartialSigner(value)\n    );\n}\n\n/**\n * Asserts that the provided value implements the {@link KeyPairSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { generateKeyPairSigner, assertIsKeyPairSigner } from '@solana/signers';\n *\n * const signer = await generateKeyPairSigner();\n * assertIsKeyPairSigner(signer); // void\n * assertIsKeyPairSigner({ address: address('1234..5678') }); // Throws an error.\n * ```\n */\nexport function assertIsKeyPairSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is KeyPairSigner<TAddress> {\n    if (!isKeyPairSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n\n/**\n * Creates a {@link KeyPairSigner} from a provided {@link CryptoKeyPair}.\n *\n * The {@link MessagePartialSigner#signMessages | signMessages} and\n * {@link TransactionPartialSigner#signTransactions | signTransactions}\n * functions of the returned signer will use the private key of the provided\n * key pair to sign messages and transactions.\n *\n * Note that both the {@link MessagePartialSigner#signMessages | signMessages} and\n * {@link TransactionPartialSigner#signTransactions | signTransactions} implementations\n * are parallelized, meaning that they will sign all provided messages and transactions in parallel.\n *\n * @example\n * ```ts\n * import { generateKeyPair } from '@solana/keys';\n * import { createSignerFromKeyPair, KeyPairSigner } from '@solana/signers';\n *\n * const keyPair: CryptoKeyPair = await generateKeyPair();\n * const signer: KeyPairSigner = await createSignerFromKeyPair(keyPair);\n * ```\n */\nexport async function createSignerFromKeyPair(keyPair: CryptoKeyPair): Promise<KeyPairSigner> {\n    const address = await getAddressFromPublicKey(keyPair.publicKey);\n    const out: KeyPairSigner = {\n        address,\n        keyPair,\n        signMessages: messages =>\n            Promise.all(\n                messages.map(async message =>\n                    Object.freeze({ [address]: await signBytes(keyPair.privateKey, message.content) }),\n                ),\n            ),\n        signTransactions: transactions =>\n            Promise.all(\n                transactions.map(async transaction => {\n                    const signedTransaction = await partiallySignTransaction([keyPair], transaction);\n                    // we know that the address has signed `signedTransaction` because it comes from the keypair\n                    return Object.freeze({ [address]: signedTransaction.signatures[address]! });\n                }),\n            ),\n    };\n\n    return Object.freeze(out);\n}\n\n/**\n * Generates a signer capable of signing messages and transactions by generating\n * a {@link CryptoKeyPair} and creating a {@link KeyPairSigner} from it.\n *\n * @example\n * ```ts\n * import { generateKeyPairSigner } from '@solana/signers';\n *\n * const signer = await generateKeyPairSigner();\n * ```\n *\n * @see {@link createSignerFromKeyPair}\n */\nexport async function generateKeyPairSigner(): Promise<KeyPairSigner> {\n    return await createSignerFromKeyPair(await generateKeyPair());\n}\n\n/**\n * Creates a new {@link KeyPairSigner} from a 64-bytes `Uint8Array` secret key (private key and public key).\n *\n * @example\n * ```ts\n * import fs from 'fs';\n * import { createKeyPairSignerFromBytes } from '@solana/signers';\n *\n * // Get bytes from local keypair file.\n * const keypairFile = fs.readFileSync('~/.config/solana/id.json');\n * const keypairBytes = new Uint8Array(JSON.parse(keypairFile.toString()));\n *\n * // Create a KeyPairSigner from the bytes.\n * const signer = await createKeyPairSignerFromBytes(keypairBytes);\n * ```\n *\n * @see {@link createKeyPairSignerFromPrivateKeyBytes} if you only have the 32-bytes private key instead.\n */\nexport async function createKeyPairSignerFromBytes(\n    bytes: ReadonlyUint8Array,\n    extractable?: boolean,\n): Promise<KeyPairSigner> {\n    return await createSignerFromKeyPair(await createKeyPairFromBytes(bytes, extractable));\n}\n\n/**\n * Creates a new {@link KeyPairSigner} from a 32-bytes `Uint8Array` private key.\n *\n * @example\n * ```ts\n * import { getUtf8Encoder } from '@solana/codecs-strings';\n * import { createKeyPairSignerFromPrivateKeyBytes } from '@solana/signers';\n *\n * const message = getUtf8Encoder().encode('Hello, World!');\n * const seed = new Uint8Array(await crypto.subtle.digest('SHA-256', message));\n *\n * const derivedSigner = await createKeyPairSignerFromPrivateKeyBytes(seed);\n * ```\n *\n * @see {@link createKeyPairSignerFromBytes} if you have the 64-bytes secret key instead (private key and public key).\n */\nexport async function createKeyPairSignerFromPrivateKeyBytes(\n    bytes: ReadonlyUint8Array,\n    extractable?: boolean,\n): Promise<KeyPairSigner> {\n    return await createSignerFromKeyPair(await createKeyPairFromPrivateKeyBytes(bytes, extractable));\n}\n", "import { Address, isAddress } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER, SolanaError } from '@solana/errors';\n\nimport { SignableMessage } from './signable-message';\nimport { BaseSignerConfig } from './types';\n\n/**\n * The configuration to optionally provide when calling the\n * {@link MessageModifyingSigner#modifyAndSignMessages | modifyAndSignMessages} method.\n *\n * @see {@link BaseSignerConfig}\n */\nexport type MessageModifyingSignerConfig = BaseSignerConfig;\n\n/**\n * A signer interface that _potentially_ modifies the content\n * of the provided {@link SignableMessage | SignableMessages} before signing them.\n *\n * For instance, this enables wallets to prefix or suffix nonces to the messages they sign.\n * For each message, instead of returning a {@link SignatureDirectory}, the\n * {@link MessageModifyingSigner#modifyAndSignMessages | modifyAndSignMessages} function\n * returns an updated {@link SignableMessage} with a potentially modified content and signature dictionary.\n *\n * @typeParam TAddress - Supply a string literal to define a signer having a particular address.\n *\n * @example\n * ```ts\n * const signer: MessageModifyingSigner<'1234..5678'> = {\n *     address: address('1234..5678'),\n *     modifyAndSignMessages: async (\n *         messages: SignableMessage[]\n *     ): Promise<SignableMessage[]> => {\n *         // My custom signing logic.\n *     },\n * };\n * ```\n *\n * @remarks\n * Here are the main characteristics of this signer interface:\n *\n * - **Sequential**. Contrary to partial signers, these cannot be executed in\n *   parallel as each call can modify the content of the message.\n * - **First signers**. For a given message, a modifying signer must always be used\n *   before a partial signer as the former will likely modify the message and\n *   thus impact the outcome of the latter.\n * - **Potential conflicts**. If more than one modifying signer is provided, the second\n *   signer may invalidate the signature of the first one. However, modifying signers\n *   may decide not to modify a message based on the existence of signatures for that message.\n *\n * @see {@link SignableMessage}\n * @see {@link createSignableMessage}\n * @see {@link isMessageModifyingSigner}\n * @see {@link assertIsMessageModifyingSigner}\n */\nexport type MessageModifyingSigner<TAddress extends string = string> = Readonly<{\n    address: Address<TAddress>;\n    modifyAndSignMessages(\n        messages: readonly SignableMessage[],\n        config?: MessageModifyingSignerConfig,\n    ): Promise<readonly SignableMessage[]>;\n}>;\n\n/**\n * Checks whether the provided value implements the {@link MessageModifyingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isMessageModifyingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isMessageModifyingSigner({ address, modifyAndSignMessages: async () => {} }); // true\n * isMessageModifyingSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsMessageModifyingSigner}\n */\nexport function isMessageModifyingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is MessageModifyingSigner<TAddress> {\n    return (\n        isAddress(value.address) &&\n        'modifyAndSignMessages' in value &&\n        typeof value.modifyAndSignMessages === 'function'\n    );\n}\n\n/**\n * Asserts that the provided value implements the {@link MessageModifyingSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsMessageModifyingSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsMessageModifyingSigner({ address, modifyAndSignMessages: async () => {} }); // void\n * assertIsMessageModifyingSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isMessageModifyingSigner}\n */\nexport function assertIsMessageModifyingSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is MessageModifyingSigner<TAddress> {\n    if (!isMessageModifyingSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address } from '@solana/addresses';\nimport { SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER, SolanaError } from '@solana/errors';\n\nimport { isMessageModifyingSigner, MessageModifyingSigner } from './message-modifying-signer';\nimport { isMessagePartialSigner, MessagePartialSigner } from './message-partial-signer';\n\n/**\n * Defines a signer capable of signing messages.\n *\n * @see {@link MessageModifyingSigner} For signers that can modify messages before signing them.\n * @see {@link MessagePartialSigner} For signers that can be used in parallel.\n * @see {@link isMessageSigner}\n * @see {@link assertIsMessageSigner}\n */\nexport type MessageSigner<TAddress extends string = string> =\n    | MessageModifyingSigner<TAddress>\n    | MessagePartialSigner<TAddress>;\n\n/**\n * Checks whether the provided value implements the {@link MessageSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { isMessageSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * isMessageSigner({ address, signMessages: async () => {} }); // true\n * isMessageSigner({ address, modifyAndSignMessages: async () => {} }); // true\n * isMessageSigner({ address }); // false\n * ```\n *\n * @see {@link assertIsMessageSigner}\n */\nexport function isMessageSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): value is MessageSigner<TAddress> {\n    return isMessagePartialSigner(value) || isMessageModifyingSigner(value);\n}\n\n/**\n * Asserts that the provided value implements the {@link MessageSigner} interface.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { Address } from '@solana/addresses';\n * import { assertIsMessageSigner } from '@solana/signers';\n *\n * const address = '1234..5678' as Address<'1234..5678'>;\n * assertIsMessageSigner({ address, signMessages: async () => {} }); // void\n * assertIsMessageSigner({ address, modifyAndSignMessages: async () => {} }); // void\n * assertIsMessageSigner({ address }); // Throws an error.\n * ```\n *\n * @see {@link isMessageSigner}\n */\nexport function assertIsMessageSigner<TAddress extends string>(value: {\n    [key: string]: unknown;\n    address: Address<TAddress>;\n}): asserts value is MessageSigner<TAddress> {\n    if (!isMessageSigner(value)) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER, {\n            address: value.address,\n        });\n    }\n}\n", "import { Address } from '@solana/addresses';\n\nimport { MessagePartialSigner } from './message-partial-signer';\nimport { TransactionPartialSigner } from './transaction-partial-signer';\n\n/**\n * Defines a Noop (No-Operation) signer that pretends to partially sign messages and transactions.\n *\n * For a given {@link Address}, a Noop Signer can be created to offer an implementation of both\n * the {@link MessagePartialSigner} and {@link TransactionPartialSigner} interfaces such that\n * they do not sign anything. Namely, signing a transaction or a message with a `NoopSigner`\n * will return an empty `SignatureDictionary`.\n *\n * @typeParam TAddress - Supply a string literal to define a Noop signer having a particular address.\n *\n * @example\n * ```ts\n * import { address } from '@solana/addresses';\n * import { createNoopSigner } from '@solana/signers';\n *\n * const signer = createNoopSigner(address('1234..5678'));\n * const [messageSignatures] = await signer.signMessages([message]);\n * const [transactionSignatures] = await signer.signTransactions([transaction]);\n * // ^ Both messageSignatures and transactionSignatures are empty.\n * ```\n *\n * @remarks\n * This signer may be useful:\n *\n * - For testing purposes.\n * - For indicating that a given account is a signer and taking the responsibility to provide\n *   the signature for that account ourselves. For instance, if we need to send the transaction\n *   to a server that will sign it and send it for us.\n *\n * @see {@link createNoopSigner}\n */\nexport type NoopSigner<TAddress extends string = string> = MessagePartialSigner<TAddress> &\n    TransactionPartialSigner<TAddress>;\n\n/**\n * Creates a {@link NoopSigner} from the provided {@link Address}.\n *\n * @typeParam TAddress - The inferred type of the address provided.\n *\n * @example\n * ```ts\n * import { address } from '@solana/addresses';\n * import { createNoopSigner } from '@solana/signers';\n *\n * const signer = createNoopSigner(address('1234..5678'));\n * ```\n */\nexport function createNoopSigner<TAddress extends string = string>(address: Address<TAddress>): NoopSigner<TAddress> {\n    const out: NoopSigner<TAddress> = {\n        address,\n        signMessages: messages => Promise.resolve(messages.map(() => Object.freeze({}))),\n        signTransactions: transactions => Promise.resolve(transactions.map(() => Object.freeze({}))),\n    };\n\n    return Object.freeze(out);\n}\n", "import {\n    SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS,\n    SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING,\n    SolanaError,\n} from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\nimport { CompilableTransactionMessage } from '@solana/transaction-messages';\n\nimport { getSignersFromTransactionMessage, ITransactionMessageWithSigners } from './account-signer-meta';\nimport { isTransactionModifyingSigner } from './transaction-modifying-signer';\nimport { isTransactionPartialSigner } from './transaction-partial-signer';\nimport { isTransactionSendingSigner } from './transaction-sending-signer';\n\n/**\n * Defines a transaction message with exactly one {@link TransactionSendingSigner}.\n *\n * This type is used to narrow the type of transaction messages that have been\n * checked to have exactly one sending signer.\n *\n * @example\n * ```ts\n * import { assertIsTransactionMessageWithSingleSendingSigner } from '@solana/signers';\n *\n * assertIsTransactionMessageWithSingleSendingSigner(transactionMessage);\n * transactionMessage satisfies ITransactionMessageWithSingleSendingSigner;\n * ```\n *\n * @see {@link isTransactionMessageWithSingleSendingSigner}\n * @see {@link assertIsTransactionMessageWithSingleSendingSigner}\n */\nexport type ITransactionMessageWithSingleSendingSigner = Brand<\n    ITransactionMessageWithSigners,\n    'TransactionMessageWithSingleSendingSigner'\n>;\n\n/**\n * Checks whether the provided transaction has exactly one {@link TransactionSendingSigner}.\n *\n * This can be useful when using {@link signAndSendTransactionMessageWithSigners} to provide\n * a fallback strategy in case the transaction message cannot be send using this function.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import {\n *     isTransactionMessageWithSingleSendingSigner,\n *     signAndSendTransactionMessageWithSigners,\n *     signTransactionMessageWithSigners,\n * } from '@solana/signers';\n * import { getBase64EncodedWireTransaction } from '@solana/transactions';\n *\n * let transactionSignature: SignatureBytes;\n * if (isTransactionMessageWithSingleSendingSigner(transactionMessage)) {\n *     transactionSignature = await signAndSendTransactionMessageWithSigners(transactionMessage);\n * } else {\n *     const signedTransaction = await signTransactionMessageWithSigners(transactionMessage);\n *     const encodedTransaction = getBase64EncodedWireTransaction(signedTransaction);\n *     transactionSignature = await rpc.sendTransaction(encodedTransaction).send();\n * }\n * ```\n *\n * @see {@link signAndSendTransactionMessageWithSigners}\n * @see {@link assertIsTransactionMessageWithSingleSendingSigner}\n */\nexport function isTransactionMessageWithSingleSendingSigner<TTransactionMessage extends CompilableTransactionMessage>(\n    transaction: TTransactionMessage,\n): transaction is ITransactionMessageWithSingleSendingSigner & TTransactionMessage {\n    try {\n        assertIsTransactionMessageWithSingleSendingSigner(transaction);\n        return true;\n    } catch {\n        return false;\n    }\n}\n\n/**\n * Asserts that the provided transaction message has exactly one {@link TransactionSendingSigner}.\n *\n * This can be useful when using the {@link signAndSendTransactionMessageWithSigners} function\n * to ensure it will be able to select the correct signer to send the transaction.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import {\n *     assertIsTransactionMessageWithSingleSendingSigner,\n *     signAndSendTransactionMessageWithSigners\n * } from '@solana/signers';\n *\n * assertIsTransactionMessageWithSingleSendingSigner(transactionMessage);\n * const transactionSignature = await signAndSendTransactionMessageWithSigners(transactionMessage);\n * ```\n *\n * @see {@link signAndSendTransactionMessageWithSigners}\n * @see {@link isTransactionMessageWithSingleSendingSigner}\n */\nexport function assertIsTransactionMessageWithSingleSendingSigner<\n    TTransactionMessage extends CompilableTransactionMessage,\n>(\n    transaction: TTransactionMessage,\n): asserts transaction is ITransactionMessageWithSingleSendingSigner & TTransactionMessage {\n    const signers = getSignersFromTransactionMessage(transaction);\n    const sendingSigners = signers.filter(isTransactionSendingSigner);\n\n    if (sendingSigners.length === 0) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING);\n    }\n\n    // When identifying if there are multiple sending signers, we only need to check for\n    // sending signers that do not implement other transaction signer interfaces as\n    // they will be used as these other signer interfaces in case of a conflict.\n    const sendingOnlySigners = sendingSigners.filter(\n        signer => !isTransactionPartialSigner(signer) && !isTransactionModifyingSigner(signer),\n    );\n\n    if (sendingOnlySigners.length > 1) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS);\n    }\n}\n", "import { SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING, SolanaError } from '@solana/errors';\nimport { SignatureBytes } from '@solana/keys';\nimport {\n    CompilableTransactionMessage,\n    TransactionMessageWithBlockhashLifetime,\n    TransactionMessageWithDurableNonceLifetime,\n} from '@solana/transaction-messages';\nimport {\n    assertTransactionIsFullySigned,\n    compileTransaction,\n    FullySignedTransaction,\n    Transaction,\n    TransactionWithBlockhashLifetime,\n    TransactionWithDurableNonceLifetime,\n    TransactionWithLifetime,\n} from '@solana/transactions';\n\nimport { getSignersFromTransactionMessage, ITransactionMessageWithSigners } from './account-signer-meta';\nimport { deduplicateSigners } from './deduplicate-signers';\nimport {\n    isTransactionModifyingSigner,\n    TransactionModifyingSigner,\n    TransactionModifyingSignerConfig,\n} from './transaction-modifying-signer';\nimport {\n    isTransactionPartialSigner,\n    TransactionPartialSigner,\n    TransactionPartialSignerConfig,\n} from './transaction-partial-signer';\nimport {\n    isTransactionSendingSigner,\n    TransactionSendingSigner,\n    TransactionSendingSignerConfig,\n} from './transaction-sending-signer';\nimport { isTransactionSigner, TransactionSigner } from './transaction-signer';\nimport { assertIsTransactionMessageWithSingleSendingSigner } from './transaction-with-single-sending-signer';\n\ntype CompilableTransactionMessageWithSigners = CompilableTransactionMessage & ITransactionMessageWithSigners;\n\n/**\n * Extracts all {@link TransactionSigner | TransactionSigners} inside the provided\n * transaction message and uses them to return a signed transaction.\n *\n * It first uses all {@link TransactionModifyingSigner | TransactionModifyingSigners} sequentially before\n * using all {@link TransactionPartialSigner | TransactionPartialSigners} in parallel.\n *\n * If a composite signer implements both interfaces, it will be used as a\n * {@link TransactionModifyingSigner} if no other signer implements that interface.\n * Otherwise, it will be used as a {@link TransactionPartialSigner}.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * const signedTransaction = await partiallySignTransactionMessageWithSigners(transactionMessage);\n * ```\n *\n * It also accepts an optional {@link AbortSignal} that will be propagated to all signers.\n *\n * ```ts\n * const signedTransaction = await partiallySignTransactionMessageWithSigners(transactionMessage, {\n *     abortSignal: myAbortController.signal,\n * });\n * ```\n *\n * @remarks\n * Finally, note that this function ignores {@link TransactionSendingSigner | TransactionSendingSigners}\n * as it does not send the transaction. Check out the {@link signAndSendTransactionMessageWithSigners}\n * function for more details on how to use sending signers.\n *\n * @see {@link signTransactionMessageWithSigners}\n * @see {@link signAndSendTransactionMessageWithSigners}\n */\nexport async function partiallySignTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners &\n        TransactionMessageWithBlockhashLifetime = CompilableTransactionMessageWithSigners &\n        TransactionMessageWithBlockhashLifetime,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Transaction & TransactionWithBlockhashLifetime>;\n\nexport async function partiallySignTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners &\n        TransactionMessageWithDurableNonceLifetime = CompilableTransactionMessageWithSigners &\n        TransactionMessageWithDurableNonceLifetime,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<Transaction & TransactionWithDurableNonceLifetime>>;\n\nexport async function partiallySignTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<Transaction & TransactionWithLifetime>>;\n\nexport async function partiallySignTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<Transaction & TransactionWithLifetime>> {\n    const { partialSigners, modifyingSigners } = categorizeTransactionSigners(\n        deduplicateSigners(getSignersFromTransactionMessage(transactionMessage).filter(isTransactionSigner)),\n        { identifySendingSigner: false },\n    );\n\n    return await signModifyingAndPartialTransactionSigners(\n        transactionMessage,\n        modifyingSigners,\n        partialSigners,\n        config,\n    );\n}\n\n/**\n * Extracts all {@link TransactionSigner | TransactionSigners} inside the provided\n * transaction message and uses them to return a signed transaction before asserting\n * that all signatures required by the transaction are present.\n *\n * This function delegates to the {@link partiallySignTransactionMessageWithSigners} function\n * in order to extract signers from the transaction message and sign the transaction.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * const mySignedTransaction = await signTransactionMessageWithSigners(myTransactionMessage);\n *\n * // With additional config.\n * const mySignedTransaction = await signTransactionMessageWithSigners(myTransactionMessage, {\n *     abortSignal: myAbortController.signal,\n * });\n *\n * // We now know the transaction is fully signed.\n * mySignedTransaction satisfies IFullySignedTransaction;\n * ```\n *\n * @see {@link partiallySignTransactionMessageWithSigners}\n * @see {@link signAndSendTransactionMessageWithSigners}\n */\nexport async function signTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners &\n        TransactionMessageWithBlockhashLifetime = CompilableTransactionMessageWithSigners &\n        TransactionMessageWithBlockhashLifetime,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<FullySignedTransaction & TransactionWithBlockhashLifetime>>;\n\nexport async function signTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners &\n        TransactionMessageWithDurableNonceLifetime = CompilableTransactionMessageWithSigners &\n        TransactionMessageWithDurableNonceLifetime,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<FullySignedTransaction & TransactionWithDurableNonceLifetime>>;\n\nexport async function signTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<FullySignedTransaction & TransactionWithLifetime>>;\n\nexport async function signTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(\n    transactionMessage: TTransactionMessage,\n    config?: TransactionPartialSignerConfig,\n): Promise<Readonly<FullySignedTransaction & TransactionWithLifetime>> {\n    const signedTransaction = await partiallySignTransactionMessageWithSigners(transactionMessage, config);\n    assertTransactionIsFullySigned(signedTransaction);\n    return signedTransaction;\n}\n\n/**\n * Extracts all {@link TransactionSigner | TransactionSigners} inside the provided\n * transaction message and uses them to sign it before sending it immediately to the blockchain.\n *\n * It returns the signature of the sent transaction (i.e. its identifier) as bytes.\n *\n * @typeParam TTransactionMessage - The inferred type of the transaction message provided.\n *\n * @example\n * ```ts\n * import { signAndSendTransactionMessageWithSigners } from '@solana/signers';\n *\n * const transactionSignature = await signAndSendTransactionMessageWithSigners(transactionMessage);\n *\n * // With additional config.\n * const transactionSignature = await signAndSendTransactionMessageWithSigners(transactionMessage, {\n *     abortSignal: myAbortController.signal,\n * });\n * ```\n *\n * @remarks\n * Similarly to the {@link partiallySignTransactionMessageWithSigners} function, it first uses all\n * {@link TransactionModifyingSigner | TransactionModifyingSigners} sequentially before using all\n * {@link TransactionPartialSigner | TransactionPartialSigners} in parallel.\n * It then sends the transaction using the {@link TransactionSendingSigner} it identified.\n *\n * Composite transaction signers are treated such that at least one sending signer is used if any.\n * When a {@link TransactionSigner} implements more than one interface, we use it as a:\n *\n * - {@link TransactionSendingSigner}, if no other {@link TransactionSendingSigner} exists.\n * - {@link TransactionModifyingSigner}, if no other {@link TransactionModifyingSigner} exists.\n * - {@link TransactionPartialSigner}, otherwise.\n *\n * The provided transaction must contain exactly one {@link TransactionSendingSigner} inside its account metas.\n * If more than one composite signers implement the {@link TransactionSendingSigner} interface,\n * one of them will be selected as the sending signer. Otherwise, if multiple\n * {@link TransactionSendingSigner | TransactionSendingSigners} must be selected, the function will throw an error.\n *\n * If you'd like to assert that a transaction makes use of exactly one {@link TransactionSendingSigner}\n * _before_ calling this function, you may use the {@link assertIsTransactionMessageWithSingleSendingSigner} function.\n *\n * Alternatively, you may use the {@link isTransactionMessageWithSingleSendingSigner} function to provide a\n * fallback in case the transaction does not contain any sending signer.\n *\n * @see {@link assertIsTransactionMessageWithSingleSendingSigner}\n * @see {@link isTransactionMessageWithSingleSendingSigner}\n * @see {@link partiallySignTransactionMessageWithSigners}\n * @see {@link signTransactionMessageWithSigners}\n *\n */\nexport async function signAndSendTransactionMessageWithSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(transaction: TTransactionMessage, config?: TransactionSendingSignerConfig): Promise<SignatureBytes> {\n    assertIsTransactionMessageWithSingleSendingSigner(transaction);\n\n    const abortSignal = config?.abortSignal;\n    const { partialSigners, modifyingSigners, sendingSigner } = categorizeTransactionSigners(\n        deduplicateSigners(getSignersFromTransactionMessage(transaction).filter(isTransactionSigner)),\n    );\n\n    abortSignal?.throwIfAborted();\n    const signedTransaction = await signModifyingAndPartialTransactionSigners(\n        transaction,\n        modifyingSigners,\n        partialSigners,\n        config,\n    );\n\n    if (!sendingSigner) {\n        throw new SolanaError(SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING);\n    }\n\n    abortSignal?.throwIfAborted();\n    const [signature] = await sendingSigner.signAndSendTransactions([signedTransaction], config);\n    abortSignal?.throwIfAborted();\n\n    return signature;\n}\n\n/**\n * Identifies each provided TransactionSigner and categorizes them into their respective types.\n * When a signer implements multiple interface, it will try to used to most powerful interface\n * but fallback to the least powerful interface when necessary.\n * For instance, if a signer implements TransactionSendingSigner and TransactionModifyingSigner,\n * it will be categorized as a TransactionSendingSigner if and only if no other signers implement\n * the TransactionSendingSigner interface.\n */\nfunction categorizeTransactionSigners(\n    signers: readonly TransactionSigner[],\n    config: { identifySendingSigner?: boolean } = {},\n): Readonly<{\n    modifyingSigners: readonly TransactionModifyingSigner[];\n    partialSigners: readonly TransactionPartialSigner[];\n    sendingSigner: TransactionSendingSigner | null;\n}> {\n    // Identify the unique sending signer that should be used.\n    const identifySendingSigner = config.identifySendingSigner ?? true;\n    const sendingSigner = identifySendingSigner ? identifyTransactionSendingSigner(signers) : null;\n\n    // Now, focus on the other signers.\n    // I.e. the modifying or partial signers that are not the identified sending signer.\n    // Note that any other sending only signers will be discarded.\n    const otherSigners = signers.filter(\n        (signer): signer is TransactionModifyingSigner | TransactionPartialSigner =>\n            signer !== sendingSigner && (isTransactionModifyingSigner(signer) || isTransactionPartialSigner(signer)),\n    );\n\n    // Identify the modifying signers from the other signers.\n    const modifyingSigners = identifyTransactionModifyingSigners(otherSigners);\n\n    // Use any remaining signers as partial signers.\n    const partialSigners = otherSigners\n        .filter(isTransactionPartialSigner)\n        .filter(signer => !(modifyingSigners as typeof otherSigners).includes(signer));\n\n    return Object.freeze({ modifyingSigners, partialSigners, sendingSigner });\n}\n\n/** Identifies the best signer to use as a TransactionSendingSigner, if any */\nfunction identifyTransactionSendingSigner(signers: readonly TransactionSigner[]): TransactionSendingSigner | null {\n    // Ensure there are any TransactionSendingSigners in the first place.\n    const sendingSigners = signers.filter(isTransactionSendingSigner);\n    if (sendingSigners.length === 0) return null;\n\n    // Prefer sending signers that do not offer other interfaces.\n    const sendingOnlySigners = sendingSigners.filter(\n        signer => !isTransactionModifyingSigner(signer) && !isTransactionPartialSigner(signer),\n    );\n    if (sendingOnlySigners.length > 0) {\n        return sendingOnlySigners[0];\n    }\n\n    // Otherwise, choose any sending signer.\n    return sendingSigners[0];\n}\n\n/** Identifies the best signers to use as TransactionModifyingSigners, if any */\nfunction identifyTransactionModifyingSigners(\n    signers: readonly (TransactionModifyingSigner | TransactionPartialSigner)[],\n): readonly TransactionModifyingSigner[] {\n    // Ensure there are any TransactionModifyingSigner in the first place.\n    const modifyingSigners = signers.filter(isTransactionModifyingSigner);\n    if (modifyingSigners.length === 0) return [];\n\n    // Prefer modifying signers that do not offer partial signing.\n    const nonPartialSigners = modifyingSigners.filter(signer => !isTransactionPartialSigner(signer));\n    if (nonPartialSigners.length > 0) return nonPartialSigners;\n\n    // Otherwise, choose only one modifying signer (whichever).\n    return [modifyingSigners[0]];\n}\n\n/**\n * Signs a transaction using the provided TransactionModifyingSigners\n * sequentially followed by the TransactionPartialSigners in parallel.\n */\nasync function signModifyingAndPartialTransactionSigners<\n    TTransactionMessage extends CompilableTransactionMessageWithSigners = CompilableTransactionMessageWithSigners,\n>(\n    transactionMessage: TTransactionMessage,\n    modifyingSigners: readonly TransactionModifyingSigner[] = [],\n    partialSigners: readonly TransactionPartialSigner[] = [],\n    config?: TransactionModifyingSignerConfig,\n): Promise<Readonly<Transaction & TransactionWithLifetime>> {\n    // serialize the transaction\n    const transaction = compileTransaction(transactionMessage);\n\n    // Handle modifying signers sequentially.\n    const modifiedTransaction = await modifyingSigners.reduce(\n        async (transaction, modifyingSigner) => {\n            config?.abortSignal?.throwIfAborted();\n            const [tx] = await modifyingSigner.modifyAndSignTransactions([await transaction], config);\n            return Object.freeze(tx);\n        },\n        Promise.resolve(transaction) as Promise<Readonly<Transaction & TransactionWithLifetime>>,\n    );\n\n    // Handle partial signers in parallel.\n    config?.abortSignal?.throwIfAborted();\n    const signatureDictionaries = await Promise.all(\n        partialSigners.map(async partialSigner => {\n            const [signatures] = await partialSigner.signTransactions([modifiedTransaction], config);\n            return signatures;\n        }),\n    );\n    const signedTransaction: Readonly<Transaction & TransactionWithLifetime> = {\n        ...modifiedTransaction,\n        signatures: Object.freeze(\n            signatureDictionaries.reduce((signatures, signatureDictionary) => {\n                return { ...signatures, ...signatureDictionary };\n            }, modifiedTransaction.signatures ?? {}),\n        ),\n    };\n\n    return Object.freeze(signedTransaction);\n}\n", "export const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\n", "import { TextEncoder } from '@solana/text-encoding-impl';\n\nimport { SignatureDictionary } from './types';\n\n/**\n * Defines a message that needs signing and its current set of signatures if any.\n *\n * This interface allows {@link MessageModifyingSigner | MessageModifyingSigners}\n * to decide on whether or not they should modify the provided message depending\n * on whether or not signatures already exist for such message.\n *\n * It also helps create a more consistent API by providing a structure analogous\n * to transactions which also keep track of their {@link SignatureDictionary}.\n *\n * @example\n * ```ts\n * import { createSignableMessage } from '@solana/signers';\n *\n * const message = createSignableMessage(new Uint8Array([1, 2, 3]));\n * message.content; // The content of the message as bytes.\n * message.signatures; // The current set of signatures for this message.\n * ```\n *\n * @see {@link createSignableMessage}\n */\nexport type SignableMessage = Readonly<{\n    content: Uint8Array;\n    signatures: SignatureDictionary;\n}>;\n\n/**\n * Creates a {@link SignableMessage} from a `Uint8Array` or a UTF-8 string.\n *\n * It optionally accepts a signature dictionary if the message already contains signatures.\n *\n * @example\n * ```ts\n * const message = createSignableMessage(new Uint8Array([1, 2, 3]));\n * const messageFromText = createSignableMessage('Hello world!');\n * const messageWithSignatures = createSignableMessage('Hello world!', {\n *     '1234..5678': new Uint8Array([1, 2, 3]),\n * });\n * ```\n */\nexport function createSignableMessage(\n    content: Uint8Array | string,\n    signatures: SignatureDictionary = {},\n): SignableMessage {\n    return Object.freeze({\n        content: typeof content === 'string' ? new TextEncoder().encode(content) : content,\n        signatures: Object.freeze({ ...signatures }),\n    });\n}\n"]}