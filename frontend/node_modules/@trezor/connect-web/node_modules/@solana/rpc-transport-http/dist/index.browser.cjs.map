{"version": 3, "sources": ["../src/http-transport-headers.ts", "../src/http-transport.ts", "../src/is-solana-request.ts", "../src/http-transport-for-solana-rpc.ts"], "names": ["SolanaError", "SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN", "SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR", "isJsonRpcPayload", "parseJsonWithBigInts", "stringifyJsonWithBigints"], "mappings": ";;;;;;;AA+CA,IAAM,kBAA8C,GAAA;AAAA,EAChD,MAAQ,EAAA,IAAA;AAAA,EACR,gBAAkB,EAAA,IAAA;AAAA,EAClB,cAAgB,EAAA;AACpB,CAAA;AAEA,IAAM,oCAAoE,MAAA,CAAA,MAAA;AAAA,EACtE;AAAA,IACI,gBAAkB,EAAA,IAAA;AAAA,IAClB,gCAAkC,EAAA,IAAA;AAAA,IAClC,+BAAiC,EAAA,IAAA;AAAA,IACjC,UAAY,EAAA,IAAA;AAAA,IACZ,gBAAkB,EAAA,IAAA;AAAA,IAClB,MAAQ,EAAA,IAAA;AAAA,IACR,IAAM,EAAA,IAAA;AAAA,IACN,GAAK,EAAA,IAAA;AAAA,IACL,MAAQ,EAAA,IAAA;AAAA,IACR,IAAM,EAAA,IAAA;AAAA,IACN,YAAc,EAAA,IAAA;AAAA,IACd,MAAQ,EAAA,IAAA;AAAA,IACR,oBAAsB,EAAA,IAAA;AAAA;AAAA;AAAA;AAAA,IAItB,OAAS,EAAA,IAAA;AAAA,IACT,EAAI,EAAA,IAAA;AAAA,IACJ,OAAS,EAAA,IAAA;AAAA,IACT,mBAAqB,EAAA,IAAA;AAAA,IACrB,OAAS,EAAA,IAAA;AAAA,IACT,GAAK,EAAA;AAAA,GACT;AAAA,EACyB,EAAE,iBAAA,EAAmB,IAAK;AACvD,CAAA;AAEO,SAAS,kCACZ,OAC4C,EAAA;AAC5C,EAAA,MAAM,aAAa,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA,CAAE,OAAO,CAAc,UAAA,KAAA;AACzD,IAAM,MAAA,mBAAA,GAAsB,WAAW,WAAY,EAAA;AACnD,IAAA,OACI,mBAAmB,UAAW,CAAA,WAAA,EAAa,CAAM,KAAA,IAAA,IACjD,kBAAkB,UAAW,CAAA,WAAA,EAAa,CAAA,KAAM,QAChD,mBAAoB,CAAA,UAAA,CAAW,QAAQ,CACvC,IAAA,mBAAA,CAAoB,WAAW,MAAM,CAAA;AAAA,GAE5C,CAAA;AACD,EAAI,IAAA,UAAA,CAAW,SAAS,CAAG,EAAA;AACvB,IAAM,MAAA,IAAIA,mBAAYC,yDAAoD,EAAA;AAAA,MACtE,OAAS,EAAA;AAAA,KACZ,CAAA;AAAA;AAET;AAIO,SAAS,iBACZ,OACiD,EAAA;AACjD,EAAA,MAAM,MAA8B,EAAC;AACrC,EAAA,KAAA,MAAW,cAAc,OAAS,EAAA;AAC9B,IAAA,GAAA,CAAI,UAAW,CAAA,WAAA,EAAa,CAAA,GAAI,QAAQ,UAAU,CAAA;AAAA;AAEtD,EAAO,OAAA,GAAA;AACX;;;ACtGA,IAAI,gDAAmD,GAAA,KAAA;AACvD,SAAS,6CAAgD,GAAA;AACrD,EAAA,IAAI,gDAAkD,EAAA;AAClD,IAAA;AAAA;AAEJ,EAAmD,gDAAA,GAAA,IAAA;AACnD,EAAQ,OAAA,CAAA,IAAA;AAAA,IACJ;AAAA,GAIJ;AACJ;AAiBO,SAAS,oBAAoB,MAA8B,EAAA;AAC9D,EAAA,IAAI,OAAyB,CAAA,GAAA,CAAA,QAAA,KAAA,YAAA,IAAgB,IAAe,IAAA,sBAAA,IAA0B,MAAQ,EAAA;AAC1F,IAA8C,6CAAA,EAAA;AAAA;AAElD,EAAA,MAAM,EAAE,QAAA,EAAU,OAAS,EAAA,MAAA,EAAQ,KAAQ,GAAA,MAAA;AAC3C,EAAI,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAyB,gBAAgB,OAAS,EAAA;AAClD,IAAA,iCAAA,CAAkC,OAAO,CAAA;AAAA;AAE7C,EAAI,IAAA,gBAAA;AAIJ,EAAM,MAAA,aAAA,GAAgB,OAAW,IAAA,gBAAA,CAAiB,OAAO,CAAA;AACzD,EAAA,OAAO,eAAe,eAA2B,CAAA;AAAA,IAC7C,OAAA;AAAA,IACA;AAAA,GAC6D,EAAA;AAC7D,IAAA,MAAM,OAAO,MAAS,GAAA,MAAA,CAAO,OAAO,CAAI,GAAA,IAAA,CAAK,UAAU,OAAO,CAAA;AAC9D,IAAA,MAAM,WAAc,GAAA;AAAA,MAChB,GAAG,gBAAA;AAAA,MACH,IAAA;AAAA,MACA,OAAS,EAAA;AAAA,QACL,GAAG,aAAA;AAAA;AAAA,QAEH,MAAQ,EAAA,kBAAA;AAAA,QACR,gBAAA,EAAkB,IAAK,CAAA,MAAA,CAAO,QAAS,EAAA;AAAA,QACvC,cAAgB,EAAA;AAAA,OACpB;AAAA,MACA,MAAQ,EAAA,MAAA;AAAA,MACR;AAAA,KACJ;AACA,IAAA,MAAM,QAAW,GAAA,MAAM,KAAM,CAAA,GAAA,EAAK,WAAW,CAAA;AAC7C,IAAI,IAAA,CAAC,SAAS,EAAI,EAAA;AACd,MAAM,MAAA,IAAID,mBAAYE,8CAAyC,EAAA;AAAA,QAC3D,SAAS,QAAS,CAAA,OAAA;AAAA,QAClB,SAAS,QAAS,CAAA,UAAA;AAAA,QAClB,YAAY,QAAS,CAAA;AAAA,OACxB,CAAA;AAAA;AAEL,IAAA,IAAI,QAAU,EAAA;AACV,MAAA,OAAO,QAAS,CAAA,MAAM,QAAS,CAAA,IAAA,IAAQ,OAAO,CAAA;AAAA;AAElD,IAAO,OAAA,MAAM,SAAS,IAAK,EAAA;AAAA,GAC/B;AACJ;AC/EA,IAAM,kBAAqB,GAAA;AAAA,EACvB,gBAAA;AAAA,EACA,YAAA;AAAA,EACA,UAAA;AAAA,EACA,oBAAA;AAAA,EACA,gBAAA;AAAA,EACA,oBAAA;AAAA,EACA,WAAA;AAAA,EACA,oBAAA;AAAA,EACA,cAAA;AAAA,EACA,iBAAA;AAAA,EACA,cAAA;AAAA,EACA,kBAAA;AAAA,EACA,kBAAA;AAAA,EACA,wBAAA;AAAA,EACA,gBAAA;AAAA,EACA,WAAA;AAAA,EACA,wBAAA;AAAA,EACA,aAAA;AAAA,EACA,sBAAA;AAAA,EACA,kBAAA;AAAA,EACA,oBAAA;AAAA,EACA,oBAAA;AAAA,EACA,oBAAA;AAAA,EACA,mBAAA;AAAA,EACA,sBAAA;AAAA,EACA,uBAAA;AAAA,EACA,mCAAA;AAAA,EACA,qBAAA;AAAA,EACA,oBAAA;AAAA,EACA,6BAAA;AAAA,EACA,6BAAA;AAAA,EACA,yBAAA;AAAA,EACA,sBAAA;AAAA,EACA,SAAA;AAAA,EACA,eAAA;AAAA,EACA,gBAAA;AAAA,EACA,2BAAA;AAAA,EACA,WAAA;AAAA,EACA,wBAAA;AAAA,EACA,4BAAA;AAAA,EACA,yBAAA;AAAA,EACA,yBAAA;AAAA,EACA,gBAAA;AAAA,EACA,gBAAA;AAAA,EACA,qBAAA;AAAA,EACA,YAAA;AAAA,EACA,iBAAA;AAAA,EACA,OAAA;AAAA,EACA,kBAAA;AAAA,EACA,mBAAA;AAAA,EACA,gBAAA;AAAA,EACA,iBAAA;AAAA,EACA;AACJ,CAAA;AAKO,SAAS,gBAAgB,OAI7B,EAAA;AACC,EAAA,OAAOC,yBAAiB,OAAO,CAAA,IAAM,kBAAyC,CAAA,QAAA,CAAS,QAAQ,MAAM,CAAA;AACzG;;;AC5CO,SAAS,gCAAgC,MAA8B,EAAA;AAC1E,EAAA,OAAO,mBAAoB,CAAA;AAAA,IACvB,GAAG,MAAA;AAAA,IACH,QAAU,EAAA,CAAC,WAAqB,EAAA,OAAA,KAC5B,eAAgB,CAAA,OAAO,CAAI,GAAAC,iCAAA,CAAqB,WAAW,CAAA,GAAI,IAAK,CAAA,KAAA,CAAM,WAAW,CAAA;AAAA,IACzF,MAAA,EAAQ,CAAC,OAAA,KACL,eAAgB,CAAA,OAAO,CAAI,GAAAC,qCAAA,CAAyB,OAAO,CAAA,GAAI,IAAK,CAAA,SAAA,CAAU,OAAO;AAAA,GAC5F,CAAA;AACL", "file": "index.browser.cjs", "sourcesContent": ["import { SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN, SolanaError } from '@solana/errors';\n\nexport type AllowedHttpRequestHeaders = Readonly<\n    {\n        // Someone can still sneak a forbidden header past Typescript if they do something like\n        // fOo-BaR, but at that point they deserve the runtime failure.\n        [K in DisallowedHeaders | ForbiddenHeaders as\n            | Capitalize<Lowercase<K>> // `Foo-bar`\n            | K // `Foo-Bar`\n            | Lowercase<K> // `foo-bar`\n            | Uncapitalize<K> // `foo-Bar`\n            // `FOO-BAR`\n            | Uppercase<K>]?: never;\n    } & { [headerName: string]: string }\n>;\n// These are headers that we simply don't allow the developer to override because they're\n// fundamental to the operation of the JSON-RPC transport.\ntype DisallowedHeaders = 'Accept' | 'Content-Length' | 'Content-Type' | 'Solana-Client';\ntype ForbiddenHeaders =\n    | 'Accept-Charset'\n    // Though technically forbidden in non-Node environments, we don't have a way to target\n    // TypeScript types depending on which platform you are authoring for. `Accept-Encoding` is\n    // therefore omitted from the forbidden headers type, but is still a runtime error in dev mode\n    // when supplied in a non-Node context.\n    // | 'Accept-Encoding'\n    | 'Access-Control-Request-Headers'\n    | 'Access-Control-Request-Method'\n    | 'Connection'\n    | 'Content-Length'\n    | 'Cookie'\n    | 'Date'\n    | 'DNT'\n    | 'Expect'\n    | 'Host'\n    | 'Keep-Alive'\n    | 'Origin'\n    | 'Permissions-Policy'\n    | 'Referer'\n    | 'TE'\n    | 'Trailer'\n    | 'Transfer-Encoding'\n    | 'Upgrade'\n    | 'Via'\n    | `Proxy-${string}`\n    | `Sec-${string}`;\n\n// These are headers which are fundamental to the JSON-RPC transport, and must not be modified.\nconst DISALLOWED_HEADERS: Record<string, boolean> = {\n    accept: true,\n    'content-length': true,\n    'content-type': true,\n};\n// https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name\nconst FORBIDDEN_HEADERS: Record<string, boolean> = /* @__PURE__ */ Object.assign(\n    {\n        'accept-charset': true,\n        'access-control-request-headers': true,\n        'access-control-request-method': true,\n        connection: true,\n        'content-length': true,\n        cookie: true,\n        date: true,\n        dnt: true,\n        expect: true,\n        host: true,\n        'keep-alive': true,\n        origin: true,\n        'permissions-policy': true,\n        // Prefix matching is implemented in code, below.\n        // 'proxy-': true,\n        // 'sec-': true,\n        referer: true,\n        te: true,\n        trailer: true,\n        'transfer-encoding': true,\n        upgrade: true,\n        via: true,\n    },\n    __NODEJS__ ? undefined : { 'accept-encoding': true },\n);\n\nexport function assertIsAllowedHttpRequestHeaders(\n    headers: Record<string, string>,\n): asserts headers is AllowedHttpRequestHeaders {\n    const badHeaders = Object.keys(headers).filter(headerName => {\n        const lowercaseHeaderName = headerName.toLowerCase();\n        return (\n            DISALLOWED_HEADERS[headerName.toLowerCase()] === true ||\n            FORBIDDEN_HEADERS[headerName.toLowerCase()] === true ||\n            lowercaseHeaderName.startsWith('proxy-') ||\n            lowercaseHeaderName.startsWith('sec-')\n        );\n    });\n    if (badHeaders.length > 0) {\n        throw new SolanaError(SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN, {\n            headers: badHeaders,\n        });\n    }\n}\n\n// Lowercasing header names makes it easier to override user-supplied headers, such as those defined\n// in the `DisallowedHeaders` type.\nexport function normalizeHeaders<T extends Record<string, string>>(\n    headers: T,\n): { [K in string & keyof T as Lowercase<K>]: T[K] } {\n    const out: Record<string, string> = {};\n    for (const headerName in headers) {\n        out[headerName.toLowerCase()] = headers[headerName];\n    }\n    return out as { [K in string & keyof T as Lowercase<K>]: T[K] };\n}\n", "import { SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR, SolanaError } from '@solana/errors';\nimport type { RpcTransport } from '@solana/rpc-spec';\nimport type { RpcResponse } from '@solana/rpc-spec-types';\nimport type Dispatcher from 'undici-types/dispatcher';\n\nimport { HttpTransportConfig as Config } from './http-transport-config';\nimport { assertIsAllowedHttpRequestHeaders, normalizeHeaders } from './http-transport-headers';\n\nlet didWarnDispatcherWasSuppliedInNonNodeEnvironment = false;\nfunction warnDispatcherWasSuppliedInNonNodeEnvironment() {\n    if (didWarnDispatcherWasSuppliedInNonNodeEnvironment) {\n        return;\n    }\n    didWarnDispatcherWasSuppliedInNonNodeEnvironment = true;\n    console.warn(\n        'You have supplied a `Dispatcher` to `createHttpTransport()`. It has been ignored ' +\n            'because Undici dispatchers only work in Node environments. To eliminate this ' +\n            'warning, omit the `dispatcher_NODE_ONLY` property from your config when running in ' +\n            'a non-Node environment.',\n    );\n}\n\n/**\n * Creates a function you can use to make `POST` requests with headers suitable for sending JSON\n * data to a server.\n *\n * @example\n * ```ts\n * import { createHttpTransport } from '@solana/rpc-transport-http';\n *\n * const transport = createHttpTransport({ url: 'https://api.mainnet-beta.solana.com' });\n * const response = await transport({\n *     payload: { id: 1, jsonrpc: '2.0', method: 'getSlot' },\n * });\n * const data = await response.json();\n * ```\n */\nexport function createHttpTransport(config: Config): RpcTransport {\n    if (process.env.NODE_ENV !== \"production\" && !__NODEJS__ && 'dispatcher_NODE_ONLY' in config) {\n        warnDispatcherWasSuppliedInNonNodeEnvironment();\n    }\n    const { fromJson, headers, toJson, url } = config;\n    if (process.env.NODE_ENV !== \"production\" && headers) {\n        assertIsAllowedHttpRequestHeaders(headers);\n    }\n    let dispatcherConfig: { dispatcher: Dispatcher | undefined } | undefined;\n    if (__NODEJS__ && 'dispatcher_NODE_ONLY' in config) {\n        dispatcherConfig = { dispatcher: config.dispatcher_NODE_ONLY };\n    }\n    const customHeaders = headers && normalizeHeaders(headers);\n    return async function makeHttpRequest<TResponse>({\n        payload,\n        signal,\n    }: Parameters<RpcTransport>[0]): Promise<RpcResponse<TResponse>> {\n        const body = toJson ? toJson(payload) : JSON.stringify(payload);\n        const requestInfo = {\n            ...dispatcherConfig,\n            body,\n            headers: {\n                ...customHeaders,\n                // Keep these headers lowercase so they will override any user-supplied headers above.\n                accept: 'application/json',\n                'content-length': body.length.toString(),\n                'content-type': 'application/json; charset=utf-8',\n            },\n            method: 'POST',\n            signal,\n        };\n        const response = await fetch(url, requestInfo);\n        if (!response.ok) {\n            throw new SolanaError(SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR, {\n                headers: response.headers,\n                message: response.statusText,\n                statusCode: response.status,\n            });\n        }\n        if (fromJson) {\n            return fromJson(await response.text(), payload) as TResponse;\n        }\n        return await response.json();\n    };\n}\n", "import { isJsonRpcPayload } from '@solana/rpc-spec';\n\nconst SOLANA_RPC_METHODS = [\n    'getAccountInfo',\n    'getBalance',\n    'getBlock',\n    'getBlockCommitment',\n    'getBlockHeight',\n    'getBlockProduction',\n    'getBlocks',\n    'getBlocksWithLimit',\n    'getBlockTime',\n    'getClusterNodes',\n    'getEpochInfo',\n    'getEpochSchedule',\n    'getFeeForMessage',\n    'getFirstAvailableBlock',\n    'getGenesisHash',\n    'getHealth',\n    'getHighestSnapshotSlot',\n    'getIdentity',\n    'getInflationGovernor',\n    'getInflationRate',\n    'getInflationReward',\n    'getLargestAccounts',\n    'getLatestBlockhash',\n    'getLeaderSchedule',\n    'getMaxRetransmitSlot',\n    'getMaxShredInsertSlot',\n    'getMinimumBalanceForRentExemption',\n    'getMultipleAccounts',\n    'getProgramAccounts',\n    'getRecentPerformanceSamples',\n    'getRecentPrioritizationFees',\n    'getSignaturesForAddress',\n    'getSignatureStatuses',\n    'getSlot',\n    'getSlotLeader',\n    'getSlotLeaders',\n    'getStakeMinimumDelegation',\n    'getSupply',\n    'getTokenAccountBalance',\n    'getTokenAccountsByDelegate',\n    'getTokenAccountsByOwner',\n    'getTokenLargestAccounts',\n    'getTokenSupply',\n    'getTransaction',\n    'getTransactionCount',\n    'getVersion',\n    'getVoteAccounts',\n    'index',\n    'isBlockhashValid',\n    'minimumLedgerSlot',\n    'requestAirdrop',\n    'sendTransaction',\n    'simulateTransaction',\n] as const;\n\n/**\n * Helper function that checks if a given `RpcRequest` comes from the Solana RPC API.\n */\nexport function isSolanaRequest(payload: unknown): payload is Readonly<{\n    jsonrpc: '2.0';\n    method: (typeof SOLANA_RPC_METHODS)[number];\n    params: unknown;\n}> {\n    return isJsonRpcPayload(payload) && (SOLANA_RPC_METHODS as readonly string[]).includes(payload.method);\n}\n", "import { RpcTransport } from '@solana/rpc-spec';\nimport { parseJsonWithBigInts, stringifyJsonWithBigints } from '@solana/rpc-spec-types';\n\nimport { createHttpTransport } from './http-transport';\nimport { HttpTransportConfig } from './http-transport-config';\nimport { isSolanaRequest } from './is-solana-request';\n\ntype Config = Pick<HttpTransportConfig, 'dispatcher_NODE_ONLY' | 'headers' | 'url'>;\n\n/**\n * Creates a {@link RpcTransport} that uses JSON HTTP requests — much like the\n * {@link createHttpTransport} function - except that it also uses custom `toJson` and `fromJson`\n * functions in order to allow `bigint` values to be serialized and deserialized correctly over the\n * wire.\n *\n * Since this is something specific to the Solana RPC API, these custom JSON functions are only\n * triggered when the request is recognized as a Solana RPC request. Normal RPC APIs should aim to\n * wrap their `bigint` values — e.g. `u64` or `i64` — in special value objects that represent the\n * number as a string to avoid numerical values going above `Number.MAX_SAFE_INTEGER`.\n *\n * It has the same configuration options as {@link createHttpTransport}, but without the `fromJson`\n * and `toJson` options.\n */\nexport function createHttpTransportForSolanaRpc(config: Config): RpcTransport {\n    return createHttpTransport({\n        ...config,\n        fromJson: (rawResponse: string, payload: unknown) =>\n            isSolanaRequest(payload) ? parseJsonWithBigInts(rawResponse) : JSON.parse(rawResponse),\n        toJson: (payload: unknown) =>\n            isSolanaRequest(payload) ? stringifyJsonWithBigints(payload) : JSON.stringify(payload),\n    });\n}\n"]}