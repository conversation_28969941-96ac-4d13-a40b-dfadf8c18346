{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAwBA,IAAM,WAAA,GAAc,OAAO,SAAU,CAAA,QAAA;AACrC,IAAM,OACF,GAAA,MAAA,CAAO,IACP,IAAA,SAAU,GAAK,EAAA;AACX,EAAA,MAAM,OAAO,EAAC;AACd,EAAA,KAAA,MAAW,QAAQ,GAAK,EAAA;AACpB,IAAA,IAAA,CAAK,KAAK,IAAI,CAAA;AAAA;AAElB,EAAO,OAAA,IAAA;AACX,CAAA;AAEJ,SAAS,SAAA,CAAU,KAAc,WAAsB,EAAA;AACnD,EAAA,IAAI,CAAG,EAAA,GAAA,EAAK,GAAK,EAAA,IAAA,EAAM,KAAK,OAAS,EAAA,KAAA;AACrC,EAAA,IAAI,QAAQ,IAAM,EAAA;AACd,IAAO,OAAA,MAAA;AAAA;AAEX,EAAA,IAAI,QAAQ,KAAO,EAAA;AACf,IAAO,OAAA,OAAA;AAAA;AAEX,EAAA,QAAQ,OAAO,GAAK;AAAA,IAChB,KAAK,QAAA;AACD,MAAA,IAAI,QAAQ,IAAM,EAAA;AACd,QAAO,OAAA,IAAA;AAAA,iBACA,QAAY,IAAA,GAAA,IAAO,OAAO,GAAA,CAAI,WAAW,UAAY,EAAA;AAC5D,QAAA,OAAO,SAAU,CAAA,GAAA,CAAI,MAAO,EAAA,EAAG,WAAW,CAAA;AAAA,OACvC,MAAA;AACH,QAAQ,KAAA,GAAA,WAAA,CAAY,KAAK,GAAG,CAAA;AAC5B,QAAA,IAAI,UAAU,gBAAkB,EAAA;AAC5B,UAAM,GAAA,GAAA,GAAA;AACN,UAAA,GAAA,GAAO,IAAkB,MAAS,GAAA,CAAA;AAClC,UAAA,KAAK,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,GAAA,EAAK,CAAK,EAAA,EAAA;AAEtB,YAAA,GAAA,IAAO,SAAW,CAAA,GAAA,CAAkB,CAAC,CAAA,EAAG,IAAI,CAAI,GAAA,GAAA;AAAA;AAEpD,UAAA,IAAI,MAAM,EAAI,EAAA;AAEV,YAAA,GAAA,IAAO,SAAW,CAAA,GAAA,CAAkB,CAAC,CAAA,EAAG,IAAI,CAAA;AAAA;AAEhD,UAAA,OAAO,GAAM,GAAA,GAAA;AAAA,SACjB,MAAA,IAAW,UAAU,iBAAmB,EAAA;AAEpC,UAAO,IAAA,GAAA,OAAA,CAAQ,GAAG,CAAA,CAAE,IAAK,EAAA;AACzB,UAAA,GAAA,GAAM,IAAK,CAAA,MAAA;AACX,UAAM,GAAA,GAAA,EAAA;AACN,UAAI,CAAA,GAAA,CAAA;AACJ,UAAA,OAAO,IAAI,GAAK,EAAA;AACZ,YAAA,GAAA,GAAM,KAAK,CAAC,CAAA;AACZ,YAAA,OAAA,GAAU,SAAW,CAAA,GAAA,CAAoC,GAAG,CAAA,EAAG,KAAK,CAAA;AACpE,YAAA,IAAI,YAAY,MAAW,EAAA;AACvB,cAAA,IAAI,GAAK,EAAA;AACL,gBAAO,GAAA,IAAA,GAAA;AAAA;AAGX,cAAA,GAAA,IAAO,IAAK,CAAA,SAAA,CAAU,GAAG,CAAA,GAAI,GAAM,GAAA,OAAA;AAAA;AAEvC,YAAA,CAAA,EAAA;AAAA;AAEJ,UAAA,OAAO,MAAM,GAAM,GAAA,GAAA;AAAA,SAChB,MAAA;AACH,UAAO,OAAA,IAAA,CAAK,UAAU,GAAG,CAAA;AAAA;AAC7B;AACJ,IACJ,KAAK,UAAA;AAAA,IACL,KAAK,WAAA;AACD,MAAA,OAAO,cAAc,IAAO,GAAA,MAAA;AAAA,IAChC,KAAK,QAAA;AACD,MAAO,OAAA,CAAA,EAAG,GAAI,CAAA,QAAA,EAAU,CAAA,CAAA,CAAA;AAAA,IAC5B,KAAK,QAAA;AACD,MAAO,OAAA,IAAA,CAAK,UAAU,GAAG,CAAA;AAAA,IAC7B;AACI,MAAO,OAAA,QAAA,CAAS,GAAa,CAAA,GAAI,GAAM,GAAA,IAAA;AAAA;AAEnD;AAQe,SAAR,cAAkB,GAAkC,EAAA;AACvD,EAAM,MAAA,SAAA,GAAY,SAAU,CAAA,GAAA,EAAK,KAAK,CAAA;AACtC,EAAA,IAAI,cAAc,MAAW,EAAA;AAEzB,IAAA,OAAO,EAAK,GAAA,SAAA;AAAA;AAEpB", "file": "index.node.cjs", "sourcesContent": ["/**\n * This project is a fork of [nickyout/fast-stable-stringify](https://github.com/nickyout/fast-stable-stringify)\n *\n * The most popular repository providing this feature is [substack's json-stable-stringify](https://www.npmjs.com/package/json-stable-stringify). The intent of this library is to provide a faster alternative for when performance is more important than features. It assumes you provide basic javascript values without circular references, and returns a non-indented string.\n *\n * Just like substack's, it:\n *\n * - handles all variations of all basic javascript values (number, string, boolean, array, object, null, Date, BigInt)\n * - handles undefined _and_ function in the same way as `JSON.stringify`\n * - **does not support ie8 (and below) with complete certainty**.\n *\n * Unlike substack's, it:\n *\n * - does not implement the 'replacer' or 'space' arguments of the JSON.stringify method\n * - does not check for circular references\n *\n * @example\n * ```js\n * import stringify from '@solana/fast-stable-stringify';\n * stringify({ d: 0, c: 1, a: 2, b: 3, e: 4 }); // '{\"a\":2,\"b\":3,\"c\":1,\"d\":0,\"e\":4}'\n * ```\n *\n * @packageDocumentation\n */\nconst objToString = Object.prototype.toString;\nconst objKeys =\n    Object.keys ||\n    function (obj) {\n        const keys = [];\n        for (const name in obj) {\n            keys.push(name);\n        }\n        return keys;\n    };\n\nfunction stringify(val: unknown, isArrayProp: boolean) {\n    let i, max, str, keys, key, propVal, toStr;\n    if (val === true) {\n        return 'true';\n    }\n    if (val === false) {\n        return 'false';\n    }\n    switch (typeof val) {\n        case 'object':\n            if (val === null) {\n                return null;\n            } else if ('toJSON' in val && typeof val.toJSON === 'function') {\n                return stringify(val.toJSON(), isArrayProp);\n            } else {\n                toStr = objToString.call(val);\n                if (toStr === '[object Array]') {\n                    str = '[';\n                    max = (val as unknown[]).length - 1;\n                    for (i = 0; i < max; i++) {\n                        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n                        str += stringify((val as unknown[])[i], true) + ',';\n                    }\n                    if (max > -1) {\n                        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n                        str += stringify((val as unknown[])[i], true);\n                    }\n                    return str + ']';\n                } else if (toStr === '[object Object]') {\n                    // only object is left\n                    keys = objKeys(val).sort();\n                    max = keys.length;\n                    str = '';\n                    i = 0;\n                    while (i < max) {\n                        key = keys[i];\n                        propVal = stringify((val as Record<typeof key, unknown>)[key], false);\n                        if (propVal !== undefined) {\n                            if (str) {\n                                str += ',';\n                            }\n                            // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n                            str += JSON.stringify(key) + ':' + propVal;\n                        }\n                        i++;\n                    }\n                    return '{' + str + '}';\n                } else {\n                    return JSON.stringify(val);\n                }\n            }\n        case 'function':\n        case 'undefined':\n            return isArrayProp ? null : undefined;\n        case 'bigint':\n            return `${val.toString()}n`;\n        case 'string':\n            return JSON.stringify(val);\n        default:\n            return isFinite(val as number) ? val : null;\n    }\n}\n\nexport default function (\n    val:\n        | Function // eslint-disable-line @typescript-eslint/no-unsafe-function-type\n        | undefined,\n): undefined;\nexport default function (val: unknown): string;\nexport default function (val: unknown): string | undefined {\n    const returnVal = stringify(val, false);\n    if (returnVal !== undefined) {\n        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n        return '' + returnVal;\n    }\n}\n"]}