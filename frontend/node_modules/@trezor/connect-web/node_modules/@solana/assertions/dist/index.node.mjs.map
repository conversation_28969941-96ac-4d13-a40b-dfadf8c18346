{"version": 3, "sources": ["../src/crypto.ts", "../src/subtle-crypto.ts"], "names": ["SolanaError"], "mappings": ";;;AAMO,SAAS,qBAAwB,GAAA;AACpC,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,oBAAoB,UAAY,EAAA;AACrG,IAAM,MAAA,IAAI,YAAY,0DAA0D,CAAA;AAAA;AAExF;ACOA,IAAI,qBAAA;AACJ,eAAe,wBAAwB,MAAwC,EAAA;AAC3E,EAAA,IAAI,0BAA0B,MAAW,EAAA;AACrC,IAAwB,qBAAA,GAAA,IAAI,QAAQ,CAAW,OAAA,KAAA;AAC3C,MACK,MAAA,CAAA,WAAA;AAAA,QAAY,SAAA;AAAA;AAAA,QAA6B,KAAA;AAAA,QAAO,CAAC,QAAQ,QAAQ;AAAA,OAAC,CAClE,KAAK,MAAM;AACR,QAAA,OAAA,CAAS,wBAAwB,IAAK,CAAA;AAAA,OACzC,CACA,CAAA,KAAA,CAAM,MAAM;AACT,QAAA,OAAA,CAAS,wBAAwB,KAAM,CAAA;AAAA,OAC1C,CAAA;AAAA,KACR,CAAA;AAAA;AAEL,EAAI,IAAA,OAAO,0BAA0B,SAAW,EAAA;AAC5C,IAAO,OAAA,qBAAA;AAAA,GACJ,MAAA;AACH,IAAA,OAAO,MAAM,qBAAA;AAAA;AAErB;AAMO,SAAS,iCAAoC,GAAA;AAEhD,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,MAAQ,EAAA,MAAA,KAAW,UAAY,EAAA;AACpG,IAAM,MAAA,IAAIA,YAAY,iDAAiD,CAAA;AAAA;AAE/E;AAMA,eAAsB,8BAAiC,GAAA;AAEnD,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,MAAQ,EAAA,WAAA,KAAgB,UAAY,EAAA;AACzG,IAAM,MAAA,IAAIA,YAAY,4DAA4D,CAAA;AAAA;AAEtF,EAAA,IAAI,CAAE,MAAM,uBAAA,CAAwB,UAAW,CAAA,MAAA,CAAO,MAAM,CAAI,EAAA;AAC5D,IAAM,MAAA,IAAIA,YAAY,4DAA4D,CAAA;AAAA;AAE1F;AAMO,SAAS,4BAA+B,GAAA;AAE3C,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,MAAQ,EAAA,SAAA,KAAc,UAAY,EAAA;AACvG,IAAM,MAAA,IAAIA,YAAY,0DAA0D,CAAA;AAAA;AAExF;AAMO,SAAS,kCAAqC,GAAA;AAEjD,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,MAAQ,EAAA,IAAA,KAAS,UAAY,EAAA;AAClG,IAAM,MAAA,IAAIA,YAAY,wDAAwD,CAAA;AAAA;AAEtF;AAKO,SAAS,uCAA0C,GAAA;AAEtD,EAAI,IAAA,OAAO,WAAW,MAAW,KAAA,WAAA,IAAe,OAAO,UAAW,CAAA,MAAA,CAAO,MAAQ,EAAA,MAAA,KAAW,UAAY,EAAA;AACpG,IAAM,MAAA,IAAIA,YAAY,0DAA0D,CAAA;AAAA;AAExF", "file": "index.node.mjs", "sourcesContent": ["import { SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED, SolanaError } from '@solana/errors';\n\n/**\n * Throws an exception unless {@link Crypto#getRandomValues | `crypto.getRandomValues()`} is\n * available in the current JavaScript environment.\n */\nexport function assertPRNGIsAvailable() {\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.getRandomValues !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED);\n    }\n}\n", "import {\n    SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT,\n    SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED,\n    SolanaError,\n} from '@solana/errors';\n\nfunction assertIsSecureContext() {\n    if (__BROWSER__ && !globalThis.isSecureContext) {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT);\n    }\n}\n\nlet cachedEd25519Decision: PromiseLike<boolean> | boolean | undefined;\nasync function isEd25519CurveSupported(subtle: SubtleCrypto): Promise<boolean> {\n    if (cachedEd25519Decision === undefined) {\n        cachedEd25519Decision = new Promise(resolve => {\n            subtle\n                .generateKey('Ed25519', /* extractable */ false, ['sign', 'verify'])\n                .then(() => {\n                    resolve((cachedEd25519Decision = true));\n                })\n                .catch(() => {\n                    resolve((cachedEd25519Decision = false));\n                });\n        });\n    }\n    if (typeof cachedEd25519Decision === 'boolean') {\n        return cachedEd25519Decision;\n    } else {\n        return await cachedEd25519Decision;\n    }\n}\n\n/**\n * Throws an exception unless {@link SubtleCrypto#digest | `crypto.subtle.digest()`} is available in\n * the current JavaScript environment.\n */\nexport function assertDigestCapabilityIsAvailable() {\n    assertIsSecureContext();\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.subtle?.digest !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED);\n    }\n}\n\n/**\n * Throws an exception unless {@link SubtleCrypto#generateKey | `crypto.subtle.generateKey()`} is\n * available in the current JavaScript environment and has support for the Ed25519 curve.\n */\nexport async function assertKeyGenerationIsAvailable() {\n    assertIsSecureContext();\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.subtle?.generateKey !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED);\n    }\n    if (!(await isEd25519CurveSupported(globalThis.crypto.subtle))) {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED);\n    }\n}\n\n/**\n * Throws an exception unless {@link SubtleCrypto#exportKey | `crypto.subtle.exportKey()`} is\n * available in the current JavaScript environment.\n */\nexport function assertKeyExporterIsAvailable() {\n    assertIsSecureContext();\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.subtle?.exportKey !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED);\n    }\n}\n\n/**\n * Throws an exception unless {@link SubtleCrypto#sign | `crypto.subtle.sign()`} is available in the\n * current JavaScript environment.\n */\nexport function assertSigningCapabilityIsAvailable() {\n    assertIsSecureContext();\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.subtle?.sign !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED);\n    }\n}\n/**\n * Throws an exception unless {@link SubtleCrypto#verify | `crypto.subtle.verify()`} is available in\n * the current JavaScript environment.\n */\nexport function assertVerificationCapabilityIsAvailable() {\n    assertIsSecureContext();\n    if (typeof globalThis.crypto === 'undefined' || typeof globalThis.crypto.subtle?.verify !== 'function') {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED);\n    }\n}\n"]}