'use strict';

var errors = require('@solana/errors');

// src/crypto.ts
function assertPRNGIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.getRandomValues !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED);
  }
}
var cachedEd25519Decision;
async function isEd25519CurveSupported(subtle) {
  if (cachedEd25519Decision === void 0) {
    cachedEd25519Decision = new Promise((resolve) => {
      subtle.generateKey(
        "Ed25519",
        /* extractable */
        false,
        ["sign", "verify"]
      ).then(() => {
        resolve(cachedEd25519Decision = true);
      }).catch(() => {
        resolve(cachedEd25519Decision = false);
      });
    });
  }
  if (typeof cachedEd25519Decision === "boolean") {
    return cachedEd25519Decision;
  } else {
    return await cachedEd25519Decision;
  }
}
function assertDigestCapabilityIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.subtle?.digest !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED);
  }
}
async function assertKeyGenerationIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.subtle?.generateKey !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED);
  }
  if (!await isEd25519CurveSupported(globalThis.crypto.subtle)) {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED);
  }
}
function assertKeyExporterIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.subtle?.exportKey !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED);
  }
}
function assertSigningCapabilityIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.subtle?.sign !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED);
  }
}
function assertVerificationCapabilityIsAvailable() {
  if (typeof globalThis.crypto === "undefined" || typeof globalThis.crypto.subtle?.verify !== "function") {
    throw new errors.SolanaError(errors.SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED);
  }
}

exports.assertDigestCapabilityIsAvailable = assertDigestCapabilityIsAvailable;
exports.assertKeyExporterIsAvailable = assertKeyExporterIsAvailable;
exports.assertKeyGenerationIsAvailable = assertKeyGenerationIsAvailable;
exports.assertPRNGIsAvailable = assertPRNGIsAvailable;
exports.assertSigningCapabilityIsAvailable = assertSigningCapabilityIsAvailable;
exports.assertVerificationCapabilityIsAvailable = assertVerificationCapabilityIsAvailable;
//# sourceMappingURL=index.node.cjs.map
//# sourceMappingURL=index.node.cjs.map