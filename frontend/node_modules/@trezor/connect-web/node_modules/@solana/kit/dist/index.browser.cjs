'use strict';

var accounts = require('@solana/accounts');
var addresses = require('@solana/addresses');
var codecs = require('@solana/codecs');
var errors = require('@solana/errors');
var functional = require('@solana/functional');
var instructions = require('@solana/instructions');
var keys = require('@solana/keys');
var programs = require('@solana/programs');
var rpc = require('@solana/rpc');
var rpcParsedTypes = require('@solana/rpc-parsed-types');
var rpcSubscriptions = require('@solana/rpc-subscriptions');
var rpcTypes = require('@solana/rpc-types');
var signers = require('@solana/signers');
var transactionMessages = require('@solana/transaction-messages');
var transactions = require('@solana/transactions');
var transactionConfirmation = require('@solana/transaction-confirmation');
var rpcSpecTypes = require('@solana/rpc-spec-types');

// src/index.ts

// src/airdrop-internal.ts
async function requestAndConfirmAirdrop_INTERNAL_ONLY_DO_NOT_EXPORT({
  abortSignal,
  commitment,
  confirmSignatureOnlyTransaction,
  lamports,
  recipientAddress,
  rpc
}) {
  const airdropTransactionSignature = await rpc.requestAirdrop(recipientAddress, lamports, { commitment }).send({ abortSignal });
  await confirmSignatureOnlyTransaction({
    abortSignal,
    commitment,
    signature: airdropTransactionSignature
  });
  return airdropTransactionSignature;
}

// src/airdrop.ts
function airdropFactory({
  rpc,
  rpcSubscriptions
}) {
  const getRecentSignatureConfirmationPromise = transactionConfirmation.createRecentSignatureConfirmationPromiseFactory({
    rpc,
    rpcSubscriptions
  });
  async function confirmSignatureOnlyTransaction(config) {
    await transactionConfirmation.waitForRecentTransactionConfirmationUntilTimeout({
      ...config,
      getRecentSignatureConfirmationPromise,
      getTimeoutPromise: transactionConfirmation.getTimeoutPromise
    });
  }
  return async function airdrop(config) {
    return await requestAndConfirmAirdrop_INTERNAL_ONLY_DO_NOT_EXPORT({
      ...config,
      confirmSignatureOnlyTransaction,
      rpc
    });
  };
}
var COMPUTE_BUDGET_PROGRAM_ADDRESS = "ComputeBudget111111111111111111111111111111";
var INVALID_BUT_SUFFICIENT_FOR_COMPILATION_BLOCKHASH = {
  blockhash: "11111111111111111111111111111111",
  lastValidBlockHeight: 0n
  // This is not included in compiled transactions; it can be anything.
};
var SET_COMPUTE_UNIT_LIMIT_INSTRUCTION_INDEX = 2;
function createComputeUnitLimitInstruction(units) {
  const data = new Uint8Array(5);
  data[0] = SET_COMPUTE_UNIT_LIMIT_INSTRUCTION_INDEX;
  codecs.getU32Encoder().write(
    units,
    data,
    1
    /* offset */
  );
  return Object.freeze({
    data,
    programAddress: COMPUTE_BUDGET_PROGRAM_ADDRESS
  });
}
function isSetComputeLimitInstruction(instruction) {
  return instructions.isInstructionForProgram(instruction, COMPUTE_BUDGET_PROGRAM_ADDRESS) && instructions.isInstructionWithData(instruction) && instruction.data[0] === SET_COMPUTE_UNIT_LIMIT_INSTRUCTION_INDEX;
}
async function getComputeUnitEstimateForTransactionMessage_INTERNAL_ONLY_DO_NOT_EXPORT({
  abortSignal,
  rpc,
  transactionMessage,
  ...simulateConfig
}) {
  const isDurableNonceTransactionMessage = transactionMessages.isDurableNonceTransaction(transactionMessage);
  let compilableTransactionMessage;
  if (isDurableNonceTransactionMessage || transactionMessages.isTransactionMessageWithBlockhashLifetime(transactionMessage)) {
    compilableTransactionMessage = transactionMessage;
  } else {
    compilableTransactionMessage = transactionMessages.setTransactionMessageLifetimeUsingBlockhash(
      INVALID_BUT_SUFFICIENT_FOR_COMPILATION_BLOCKHASH,
      transactionMessage
    );
  }
  const existingSetComputeUnitLimitInstructionIndex = transactionMessage.instructions.findIndex(isSetComputeLimitInstruction);
  const maxComputeUnitLimitInstruction = createComputeUnitLimitInstruction(
    14e5
    /* MAX_COMPUTE_UNIT_LIMIT */
  );
  if (existingSetComputeUnitLimitInstructionIndex === -1) {
    compilableTransactionMessage = transactionMessages.appendTransactionMessageInstruction(
      maxComputeUnitLimitInstruction,
      compilableTransactionMessage
    );
  } else {
    const nextInstructions = [...compilableTransactionMessage.instructions];
    nextInstructions.splice(existingSetComputeUnitLimitInstructionIndex, 1, maxComputeUnitLimitInstruction);
    compilableTransactionMessage = Object.freeze({
      ...compilableTransactionMessage,
      instructions: nextInstructions
    });
  }
  const compiledTransaction = transactions.compileTransaction(compilableTransactionMessage);
  const wireTransactionBytes = transactions.getBase64EncodedWireTransaction(compiledTransaction);
  try {
    const {
      value: { err: transactionError, unitsConsumed }
    } = await rpc.simulateTransaction(wireTransactionBytes, {
      ...simulateConfig,
      encoding: "base64",
      replaceRecentBlockhash: !isDurableNonceTransactionMessage,
      sigVerify: false
    }).send({ abortSignal });
    if (unitsConsumed == null) {
      throw new errors.SolanaError(errors.SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT);
    }
    const downcastUnitsConsumed = unitsConsumed > 4294967295n ? 4294967295 : Number(unitsConsumed);
    if (transactionError) {
      throw new errors.SolanaError(errors.SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT, {
        cause: transactionError,
        unitsConsumed: downcastUnitsConsumed
      });
    }
    return downcastUnitsConsumed;
  } catch (e) {
    if (errors.isSolanaError(e, errors.SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT)) throw e;
    throw new errors.SolanaError(errors.SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT, {
      cause: e
    });
  }
}

// src/compute-limit.ts
function getComputeUnitEstimateForTransactionMessageFactory({
  rpc
}) {
  return async function getComputeUnitEstimateForTransactionMessage(transactionMessage, config) {
    return await getComputeUnitEstimateForTransactionMessage_INTERNAL_ONLY_DO_NOT_EXPORT({
      ...config,
      rpc,
      transactionMessage
    });
  };
}
async function fetchAddressesForLookupTables(lookupTableAddresses, rpc, config) {
  if (lookupTableAddresses.length === 0) {
    return {};
  }
  const fetchedLookupTables = await accounts.fetchJsonParsedAccounts(
    rpc,
    lookupTableAddresses,
    config
  );
  accounts.assertAccountsDecoded(fetchedLookupTables);
  accounts.assertAccountsExist(fetchedLookupTables);
  return fetchedLookupTables.reduce((acc, lookup) => {
    return {
      ...acc,
      [lookup.address]: lookup.data.addresses
    };
  }, {});
}

// src/decompile-transaction-message-fetching-lookup-tables.ts
async function decompileTransactionMessageFetchingLookupTables(compiledTransactionMessage, rpc, config) {
  const lookupTables = "addressTableLookups" in compiledTransactionMessage && compiledTransactionMessage.addressTableLookups !== void 0 && compiledTransactionMessage.addressTableLookups.length > 0 ? compiledTransactionMessage.addressTableLookups : [];
  const lookupTableAddresses = lookupTables.map((l) => l.lookupTableAddress);
  const { lastValidBlockHeight, ...fetchAccountsConfig } = config ?? {};
  const addressesByLookupTableAddress = lookupTableAddresses.length > 0 ? await fetchAddressesForLookupTables(lookupTableAddresses, rpc, fetchAccountsConfig) : {};
  return transactionMessages.decompileTransactionMessage(compiledTransactionMessage, {
    addressesByLookupTableAddress,
    lastValidBlockHeight
  });
}
function getSendTransactionConfigWithAdjustedPreflightCommitment(commitment, config) {
  if (
    // The developer has supplied no value for `preflightCommitment`.
    !config?.preflightCommitment && // The value of `commitment` is lower than the server default of `preflightCommitment`.
    rpcTypes.commitmentComparator(
      commitment,
      "finalized"
      /* default value of `preflightCommitment` */
    ) < 0
  ) {
    return {
      ...config,
      // In the common case, it is unlikely that you want to simulate a transaction at
      // `finalized` commitment when your standard of commitment for confirming the
      // transaction is lower. Cap the simulation commitment level to the level of the
      // confirmation commitment.
      preflightCommitment: commitment
    };
  }
  return config;
}
async function sendTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
  abortSignal,
  commitment,
  rpc,
  transaction,
  ...sendTransactionConfig
}) {
  const base64EncodedWireTransaction = transactions.getBase64EncodedWireTransaction(transaction);
  return await rpc.sendTransaction(base64EncodedWireTransaction, {
    ...getSendTransactionConfigWithAdjustedPreflightCommitment(commitment, sendTransactionConfig),
    encoding: "base64"
  }).send({ abortSignal });
}
async function sendAndConfirmDurableNonceTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
  abortSignal,
  commitment,
  confirmDurableNonceTransaction,
  rpc,
  transaction,
  ...sendTransactionConfig
}) {
  const transactionSignature = await sendTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
    ...sendTransactionConfig,
    abortSignal,
    commitment,
    rpc,
    transaction
  });
  await confirmDurableNonceTransaction({
    abortSignal,
    commitment,
    transaction
  });
  return transactionSignature;
}
async function sendAndConfirmTransactionWithBlockhashLifetime_INTERNAL_ONLY_DO_NOT_EXPORT({
  abortSignal,
  commitment,
  confirmRecentTransaction,
  rpc,
  transaction,
  ...sendTransactionConfig
}) {
  const transactionSignature = await sendTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
    ...sendTransactionConfig,
    abortSignal,
    commitment,
    rpc,
    transaction
  });
  await confirmRecentTransaction({
    abortSignal,
    commitment,
    transaction
  });
  return transactionSignature;
}

// src/send-and-confirm-durable-nonce-transaction.ts
function sendAndConfirmDurableNonceTransactionFactory({
  rpc,
  rpcSubscriptions
}) {
  const getNonceInvalidationPromise = transactionConfirmation.createNonceInvalidationPromiseFactory({ rpc, rpcSubscriptions });
  const getRecentSignatureConfirmationPromise = transactionConfirmation.createRecentSignatureConfirmationPromiseFactory({
    rpc,
    rpcSubscriptions
  });
  async function confirmDurableNonceTransaction(config) {
    await transactionConfirmation.waitForDurableNonceTransactionConfirmation({
      ...config,
      getNonceInvalidationPromise,
      getRecentSignatureConfirmationPromise
    });
  }
  return async function sendAndConfirmDurableNonceTransaction(transaction, config) {
    await sendAndConfirmDurableNonceTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
      ...config,
      confirmDurableNonceTransaction,
      rpc,
      transaction
    });
  };
}
function sendAndConfirmTransactionFactory({
  rpc,
  rpcSubscriptions
}) {
  const getBlockHeightExceedencePromise = transactionConfirmation.createBlockHeightExceedencePromiseFactory({
    rpc,
    rpcSubscriptions
  });
  const getRecentSignatureConfirmationPromise = transactionConfirmation.createRecentSignatureConfirmationPromiseFactory({
    rpc,
    rpcSubscriptions
  });
  async function confirmRecentTransaction(config) {
    await transactionConfirmation.waitForRecentTransactionConfirmation({
      ...config,
      getBlockHeightExceedencePromise,
      getRecentSignatureConfirmationPromise
    });
  }
  return async function sendAndConfirmTransaction(transaction, config) {
    await sendAndConfirmTransactionWithBlockhashLifetime_INTERNAL_ONLY_DO_NOT_EXPORT({
      ...config,
      confirmRecentTransaction,
      rpc,
      transaction
    });
  };
}

// src/send-transaction-without-confirming.ts
function sendTransactionWithoutConfirmingFactory({
  rpc
}) {
  return async function sendTransactionWithoutConfirming(transaction, config) {
    await sendTransaction_INTERNAL_ONLY_DO_NOT_EXPORT({
      ...config,
      rpc,
      transaction
    });
  };
}

Object.defineProperty(exports, "createRpcMessage", {
  enumerable: true,
  get: function () { return rpcSpecTypes.createRpcMessage; }
});
exports.airdropFactory = airdropFactory;
exports.decompileTransactionMessageFetchingLookupTables = decompileTransactionMessageFetchingLookupTables;
exports.fetchAddressesForLookupTables = fetchAddressesForLookupTables;
exports.getComputeUnitEstimateForTransactionMessageFactory = getComputeUnitEstimateForTransactionMessageFactory;
exports.sendAndConfirmDurableNonceTransactionFactory = sendAndConfirmDurableNonceTransactionFactory;
exports.sendAndConfirmTransactionFactory = sendAndConfirmTransactionFactory;
exports.sendTransactionWithoutConfirmingFactory = sendTransactionWithoutConfirmingFactory;
Object.keys(accounts).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return accounts[k]; }
  });
});
Object.keys(addresses).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return addresses[k]; }
  });
});
Object.keys(codecs).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return codecs[k]; }
  });
});
Object.keys(errors).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return errors[k]; }
  });
});
Object.keys(functional).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return functional[k]; }
  });
});
Object.keys(instructions).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return instructions[k]; }
  });
});
Object.keys(keys).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return keys[k]; }
  });
});
Object.keys(programs).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return programs[k]; }
  });
});
Object.keys(rpc).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return rpc[k]; }
  });
});
Object.keys(rpcParsedTypes).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return rpcParsedTypes[k]; }
  });
});
Object.keys(rpcSubscriptions).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return rpcSubscriptions[k]; }
  });
});
Object.keys(rpcTypes).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return rpcTypes[k]; }
  });
});
Object.keys(signers).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return signers[k]; }
  });
});
Object.keys(transactionMessages).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return transactionMessages[k]; }
  });
});
Object.keys(transactions).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return transactions[k]; }
  });
});
//# sourceMappingURL=index.browser.cjs.map
//# sourceMappingURL=index.browser.cjs.map