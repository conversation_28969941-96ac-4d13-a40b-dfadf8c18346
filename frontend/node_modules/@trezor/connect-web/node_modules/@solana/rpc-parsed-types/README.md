[![npm][npm-image]][npm-url] [![npm-downloads][npm-downloads-image]][npm-url]
<br />
[![code-style-prettier][code-style-prettier-image]][code-style-prettier-url]

[code-style-prettier-image]: https://img.shields.io/badge/code_style-prettier-ff69b4.svg?style=flat-square
[code-style-prettier-url]: https://github.com/prettier/prettier
[npm-downloads-image]: https://img.shields.io/npm/dm/@solana/rpc-types?style=flat
[npm-image]: https://img.shields.io/npm/v/@solana/rpc-types?style=flat
[npm-url]: https://www.npmjs.com/package/@solana/rpc-types

# @solana/rpc-parsed-types

This package defines types for parsed objects returned by methods of the [Solana JSON-RPC](https://docs.solana.com/api/http). It can be used standalone, but it is also exported as part of Kit [`@solana/kit`](https://github.com/anza-xyz/kit/tree/main/packages/kit).
