{"version": 3, "sources": ["../src/program-error.ts"], "names": ["isSolana<PERSON>rror", "SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM"], "mappings": ";;;;;AA8BO,SAAS,cACZ,CAAA,KAAA,EACA,kBACA,EAAA,cAAA,EACA,IAE4D,EAAA;AAC5D,EAAA,IAAI,CAACA,oBAAA,CAAc,KAAO,EAAAC,8CAAuC,CAAG,EAAA;AAChE,IAAO,OAAA,KAAA;AAAA;AAEX,EAAA,MAAM,4BAA4B,kBAAmB,CAAA,YAAA,CAAa,KAAM,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA,cAAA;AACxF,EAAI,IAAA,CAAC,yBAA6B,IAAA,yBAAA,KAA8B,cAAgB,EAAA;AAC5E,IAAO,OAAA,KAAA;AAAA;AAEX,EAAA,OAAO,OAAO,IAAA,KAAS,WAAe,IAAA,KAAA,CAAM,QAAQ,IAAS,KAAA,IAAA;AACjE", "file": "index.node.cjs", "sourcesContent": ["import type { Address } from '@solana/addresses';\nimport { isSolanaError, SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM, SolanaError } from '@solana/errors';\n\n/**\n * Identifies whether an error -- typically caused by a transaction failure -- is a custom program\n * error from the provided program address.\n *\n * @param transactionMessage The transaction message that failed to execute. Since the RPC response\n * only provides the index of the failed instruction, the transaction message is required to\n * determine its program address\n * @param programAddress The address of the program from which the error is expected to have\n * originated\n * @param code The expected error code of the custom program error. When provided, the function will\n * check that the custom program error code matches the given value.\n *\n * @example\n * ```ts\n * try {\n *     // Send and confirm your transaction.\n * } catch (error) {\n *     if (isProgramError(error, transactionMessage, myProgramAddress, 42)) {\n *         // Handle custom program error 42 from this program.\n *     } else if (isProgramError(error, transactionMessage, myProgramAddress)) {\n *         // Handle all other custom program errors from this program.\n *     } else {\n *         throw error;\n *     }\n * }\n * ```\n */\nexport function isProgramError<TProgramErrorCode extends number>(\n    error: unknown,\n    transactionMessage: { instructions: Record<number, { programAddress: Address }> },\n    programAddress: Address,\n    code?: TProgramErrorCode,\n): error is Readonly<{ context: Readonly<{ code: TProgramErrorCode }> }> &\n    SolanaError<typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM> {\n    if (!isSolanaError(error, SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM)) {\n        return false;\n    }\n    const instructionProgramAddress = transactionMessage.instructions[error.context.index]?.programAddress;\n    if (!instructionProgramAddress || instructionProgramAddress !== programAddress) {\n        return false;\n    }\n    return typeof code === 'undefined' || error.context.code === code;\n}\n"]}