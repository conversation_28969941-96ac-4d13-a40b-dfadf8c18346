{"version": 3, "sources": ["../src/address.ts", "../src/vendor/noble/ed25519.ts", "../src/curve.ts", "../src/program-derived-address.ts", "../src/public-key.ts"], "names": ["SolanaError", "address"], "mappings": ";;;;;;AA4BA,IAAI,qBAAA;AACJ,IAAI,qBAAA;AAEJ,SAAS,wBAA4C,GAAA;AACjD,EAAI,IAAA,CAAC,qBAAuB,EAAA,qBAAA,GAAwB,gBAAiB,EAAA;AACrE,EAAO,OAAA,qBAAA;AACX;AAEA,SAAS,wBAA4C,GAAA;AACjD,EAAI,IAAA,CAAC,qBAAuB,EAAA,qBAAA,GAAwB,gBAAiB,EAAA;AACrE,EAAO,OAAA,qBAAA;AACX;AAoBO,SAAS,UAAU,eAA6E,EAAA;AAEnG,EAAA;AAAA;AAAA,IAEI,gBAAgB,MAAS,GAAA,EAAA;AAAA,IAEzB,gBAAgB,MAAS,GAAA;AAAA,IAC3B;AACE,IAAO,OAAA,KAAA;AAAA;AAGX,EAAA,MAAM,gBAAgB,wBAAyB,EAAA;AAC/C,EAAI,IAAA;AACA,IAAA,OAAO,aAAc,CAAA,MAAA,CAAO,eAAe,CAAA,CAAE,UAAe,KAAA,EAAA;AAAA,GACxD,CAAA,MAAA;AACJ,IAAO,OAAA,KAAA;AAAA;AAEf;AA2BO,SAAS,gBAAgB,eAAqF,EAAA;AAEjH,EAAA;AAAA;AAAA,IAEI,gBAAgB,MAAS,GAAA,EAAA;AAAA,IAEzB,gBAAgB,MAAS,GAAA;AAAA,IAC3B;AACE,IAAM,MAAA,IAAI,YAAY,mDAAqD,EAAA;AAAA,MACvE,cAAc,eAAgB,CAAA;AAAA,KACjC,CAAA;AAAA;AAGL,EAAA,MAAM,gBAAgB,wBAAyB,EAAA;AAC/C,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,MAAA,CAAO,eAAe,CAAA;AAClD,EAAA,MAAM,WAAW,KAAM,CAAA,UAAA;AACvB,EAAA,IAAI,aAAa,EAAI,EAAA;AACjB,IAAM,MAAA,IAAI,YAAY,4CAA8C,EAAA;AAAA,MAChE,YAAc,EAAA;AAAA,KACjB,CAAA;AAAA;AAET;AAyBO,SAAS,QAA0C,eAA8C,EAAA;AACpG,EAAA,eAAA,CAAgB,eAAe,CAAA;AAC/B,EAAO,OAAA,eAAA;AACX;AAoBO,SAAS,iBAAmD,GAAA;AAC/D,EAAO,OAAA,gBAAA;AAAA,IAAiB,cAAA,CAAe,wBAAyB,EAAA,EAAG,EAAE,CAAA;AAAA,IAAG,CAAA,eAAA,KACpE,QAAQ,eAAe;AAAA,GAC3B;AACJ;AAoBO,SAAS,iBAAmD,GAAA;AAC/D,EAAO,OAAA,cAAA,CAAe,wBAAyB,EAAA,EAAG,EAAE,CAAA;AACxD;AAQO,SAAS,eAAwD,GAAA;AACpE,EAAA,OAAO,YAAa,CAAA,iBAAA,EAAqB,EAAA,iBAAA,EAAmB,CAAA;AAChE;AAEO,SAAS,oBAAyD,GAAA;AACrE,EAAO,OAAA,IAAI,IAAK,CAAA,QAAA,CAAS,IAAM,EAAA;AAAA,IAC3B,SAAW,EAAA,OAAA;AAAA,IACX,iBAAmB,EAAA,KAAA;AAAA,IACnB,aAAe,EAAA,UAAA;AAAA,IACf,OAAS,EAAA,KAAA;AAAA,IACT,WAAa,EAAA,SAAA;AAAA,IACb,KAAO,EAAA;AAAA,GACV,CAAE,CAAA,OAAA;AACP;;;AClMA,IAAM,CAAI,GAAA,8EAAA;AACV,IAAM,CAAI,GAAA,8EAAA;AACV,IAAM,GAAM,GAAA,8EAAA;AAGZ,SAAS,IAAI,CAAmB,EAAA;AAC5B,EAAA,MAAM,IAAI,CAAI,GAAA,CAAA;AACd,EAAO,OAAA,CAAA,IAAK,EAAK,GAAA,CAAA,GAAI,CAAI,GAAA,CAAA;AAC7B;AACA,SAAS,IAAA,CAAK,GAAW,KAAuB,EAAA;AAE5C,EAAA,IAAI,CAAI,GAAA,CAAA;AACR,EAAA,OAAO,UAAU,EAAI,EAAA;AACjB,IAAK,CAAA,IAAA,CAAA;AACL,IAAK,CAAA,IAAA,CAAA;AAAA;AAET,EAAO,OAAA,CAAA;AACX;AACA,SAAS,YAAY,CAAmB,EAAA;AAEpC,EAAM,MAAA,EAAA,GAAM,IAAI,CAAK,GAAA,CAAA;AACrB,EAAM,MAAA,EAAA,GAAM,KAAK,CAAK,GAAA,CAAA;AACtB,EAAA,MAAM,EAAM,GAAA,IAAA,CAAK,EAAI,EAAA,EAAE,IAAI,EAAM,GAAA,CAAA;AACjC,EAAA,MAAM,EAAM,GAAA,IAAA,CAAK,EAAI,EAAA,EAAE,IAAI,CAAK,GAAA,CAAA;AAChC,EAAA,MAAM,GAAO,GAAA,IAAA,CAAK,EAAI,EAAA,EAAE,IAAI,EAAM,GAAA,CAAA;AAClC,EAAA,MAAM,GAAO,GAAA,IAAA,CAAK,GAAK,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACrC,EAAA,MAAM,GAAO,GAAA,IAAA,CAAK,GAAK,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACrC,EAAA,MAAM,GAAO,GAAA,IAAA,CAAK,GAAK,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACrC,EAAA,MAAM,IAAQ,GAAA,IAAA,CAAK,GAAK,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACtC,EAAA,MAAM,IAAQ,GAAA,IAAA,CAAK,IAAM,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACvC,EAAA,MAAM,IAAQ,GAAA,IAAA,CAAK,IAAM,EAAA,GAAG,IAAI,GAAO,GAAA,CAAA;AACvC,EAAA,MAAM,SAAa,GAAA,IAAA,CAAK,IAAM,EAAA,EAAE,IAAI,CAAK,GAAA,CAAA;AACzC,EAAO,OAAA,SAAA;AACX;AACA,SAAS,OAAA,CAAQ,GAAW,CAA0B,EAAA;AAElD,EAAA,MAAM,EAAK,GAAA,GAAA,CAAI,CAAI,GAAA,CAAA,GAAI,CAAC,CAAA;AACxB,EAAA,MAAM,EAAK,GAAA,GAAA,CAAI,EAAK,GAAA,EAAA,GAAK,CAAC,CAAA;AAC1B,EAAM,MAAA,GAAA,GAAM,WAAY,CAAA,CAAA,GAAI,EAAE,CAAA;AAC9B,EAAA,IAAI,CAAI,GAAA,GAAA,CAAI,CAAI,GAAA,EAAA,GAAK,GAAG,CAAA;AACxB,EAAA,MAAM,GAAM,GAAA,GAAA,CAAI,CAAI,GAAA,CAAA,GAAI,CAAC,CAAA;AACzB,EAAA,MAAM,KAAQ,GAAA,CAAA;AACd,EAAM,MAAA,KAAA,GAAQ,GAAI,CAAA,CAAA,GAAI,GAAG,CAAA;AACzB,EAAA,MAAM,WAAW,GAAQ,KAAA,CAAA;AACzB,EAAA,MAAM,QAAW,GAAA,GAAA,KAAQ,GAAI,CAAA,CAAC,CAAC,CAAA;AAC/B,EAAA,MAAM,MAAS,GAAA,GAAA,KAAQ,GAAI,CAAA,CAAC,IAAI,GAAG,CAAA;AACnC,EAAA,IAAI,UAAc,CAAA,GAAA,KAAA;AAClB,EAAI,IAAA,QAAA,IAAY,QAAY,CAAA,GAAA,KAAA;AAC5B,EAAK,IAAA,CAAA,GAAA,CAAI,CAAC,CAAI,GAAA,EAAA,MAAQ,IAAQ,CAAA,GAAA,GAAA,CAAI,CAAC,CAAC,CAAA;AACpC,EAAI,IAAA,CAAC,QAAY,IAAA,CAAC,QAAU,EAAA;AACxB,IAAO,OAAA,IAAA;AAAA;AAEX,EAAO,OAAA,CAAA;AACX;AAEO,SAAS,cAAA,CAAe,GAAW,QAA2B,EAAA;AACjE,EAAM,MAAA,EAAA,GAAK,GAAI,CAAA,CAAA,GAAI,CAAC,CAAA;AACpB,EAAM,MAAA,CAAA,GAAI,GAAI,CAAA,EAAA,GAAK,EAAE,CAAA;AACrB,EAAA,MAAM,CAAI,GAAA,GAAA,CAAI,CAAI,GAAA,EAAA,GAAK,EAAE,CAAA;AACzB,EAAM,MAAA,CAAA,GAAI,OAAQ,CAAA,CAAA,EAAG,CAAC,CAAA;AACtB,EAAA,IAAI,MAAM,IAAM,EAAA;AACZ,IAAO,OAAA,KAAA;AAAA;AAEX,EAAM,MAAA,aAAA,GAAA,CAAiB,WAAW,GAAU,MAAA,CAAA;AAC5C,EAAI,IAAA,CAAA,KAAM,MAAM,aAAe,EAAA;AAC3B,IAAO,OAAA,KAAA;AAAA;AAEX,EAAO,OAAA,IAAA;AACX;;;AC3FA,SAAS,UAAU,IAAsB,EAAA;AACrC,EAAM,MAAA,SAAA,GAAY,IAAK,CAAA,QAAA,CAAS,EAAE,CAAA;AAClC,EAAI,IAAA,SAAA,CAAU,WAAW,CAAG,EAAA;AACxB,IAAA,OAAO,IAAI,SAAS,CAAA,CAAA;AAAA,GACjB,MAAA;AACH,IAAO,OAAA,SAAA;AAAA;AAEf;AAEA,SAAS,qBAAqB,KAA2B,EAAA;AACrD,EAAA,MAAM,YAAY,KAAM,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,IAAA,EAAM,OAAO,CAAG,EAAA,SAAA,CAAU,OAAO,EAAK,GAAA,IAAA,GAAO,IAAQ,GAAA,IAAI,CAAC,CAAG,EAAA,GAAG,IAAI,EAAE,CAAA;AAC3G,EAAM,MAAA,oBAAA,GAAuB,KAAK,SAAS,CAAA,CAAA;AAC3C,EAAA,OAAO,OAAO,oBAAoB,CAAA;AACtC;AAEO,SAAS,+BAA+B,KAA4B,EAAA;AACvE,EAAI,IAAA,KAAA,CAAM,eAAe,EAAI,EAAA;AACzB,IAAO,OAAA,KAAA;AAAA;AAEX,EAAM,MAAA,CAAA,GAAI,qBAAqB,KAAK,CAAA;AACpC,EAAA,OAAO,cAAe,CAAA,CAAA,EAAG,KAAM,CAAA,EAAE,CAAC,CAAA;AACtC;;;ACoBO,SAAS,wBACZ,KACwC,EAAA;AACxC,EAAA,OACI,KAAM,CAAA,OAAA,CAAQ,KAAK,CAAA,IACnB,KAAM,CAAA,MAAA,KAAW,CACjB,IAAA,OAAO,KAAM,CAAA,CAAC,CAAM,KAAA,QAAA,IACpB,OAAO,KAAA,CAAM,CAAC,CAAA,KAAM,QACpB,IAAA,KAAA,CAAM,CAAC,CAAA,IAAK,CACZ,IAAA,KAAA,CAAM,CAAC,CAAA,IAAK,GACZ,IAAA,SAAA,CAAU,KAAM,CAAA,CAAC,CAAC,CAAA;AAE1B;AAQO,SAAS,8BACZ,KACgD,EAAA;AAChD,EAAA,MAAM,cACF,KAAM,CAAA,OAAA,CAAQ,KAAK,CAAA,IAAK,MAAM,MAAW,KAAA,CAAA,IAAK,OAAO,KAAA,CAAM,CAAC,CAAM,KAAA,QAAA,IAAY,OAAO,KAAA,CAAM,CAAC,CAAM,KAAA,QAAA;AACtG,EAAA,IAAI,CAAC,WAAa,EAAA;AACd,IAAM,MAAA,IAAIA,YAAY,sCAAsC,CAAA;AAAA;AAEhE,EAAA,IAAI,MAAM,CAAC,CAAA,GAAI,KAAK,KAAM,CAAA,CAAC,IAAI,GAAK,EAAA;AAChC,IAAM,MAAA,IAAIA,YAAY,mDAAqD,EAAA;AAAA,MACvE,IAAA,EAAM,MAAM,CAAC;AAAA,KAChB,CAAA;AAAA;AAEL,EAAgB,eAAA,CAAA,KAAA,CAAM,CAAC,CAAC,CAAA;AAC5B;AAeA,IAAM,eAAkB,GAAA,EAAA;AACxB,IAAM,SAAY,GAAA,EAAA;AAClB,IAAM,gBAAmB,GAAA;AAAA;AAAA,EAErB,EAAA;AAAA,EAAI,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,EAAA;AAAA,EAAI,GAAA;AAAA,EAAK,EAAA;AAAA,EAAI,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,EAAA;AAAA,EAAI,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK,GAAA;AAAA,EAAK;AACpG,CAAA;AAEA,eAAe,2BAA4B,CAAA,EAAE,cAAgB,EAAA,KAAA,EAAuD,EAAA;AAChH,EAAkC,iCAAA,EAAA;AAClC,EAAI,IAAA,KAAA,CAAM,SAAS,SAAW,EAAA;AAC1B,IAAM,MAAA,IAAIA,YAAY,yDAA2D,EAAA;AAAA,MAC7E,QAAQ,KAAM,CAAA,MAAA;AAAA,MACd,QAAU,EAAA;AAAA,KACb,CAAA;AAAA;AAEL,EAAI,IAAA,WAAA;AACJ,EAAA,MAAM,YAAY,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,MAAM,EAAO,KAAA;AAC9C,IAAM,MAAA,KAAA,GAAQ,OAAO,IAAA,KAAS,QAAY,GAAA,CAAA,WAAA,KAAgB,IAAI,WAAY,EAAA,EAAG,MAAO,CAAA,IAAI,CAAI,GAAA,IAAA;AAC5F,IAAI,IAAA,KAAA,CAAM,aAAa,eAAiB,EAAA;AACpC,MAAM,MAAA,IAAIA,YAAY,qDAAuD,EAAA;AAAA,QACzE,QAAQ,KAAM,CAAA,UAAA;AAAA,QACd,KAAO,EAAA,EAAA;AAAA,QACP,aAAe,EAAA;AAAA,OAClB,CAAA;AAAA;AAEL,IAAI,GAAA,CAAA,IAAA,CAAK,GAAG,KAAK,CAAA;AACjB,IAAO,OAAA,GAAA;AAAA,GACX,EAAG,EAAc,CAAA;AACjB,EAAA,MAAM,4BAA4B,eAAgB,EAAA;AAClD,EAAM,MAAA,mBAAA,GAAsB,yBAA0B,CAAA,MAAA,CAAO,cAAc,CAAA;AAC3E,EAAM,MAAA,kBAAA,GAAqB,MAAM,MAAA,CAAO,MAAO,CAAA,MAAA;AAAA,IAC3C,SAAA;AAAA,IACA,IAAI,WAAW,CAAC,GAAG,WAAW,GAAG,mBAAA,EAAqB,GAAG,gBAAgB,CAAC;AAAA,GAC9E;AACA,EAAM,MAAA,YAAA,GAAe,IAAI,UAAA,CAAW,kBAAkB,CAAA;AACtD,EAAI,IAAA,8BAAA,CAA+B,YAAY,CAAG,EAAA;AAC9C,IAAM,MAAA,IAAIA,YAAY,qDAAqD,CAAA;AAAA;AAE/E,EAAO,OAAA,yBAAA,CAA0B,OAAO,YAAY,CAAA;AACxD;AAwBA,eAAsB,wBAAyB,CAAA;AAAA,EAC3C,cAAA;AAAA,EACA;AACJ,CAA+D,EAAA;AAC3D,EAAA,IAAI,QAAW,GAAA,GAAA;AACf,EAAA,OAAO,WAAW,CAAG,EAAA;AACjB,IAAI,IAAA;AACA,MAAMC,MAAAA,QAAAA,GAAU,MAAM,2BAA4B,CAAA;AAAA,QAC9C,cAAA;AAAA,QACA,KAAA,EAAO,CAAC,GAAG,KAAA,EAAO,IAAI,UAAW,CAAA,CAAC,QAAQ,CAAC,CAAC;AAAA,OAC/C,CAAA;AACD,MAAO,OAAA,CAACA,UAAS,QAAqC,CAAA;AAAA,aACjD,CAAG,EAAA;AACR,MAAI,IAAA,aAAA,CAAc,CAAG,EAAA,qDAAqD,CAAG,EAAA;AACzE,QAAA,QAAA,EAAA;AAAA,OACG,MAAA;AACH,QAAM,MAAA,CAAA;AAAA;AACV;AACJ;AAEJ,EAAM,MAAA,IAAID,YAAY,4DAA4D,CAAA;AACtF;AAmBA,eAAsB,qBAAsB,CAAA,EAAE,WAAa,EAAA,cAAA,EAAgB,MAAqC,EAAA;AAC5G,EAAA,MAAM,EAAE,MAAA,EAAQ,MAAO,EAAA,GAAI,eAAgB,EAAA;AAE3C,EAAM,MAAA,SAAA,GAAY,OAAO,IAAS,KAAA,QAAA,GAAW,IAAI,WAAY,EAAA,CAAE,MAAO,CAAA,IAAI,CAAI,GAAA,IAAA;AAC9E,EAAI,IAAA,SAAA,CAAU,aAAa,eAAiB,EAAA;AACxC,IAAM,MAAA,IAAIA,YAAY,qDAAuD,EAAA;AAAA,MACzE,QAAQ,SAAU,CAAA,UAAA;AAAA,MAClB,KAAO,EAAA,CAAA;AAAA,MACP,aAAe,EAAA;AAAA,KAClB,CAAA;AAAA;AAGL,EAAM,MAAA,mBAAA,GAAsB,OAAO,cAAc,CAAA;AACjD,EAAA,IACI,oBAAoB,MAAU,IAAA,gBAAA,CAAiB,UAC/C,mBAAoB,CAAA,KAAA,CAAM,CAAC,gBAAiB,CAAA,MAAM,CAAE,CAAA,KAAA,CAAM,CAAC,IAAM,EAAA,KAAA,KAAU,SAAS,gBAAiB,CAAA,KAAK,CAAC,CAC7G,EAAA;AACE,IAAM,MAAA,IAAIA,YAAY,iDAAiD,CAAA;AAAA;AAG3E,EAAM,MAAA,kBAAA,GAAqB,MAAM,MAAA,CAAO,MAAO,CAAA,MAAA;AAAA,IAC3C,SAAA;AAAA,IACA,IAAI,UAAW,CAAA,CAAC,GAAG,MAAA,CAAO,WAAW,CAAA,EAAG,GAAG,SAAA,EAAW,GAAG,mBAAmB,CAAC;AAAA,GACjF;AACA,EAAM,MAAA,YAAA,GAAe,IAAI,UAAA,CAAW,kBAAkB,CAAA;AAEtD,EAAA,OAAO,OAAO,YAAY,CAAA;AAC9B;AC/MA,eAAsB,wBAAwB,SAAwC,EAAA;AAClF,EAA6B,4BAAA,EAAA;AAC7B,EAAA,IAAI,UAAU,IAAS,KAAA,QAAA,IAAY,SAAU,CAAA,SAAA,CAAU,SAAS,SAAW,EAAA;AACvE,IAAM,MAAA,IAAIA,YAAY,mDAAmD,CAAA;AAAA;AAE7E,EAAA,MAAM,iBAAiB,MAAM,MAAA,CAAO,MAAO,CAAA,SAAA,CAAU,OAAO,SAAS,CAAA;AACrE,EAAA,OAAO,mBAAoB,CAAA,MAAA,CAAO,IAAI,UAAA,CAAW,cAAc,CAAC,CAAA;AACpE;AAYA,eAAsB,wBAAwBC,QAAkB,EAAA;AAC5D,EAAA,MAAM,YAAe,GAAA,iBAAA,EAAoB,CAAA,MAAA,CAAOA,QAAO,CAAA;AACvD,EAAA,OAAO,MAAM,MAAA,CAAO,MAAO,CAAA,SAAA,CAAU,KAAO,EAAA,YAAA,EAAc,EAAE,IAAA,EAAM,SAAU,EAAA,EAAG,IAAwB,EAAA,CAAC,QAAQ,CAAC,CAAA;AACrH", "file": "index.browser.mjs", "sourcesContent": ["import {\n    combine<PERSON>odec,\n    Decoder,\n    Encoder,\n    fixDecoder<PERSON>ize,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoderSize,\n    transformEncoder,\n} from '@solana/codecs-core';\nimport { getBase58Decoder, getBase58Encoder } from '@solana/codecs-strings';\nimport {\n    SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE,\n    SolanaError,\n} from '@solana/errors';\nimport { Brand, EncodedString } from '@solana/nominal-types';\n\n/**\n * Represents a string that validates as a Solana address. Functions that require well-formed\n * addresses should specify their inputs in terms of this type.\n *\n * Whenever you need to validate an arbitrary string as a base58-encoded address, use the\n * {@link address}, {@link assertIsAddress}, or {@link isAddress} functions in this package.\n */\nexport type Address<TAddress extends string = string> = Brand<EncodedString<TAddress, 'base58'>, 'Address'>;\n\nlet memoizedBase58Encoder: Encoder<string> | undefined;\nlet memoizedBase58Decoder: Decoder<string> | undefined;\n\nfunction getMemoizedBase58Encoder(): Encoder<string> {\n    if (!memoizedBase58Encoder) memoizedBase58Encoder = getBase58Encoder();\n    return memoizedBase58Encoder;\n}\n\nfunction getMemoizedBase58Decoder(): Decoder<string> {\n    if (!memoizedBase58Decoder) memoizedBase58Decoder = getBase58Decoder();\n    return memoizedBase58Decoder;\n}\n\n/**\n * A type guard that returns `true` if the input string conforms to the {@link Address} type, and\n * refines its type for use in your program.\n *\n * @example\n * ```ts\n * import { isAddress } from '@solana/addresses';\n *\n * if (isAddress(ownerAddress)) {\n *     // At this point, `ownerAddress` has been refined to a\n *     // `Address` that can be used with the RPC.\n *     const { value: lamports } = await rpc.getBalance(ownerAddress).send();\n *     setBalanceLamports(lamports);\n * } else {\n *     setError(`${ownerAddress} is not an address`);\n * }\n * ```\n */\nexport function isAddress(putativeAddress: string): putativeAddress is Address<typeof putativeAddress> {\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest address (32 bytes of zeroes)\n        putativeAddress.length < 32 ||\n        // Highest address (32 bytes of 255)\n        putativeAddress.length > 44\n    ) {\n        return false;\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const base58Encoder = getMemoizedBase58Encoder();\n    try {\n        return base58Encoder.encode(putativeAddress).byteLength === 32;\n    } catch {\n        return false;\n    }\n}\n\n/**\n * From time to time you might acquire a string, that you expect to validate as an address or public\n * key, from an untrusted network API or user input. Use this function to assert that such an\n * arbitrary string is a base58-encoded address.\n *\n * @example\n * ```ts\n * import { assertIsAddress } from '@solana/addresses';\n *\n * // Imagine a function that fetches an account's balance when a user submits a form.\n * function handleSubmit() {\n *     // We know only that what the user typed conforms to the `string` type.\n *     const address: string = accountAddressInput.value;\n *     try {\n *         // If this type assertion function doesn't throw, then\n *         // Typescript will upcast `address` to `Address`.\n *         assertIsAddress(address);\n *         // At this point, `address` is an `Address` that can be used with the RPC.\n *         const balanceInLamports = await rpc.getBalance(address).send();\n *     } catch (e) {\n *         // `address` turned out not to be a base58-encoded address\n *     }\n * }\n * ```\n */\nexport function assertIsAddress(putativeAddress: string): asserts putativeAddress is Address<typeof putativeAddress> {\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest address (32 bytes of zeroes)\n        putativeAddress.length < 32 ||\n        // Highest address (32 bytes of 255)\n        putativeAddress.length > 44\n    ) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE, {\n            actualLength: putativeAddress.length,\n        });\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const base58Encoder = getMemoizedBase58Encoder();\n    const bytes = base58Encoder.encode(putativeAddress);\n    const numBytes = bytes.byteLength;\n    if (numBytes !== 32) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH, {\n            actualLength: numBytes,\n        });\n    }\n}\n\n/**\n * Combines _asserting_ that a string is an address with _coercing_ it to the {@link Address} type.\n * It's most useful with untrusted input.\n *\n * @example\n * ```ts\n * import { address } from '@solana/addresses';\n *\n * await transfer(address(fromAddress), address(toAddress), lamports(100000n));\n * ```\n *\n * > [!TIP]\n * > When starting from a known-good address as a string, it's more efficient to typecast it rather\n * than to use the {@link address} helper, because the helper unconditionally performs validation on\n * its input.\n * >\n * > ```ts\n * > import { Address } from '@solana/addresses';\n * >\n * > const MEMO_PROGRAM_ADDRESS =\n * >     'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr' as Address<'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'>;\n * > ```\n */\nexport function address<TAddress extends string = string>(putativeAddress: TAddress): Address<TAddress> {\n    assertIsAddress(putativeAddress);\n    return putativeAddress as Address<TAddress>;\n}\n\n/**\n * Returns an encoder that you can use to encode a base58-encoded address to a byte array.\n *\n * @example\n * ```ts\n * import { getAddressEncoder } from '@solana/addresses';\n *\n * const address = 'B9Lf9z5BfNPT4d5KMeaBFx8x1G4CULZYR1jA2kmxRDka' as Address;\n * const addressEncoder = getAddressEncoder();\n * const addressBytes = addressEncoder.encode(address);\n * // Uint8Array(32) [\n * //   150, 183, 190,  48, 171,   8, 39, 156,\n * //   122, 213, 172, 108, 193,  95, 26, 158,\n * //   149, 243, 115, 254,  20, 200, 36,  30,\n * //   248, 179, 178, 232, 220,  89, 53, 127\n * // ]\n * ```\n */\nexport function getAddressEncoder(): FixedSizeEncoder<Address, 32> {\n    return transformEncoder(fixEncoderSize(getMemoizedBase58Encoder(), 32), putativeAddress =>\n        address(putativeAddress),\n    );\n}\n\n/**\n * Returns a decoder that you can use to convert an array of 32 bytes representing an address to the\n * base58-encoded representation of that address.\n *\n * @example\n * ```ts\n * import { getAddressDecoder } from '@solana/addresses';\n *\n * const addressBytes = new Uint8Array([\n *     150, 183, 190,  48, 171,   8, 39, 156,\n *     122, 213, 172, 108, 193,  95, 26, 158,\n *     149, 243, 115, 254,  20, 200, 36,  30,\n *     248, 179, 178, 232, 220,  89, 53, 127\n * ]);\n * const addressDecoder = getAddressDecoder();\n * const address = addressDecoder.decode(addressBytes); // B9Lf9z5BfNPT4d5KMeaBFx8x1G4CULZYR1jA2kmxRDka\n * ```\n */\nexport function getAddressDecoder(): FixedSizeDecoder<Address, 32> {\n    return fixDecoderSize(getMemoizedBase58Decoder(), 32) as FixedSizeDecoder<Address, 32>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to a base-58 encoded address.\n *\n * @see {@link getAddressDecoder}\n * @see {@link getAddressEncoder}\n */\nexport function getAddressCodec(): FixedSizeCodec<Address, Address, 32> {\n    return combineCodec(getAddressEncoder(), getAddressDecoder());\n}\n\nexport function getAddressComparator(): (x: string, y: string) => number {\n    return new Intl.Collator('en', {\n        caseFirst: 'lower',\n        ignorePunctuation: false,\n        localeMatcher: 'best fit',\n        numeric: false,\n        sensitivity: 'variant',\n        usage: 'sort',\n    }).compare;\n}\n", "/**!\n * noble-ed25519\n *\n * The MIT License (MIT)\n *\n * Copyright (c) 2019 <PERSON> (https://paulmillr.com)\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the “Software”), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nconst D = 37095705934669439343138083508754565189542113879843219016388785533085940283555n;\nconst P = 57896044618658097711785492504343953926634992332820282019728792003956564819949n; // 2n ** 255n - 19n;  ed25519 is twisted edwards curve\nconst RM1 = 19681161376707505956807079304988542015446066515923890162744021073123829784752n; // √-1\n\n// mod division\nfunction mod(a: bigint): bigint {\n    const r = a % P;\n    return r >= 0n ? r : P + r;\n}\nfunction pow2(x: bigint, power: bigint): bigint {\n    // pow2(x, 4) == x^(2^4)\n    let r = x;\n    while (power-- > 0n) {\n        r *= r;\n        r %= P;\n    }\n    return r;\n}\nfunction pow_2_252_3(x: bigint): bigint {\n    // x^(2^252-3) unrolled util for square root\n    const x2 = (x * x) % P; // x^2,       bits 1\n    const b2 = (x2 * x) % P; // x^3,       bits 11\n    const b4 = (pow2(b2, 2n) * b2) % P; // x^(2^4-1), bits 1111\n    const b5 = (pow2(b4, 1n) * x) % P; // x^(2^5-1), bits 11111\n    const b10 = (pow2(b5, 5n) * b5) % P; // x^(2^10)\n    const b20 = (pow2(b10, 10n) * b10) % P; // x^(2^20)\n    const b40 = (pow2(b20, 20n) * b20) % P; // x^(2^40)\n    const b80 = (pow2(b40, 40n) * b40) % P; // x^(2^80)\n    const b160 = (pow2(b80, 80n) * b80) % P; // x^(2^160)\n    const b240 = (pow2(b160, 80n) * b80) % P; // x^(2^240)\n    const b250 = (pow2(b240, 10n) * b10) % P; // x^(2^250)\n    const pow_p_5_8 = (pow2(b250, 2n) * x) % P; // < To pow to (p+3)/8, multiply it by x.\n    return pow_p_5_8;\n}\nfunction uvRatio(u: bigint, v: bigint): bigint | null {\n    // for sqrt comp\n    const v3 = mod(v * v * v); // v³\n    const v7 = mod(v3 * v3 * v); // v⁷\n    const pow = pow_2_252_3(u * v7); // (uv⁷)^(p-5)/8\n    let x = mod(u * v3 * pow); // (uv³)(uv⁷)^(p-5)/8\n    const vx2 = mod(v * x * x); // vx²\n    const root1 = x; // First root candidate\n    const root2 = mod(x * RM1); // Second root candidate; RM1 is √-1\n    const useRoot1 = vx2 === u; // If vx² = u (mod p), x is a square root\n    const useRoot2 = vx2 === mod(-u); // If vx² = -u, set x <-- x * 2^((p-1)/4)\n    const noRoot = vx2 === mod(-u * RM1); // There is no valid root, vx² = -u√-1\n    if (useRoot1) x = root1;\n    if (useRoot2 || noRoot) x = root2; // We return root2 anyway, for const-time\n    if ((mod(x) & 1n) === 1n) x = mod(-x); // edIsNegative\n    if (!useRoot1 && !useRoot2) {\n        return null;\n    }\n    return x;\n}\n// https://datatracker.ietf.org/doc/html/rfc8032#section-5.1.3\nexport function pointIsOnCurve(y: bigint, lastByte: number): boolean {\n    const y2 = mod(y * y); // y²\n    const u = mod(y2 - 1n); // u=y²-1\n    const v = mod(D * y2 + 1n);\n    const x = uvRatio(u, v); // (uv³)(uv⁷)^(p-5)/8; square root\n    if (x === null) {\n        return false;\n    }\n    const isLastByteOdd = (lastByte & 0x80) !== 0; // x_0, last bit\n    if (x === 0n && isLastByteOdd) {\n        return false;\n    }\n    return true;\n}\n", "import { pointIsOnCurve } from './vendor/noble/ed25519';\n\nfunction byteToHex(byte: number): string {\n    const hexString = byte.toString(16);\n    if (hexString.length === 1) {\n        return `0${hexString}`;\n    } else {\n        return hexString;\n    }\n}\n\nfunction decompressPointBytes(bytes: Uint8Array): bigint {\n    const hexString = bytes.reduce((acc, byte, ii) => `${byteToHex(ii === 31 ? byte & ~0x80 : byte)}${acc}`, '');\n    const integerLiteralString = `0x${hexString}`;\n    return BigInt(integerLiteralString);\n}\n\nexport function compressedPointBytesAreOnCurve(bytes: Uint8Array): boolean {\n    if (bytes.byteLength !== 32) {\n        return false;\n    }\n    const y = decompressPointBytes(bytes);\n    return pointIsOnCurve(y, bytes[31]);\n}\n", "import { assertDigestCapabilityIsAvailable } from '@solana/assertions';\nimport type { ReadonlyUint8Array } from '@solana/codecs-core';\nimport {\n    isSolanaError,\n    SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED,\n    SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE,\n    SOLANA_ERROR__ADDRESSES__MALFORMED_PDA,\n    SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE,\n    SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER,\n    SolanaError,\n} from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\n\nimport { Address, assertIsAddress, getAddressCodec, isAddress } from './address';\nimport { compressedPointBytesAreOnCurve } from './curve';\n\n/**\n * A tuple representing a program derived address (derived from the address of some program and a\n * set of seeds) and the associated bump seed used to ensure that the address, as derived, does not\n * fall on the Ed25519 curve.\n *\n * Whenever you need to validate an arbitrary tuple as one that represents a program derived\n * address, use the {@link assertIsProgramDerivedAddress} or {@link isProgramDerivedAddress}\n * functions in this package.\n */\nexport type ProgramDerivedAddress<TAddress extends string = string> = Readonly<\n    [Address<TAddress>, ProgramDerivedAddressBump]\n>;\n\n/**\n * Represents an integer in the range [0,255] used in the derivation of a program derived address to\n * ensure that it does not fall on the Ed25519 curve.\n */\nexport type ProgramDerivedAddressBump = Brand<number, 'ProgramDerivedAddressBump'>;\n\n/**\n * A type guard that returns `true` if the input tuple conforms to the {@link ProgramDerivedAddress}\n * type, and refines its type for use in your program.\n *\n * @see The {@link isAddress} function for an example of how to use a type guard.\n */\nexport function isProgramDerivedAddress<TAddress extends string = string>(\n    value: unknown,\n): value is ProgramDerivedAddress<TAddress> {\n    return (\n        Array.isArray(value) &&\n        value.length === 2 &&\n        typeof value[0] === 'string' &&\n        typeof value[1] === 'number' &&\n        value[1] >= 0 &&\n        value[1] <= 255 &&\n        isAddress(value[0])\n    );\n}\n\n/**\n * In the event that you receive an address/bump-seed tuple from some untrusted source, use this\n * function to assert that it conforms to the {@link ProgramDerivedAddress} interface.\n *\n * @see The {@link assertIsAddress} function for an example of how to use an assertion function.\n */\nexport function assertIsProgramDerivedAddress<TAddress extends string = string>(\n    value: unknown,\n): asserts value is ProgramDerivedAddress<TAddress> {\n    const validFormat =\n        Array.isArray(value) && value.length === 2 && typeof value[0] === 'string' && typeof value[1] === 'number';\n    if (!validFormat) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__MALFORMED_PDA);\n    }\n    if (value[1] < 0 || value[1] > 255) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE, {\n            bump: value[1],\n        });\n    }\n    assertIsAddress(value[0]);\n}\n\ntype ProgramDerivedAddressInput = Readonly<{\n    programAddress: Address;\n    seeds: Seed[];\n}>;\n\ntype SeedInput = Readonly<{\n    baseAddress: Address;\n    programAddress: Address;\n    seed: Seed;\n}>;\n\ntype Seed = ReadonlyUint8Array | string;\n\nconst MAX_SEED_LENGTH = 32;\nconst MAX_SEEDS = 16;\nconst PDA_MARKER_BYTES = [\n    // The string 'ProgramDerivedAddress'\n    80, 114, 111, 103, 114, 97, 109, 68, 101, 114, 105, 118, 101, 100, 65, 100, 100, 114, 101, 115, 115,\n] as const;\n\nasync function createProgramDerivedAddress({ programAddress, seeds }: ProgramDerivedAddressInput): Promise<Address> {\n    assertDigestCapabilityIsAvailable();\n    if (seeds.length > MAX_SEEDS) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED, {\n            actual: seeds.length,\n            maxSeeds: MAX_SEEDS,\n        });\n    }\n    let textEncoder: TextEncoder;\n    const seedBytes = seeds.reduce((acc, seed, ii) => {\n        const bytes = typeof seed === 'string' ? (textEncoder ||= new TextEncoder()).encode(seed) : seed;\n        if (bytes.byteLength > MAX_SEED_LENGTH) {\n            throw new SolanaError(SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED, {\n                actual: bytes.byteLength,\n                index: ii,\n                maxSeedLength: MAX_SEED_LENGTH,\n            });\n        }\n        acc.push(...bytes);\n        return acc;\n    }, [] as number[]);\n    const base58EncodedAddressCodec = getAddressCodec();\n    const programAddressBytes = base58EncodedAddressCodec.encode(programAddress);\n    const addressBytesBuffer = await crypto.subtle.digest(\n        'SHA-256',\n        new Uint8Array([...seedBytes, ...programAddressBytes, ...PDA_MARKER_BYTES]),\n    );\n    const addressBytes = new Uint8Array(addressBytesBuffer);\n    if (compressedPointBytesAreOnCurve(addressBytes)) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE);\n    }\n    return base58EncodedAddressCodec.decode(addressBytes);\n}\n\n/**\n * Given a program's {@link Address} and up to 16 {@link Seed | Seeds}, this method will return the\n * program derived address (PDA) associated with each.\n *\n * @example\n * ```ts\n * import { getAddressEncoder, getProgramDerivedAddress } from '@solana/addresses';\n *\n * const addressEncoder = getAddressEncoder();\n * const [pda, bumpSeed] = await getProgramDerivedAddress({\n *     programAddress: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' as Address,\n *     seeds: [\n *         // Owner\n *         addressEncoder.encode('9fYLFVoVqwH37C3dyPi6cpeobfbQ2jtLpN5HgAYDDdkm' as Address),\n *         // Token program\n *         addressEncoder.encode('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address),\n *         // Mint\n *         addressEncoder.encode('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' as Address),\n *     ],\n * });\n * ```\n */\nexport async function getProgramDerivedAddress({\n    programAddress,\n    seeds,\n}: ProgramDerivedAddressInput): Promise<ProgramDerivedAddress> {\n    let bumpSeed = 255;\n    while (bumpSeed > 0) {\n        try {\n            const address = await createProgramDerivedAddress({\n                programAddress,\n                seeds: [...seeds, new Uint8Array([bumpSeed])],\n            });\n            return [address, bumpSeed as ProgramDerivedAddressBump];\n        } catch (e) {\n            if (isSolanaError(e, SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE)) {\n                bumpSeed--;\n            } else {\n                throw e;\n            }\n        }\n    }\n    throw new SolanaError(SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED);\n}\n\n/**\n * Returns a base58-encoded address derived from some base address, some program address, and a seed\n * string or byte array.\n *\n * @example\n * ```ts\n * import { createAddressWithSeed } from '@solana/addresses';\n *\n * const derivedAddress = await createAddressWithSeed({\n *     // The private key associated with this address will be able to sign for `derivedAddress`.\n *     baseAddress: 'B9Lf9z5BfNPT4d5KMeaBFx8x1G4CULZYR1jA2kmxRDka' as Address,\n *     // Only this program will be able to write data to this account.\n *     programAddress: '445erYq578p2aERrGW9mn9KiYe3fuG6uHdcJ2LPPShGw' as Address,\n *     seed: 'data-account',\n * });\n * ```\n */\nexport async function createAddressWithSeed({ baseAddress, programAddress, seed }: SeedInput): Promise<Address> {\n    const { encode, decode } = getAddressCodec();\n\n    const seedBytes = typeof seed === 'string' ? new TextEncoder().encode(seed) : seed;\n    if (seedBytes.byteLength > MAX_SEED_LENGTH) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED, {\n            actual: seedBytes.byteLength,\n            index: 0,\n            maxSeedLength: MAX_SEED_LENGTH,\n        });\n    }\n\n    const programAddressBytes = encode(programAddress);\n    if (\n        programAddressBytes.length >= PDA_MARKER_BYTES.length &&\n        programAddressBytes.slice(-PDA_MARKER_BYTES.length).every((byte, index) => byte === PDA_MARKER_BYTES[index])\n    ) {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER);\n    }\n\n    const addressBytesBuffer = await crypto.subtle.digest(\n        'SHA-256',\n        new Uint8Array([...encode(baseAddress), ...seedBytes, ...programAddressBytes]),\n    );\n    const addressBytes = new Uint8Array(addressBytesBuffer);\n\n    return decode(addressBytes);\n}\n", "import { assertKeyExporterIsAvailable } from '@solana/assertions';\nimport { SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY, SolanaError } from '@solana/errors';\n\nimport { Address, getAddressDecoder, getAddressEncoder } from './address';\n\n/**\n * Given a public {@link CryptoKey}, this method will return its associated {@link Address}.\n *\n * @example\n * ```ts\n * import { getAddressFromPublicKey } from '@solana/addresses';\n *\n * const address = await getAddressFromPublicKey(publicKey);\n * ```\n */\nexport async function getAddressFromPublicKey(publicKey: CryptoKey): Promise<Address> {\n    assertKeyExporterIsAvailable();\n    if (publicKey.type !== 'public' || publicKey.algorithm.name !== 'Ed25519') {\n        throw new SolanaError(SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY);\n    }\n    const publicKeyBytes = await crypto.subtle.exportKey('raw', publicKey);\n    return getAddressDecoder().decode(new Uint8Array(publicKeyBytes));\n}\n\n/**\n * Given an {@link Address}, return a {@link CryptoKey} that can be used to verify signatures.\n *\n * @example\n * ```ts\n * import { getAddressFromPublicKey } from '@solana/addresses';\n *\n * const publicKey = await getPublicKeyFromAddress(address);\n * ```\n */\nexport async function getPublicKeyFromAddress(address: Address) {\n    const addressBytes = getAddressEncoder().encode(address);\n    return await crypto.subtle.importKey('raw', addressBytes, { name: 'Ed25519' }, true /* extractable */, ['verify']);\n}\n"]}