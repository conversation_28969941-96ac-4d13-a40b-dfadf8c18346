{"version": 3, "sources": ["../src/pipe.ts"], "names": [], "mappings": ";AAyOO,SAAS,IAAA,CAAe,SAAmB,GAAyB,EAAA;AACvE,EAAO,OAAA,GAAA,CAAI,OAAO,CAAC,GAAA,EAAK,OAAO,EAAG,CAAA,GAAG,GAAG,IAAI,CAAA;AAChD", "file": "index.native.mjs", "sourcesContent": ["/**\n * A pipeline is a solution that allows you to perform successive transforms of a value using functions. This is useful when building up a transaction message.\n *\n * Until the [pipeline operator](https://github.com/tc39/proposal-pipeline-operator) becomes part of JavaScript you can use this utility to create pipelines.\n *\n * Following common implementations of pipe functions that use TypeScript, this function supports a maximum arity of 10 for type safety.\n *\n * Note you can use nested pipes to extend this limitation, like so:\n * ```ts\n * const myValue = pipe(\n *      pipe(\n *          1,\n *          (x) => x + 1,\n *          (x) => x * 2,\n *          (x) => x - 1,\n *      ),\n *      (y) => y / 3,\n *      (y) => y + 1,\n * );\n * ```\n *\n * @see https://github.com/ramda/ramda/blob/master/source/pipe.js\n * @see https://github.com/darky/rocket-pipes/blob/master/index.ts\n *\n * @example Basic\n * ```ts\n * const add = (a, b) => a + b;\n * const add10 = x => add(x, 10);\n * const add100 = x => add(x, 100);\n * const sum = pipe(1, add10, add100);\n * sum === 111; // true\n * ```\n *\n * @example Building a Solana transaction message\n * ```ts\n * const transferTransactionMessage = pipe(\n *     // The result of the first expression...\n *     createTransactionMessage({ version: 0 }),\n *     // ...gets passed as the sole argument to the next function in the pipeline.\n *     tx => setTransactionMessageFeePayer(myAddress, tx),\n *     // The return value of that function gets passed to the next...\n *     tx => setTransactionMessageLifetimeUsingBlockhash(latestBlockhash, tx),\n *     // ...and so on.\n *     tx => appendTransactionMessageInstruction(createTransferInstruction(myAddress, toAddress, amountInLamports), tx),\n * );\n * ```\n *\n * @returns The initial value\n */\nexport function pipe<TInitial>(\n    /** The initial value */\n    init: TInitial,\n): TInitial;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n): R1;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n): R2;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n): R3;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n): R4;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n): R5;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5, R6>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n    /** The function with which to transform the return value of the prior function */\n    r5_r6: (r5: R5) => R6,\n): R6;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5, R6, R7>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n    /** The function with which to transform the return value of the prior function */\n    r5_r6: (r5: R5) => R6,\n    /** The function with which to transform the return value of the prior function */\n    r6_r7: (r6: R6) => R7,\n): R7;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5, R6, R7, R8>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n    /** The function with which to transform the return value of the prior function */\n    r5_r6: (r5: R5) => R6,\n    /** The function with which to transform the return value of the prior function */\n    r6_r7: (r6: R6) => R7,\n    /** The function with which to transform the return value of the prior function */\n    r7_r8: (r7: R7) => R8,\n): R8;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5, R6, R7, R8, R9>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n    /** The function with which to transform the return value of the prior function */\n    r5_r6: (r5: R5) => R6,\n    /** The function with which to transform the return value of the prior function */\n    r6_r7: (r6: R6) => R7,\n    /** The function with which to transform the return value of the prior function */\n    r7_r8: (r7: R7) => R8,\n    /** The function with which to transform the return value of the prior function */\n    r8_r9: (r8: R8) => R9,\n): R9;\n/**\n * @returns The return value of the final transform function\n */\nexport function pipe<TInitial, R1, R2, R3, R4, R5, R6, R7, R8, R9, R10>(\n    /** The initial value */\n    init: TInitial,\n    /** The function with which to transform the initial value */\n    init_r1: (init: TInitial) => R1,\n    /** The function with which to transform the return value of the prior function */\n    r1_r2: (r1: R1) => R2,\n    /** The function with which to transform the return value of the prior function */\n    r2_r3: (r2: R2) => R3,\n    /** The function with which to transform the return value of the prior function */\n    r3_r4: (r3: R3) => R4,\n    /** The function with which to transform the return value of the prior function */\n    r4_r5: (r4: R4) => R5,\n    /** The function with which to transform the return value of the prior function */\n    r5_r6: (r5: R5) => R6,\n    /** The function with which to transform the return value of the prior function */\n    r6_r7: (r6: R6) => R7,\n    /** The function with which to transform the return value of the prior function */\n    r7_r8: (r7: R7) => R8,\n    /** The function with which to transform the return value of the prior function */\n    r8_r9: (r8: R8) => R9,\n    /** The function with which to transform the return value of the prior function */\n    r9_r10: (r9: R9) => R10,\n): R10;\nexport function pipe<TInitial>(init: TInitial, ...fns: CallableFunction[]) {\n    return fns.reduce((acc, fn) => fn(acc), init);\n}\n"]}