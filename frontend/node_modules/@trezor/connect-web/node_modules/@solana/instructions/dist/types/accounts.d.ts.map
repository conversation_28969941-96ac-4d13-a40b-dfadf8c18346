{"version": 3, "file": "accounts.d.ts", "sourceRoot": "", "sources": ["../../src/accounts.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAE5C,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAEtC;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,WAAW,YAAY,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM;IAC1D,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG;IACrF,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAC;CACvC,CAAC;AACF;;GAEG;AACH,MAAM,MAAM,eAAe,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG;IAAE,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAA;CAAE,CAAC;AACxH;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG;IAC3F,IAAI,EAAE,WAAW,CAAC,eAAe,CAAC;CACrC,CAAC;AACF;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG;IAC3F,IAAI,EAAE,WAAW,CAAC,eAAe,CAAC;CACrC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,WAAW,kBAAkB,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,EAAE,mBAAmB,SAAS,MAAM,GAAG,MAAM;IAC7G,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC;IAC9B,QAAQ,CAAC,kBAAkB,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAC1D,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;CAC9D;AAED;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAC7B,QAAQ,SAAS,MAAM,GAAG,MAAM,EAChC,mBAAmB,SAAS,MAAM,GAAG,MAAM,IAC3C,kBAAkB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,GAAG;IAAE,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAA;CAAE,CAAC;AAChG;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAC7B,QAAQ,SAAS,MAAM,GAAG,MAAM,EAChC,mBAAmB,SAAS,MAAM,GAAG,MAAM,IAC3C,kBAAkB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,GAAG;IAAE,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAA;CAAE,CAAC"}