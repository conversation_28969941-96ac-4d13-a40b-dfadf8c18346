'use strict';

var errors = require('@solana/errors');

// src/instruction.ts
function isInstructionForProgram(instruction, programAddress) {
  return instruction.programAddress === programAddress;
}
function assertIsInstructionForProgram(instruction, programAddress) {
  if (instruction.programAddress !== programAddress) {
    throw new errors.SolanaError(errors.SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH, {
      actualProgramAddress: instruction.programAddress,
      expectedProgramAddress: programAddress
    });
  }
}
function isInstructionWithAccounts(instruction) {
  return instruction.accounts !== void 0;
}
function assertIsInstructionWithAccounts(instruction) {
  if (instruction.accounts === void 0) {
    throw new errors.SolanaError(errors.SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS, {
      data: instruction.data,
      programAddress: instruction.programAddress
    });
  }
}
function isInstructionWithData(instruction) {
  return instruction.data !== void 0;
}
function assertIsInstructionWithData(instruction) {
  if (instruction.data === void 0) {
    throw new errors.SolanaError(errors.SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA, {
      accountAddresses: instruction.accounts?.map((a) => a.address),
      programAddress: instruction.programAddress
    });
  }
}

// src/roles.ts
var AccountRole = /* @__PURE__ */ ((AccountRole2) => {
  AccountRole2[AccountRole2["WRITABLE_SIGNER"] = /* 3 */
  3] = "WRITABLE_SIGNER";
  AccountRole2[AccountRole2["READONLY_SIGNER"] = /* 2 */
  2] = "READONLY_SIGNER";
  AccountRole2[AccountRole2["WRITABLE"] = /* 1 */
  1] = "WRITABLE";
  AccountRole2[AccountRole2["READONLY"] = /* 0 */
  0] = "READONLY";
  return AccountRole2;
})(AccountRole || {});
var IS_SIGNER_BITMASK = 2;
var IS_WRITABLE_BITMASK = 1;
function downgradeRoleToNonSigner(role) {
  return role & -3;
}
function downgradeRoleToReadonly(role) {
  return role & -2;
}
function isSignerRole(role) {
  return role >= 2 /* READONLY_SIGNER */;
}
function isWritableRole(role) {
  return (role & IS_WRITABLE_BITMASK) !== 0;
}
function mergeRoles(roleA, roleB) {
  return roleA | roleB;
}
function upgradeRoleToSigner(role) {
  return role | IS_SIGNER_BITMASK;
}
function upgradeRoleToWritable(role) {
  return role | IS_WRITABLE_BITMASK;
}

exports.AccountRole = AccountRole;
exports.assertIsInstructionForProgram = assertIsInstructionForProgram;
exports.assertIsInstructionWithAccounts = assertIsInstructionWithAccounts;
exports.assertIsInstructionWithData = assertIsInstructionWithData;
exports.downgradeRoleToNonSigner = downgradeRoleToNonSigner;
exports.downgradeRoleToReadonly = downgradeRoleToReadonly;
exports.isInstructionForProgram = isInstructionForProgram;
exports.isInstructionWithAccounts = isInstructionWithAccounts;
exports.isInstructionWithData = isInstructionWithData;
exports.isSignerRole = isSignerRole;
exports.isWritableRole = isWritableRole;
exports.mergeRoles = mergeRoles;
exports.upgradeRoleToSigner = upgradeRoleToSigner;
exports.upgradeRoleToWritable = upgradeRoleToWritable;
//# sourceMappingURL=index.browser.cjs.map
//# sourceMappingURL=index.browser.cjs.map