{"version": 3, "sources": ["../../event-target-impl/src/index.browser.ts", "../src/async-iterable.ts", "../src/data-publisher.ts", "../src/demultiplex.ts"], "names": ["AbortController", "EventTarget", "SolanaError", "SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING", "SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE"], "mappings": ";;;;;;;AAAO,IAAMA,IAAkB,UAAW,CAAA,eAAA;AAAnC,IACMC,IAAc,UAAW,CAAA,WAAA;;;AC6DtC,IAAI,oBAAA;AACJ,SAAS,wBAA2B,GAAA;AAGhC,EAAO,OAAA,MAAA;AAAA,IACH,OAAA,CAAA,GAAA,CAAA,QAAA,KAAyB,eACnB,sGAEA,GAAA;AAAA,GACV;AACJ;AAEA,IAAM,gBAAgB,MAAO,EAAA;AA4CtB,SAAS,oCAA4C,CAAA;AAAA,EACxD,WAAA;AAAA,EACA,eAAA;AAAA,EACA,aAAA;AAAA,EACA;AACJ,CAAiC,EAAA;AAC7B,EAAM,MAAA,aAAA,uBAA4D,GAAI,EAAA;AACtE,EAAA,SAAS,2BAA2B,MAAiB,EAAA;AACjD,IAAA,KAAA,MAAW,CAAC,WAAa,EAAA,KAAK,CAAK,IAAA,aAAA,CAAc,SAAW,EAAA;AACxD,MAAA,IAAI,MAAM,WAAa,EAAA;AACnB,QAAA,aAAA,CAAc,OAAO,WAAW,CAAA;AAChC,QAAA,KAAA,CAAM,QAAQ,MAAM,CAAA;AAAA,OACjB,MAAA;AACH,QAAA,KAAA,CAAM,aAAa,IAAK,CAAA;AAAA,UACpB,MAAQ,EAAA,CAAA;AAAA,UACR,GAAK,EAAA;AAAA,SACR,CAAA;AAAA;AACL;AACJ;AAEJ,EAAM,MAAA,eAAA,GAAkB,IAAI,CAAgB,EAAA;AAC5C,EAAY,WAAA,CAAA,gBAAA,CAAiB,SAAS,MAAM;AACxC,IAAA,eAAA,CAAgB,KAAM,EAAA;AACtB,IAA4B,0BAAA,CAAA,oBAAA,KAAyB,0BAA2B,CAAA;AAAA,GACnF,CAAA;AACD,EAAA,MAAM,OAAU,GAAA,EAAE,MAAQ,EAAA,eAAA,CAAgB,MAAO,EAAA;AACjD,EAAA,IAAI,UAAsB,GAAA,aAAA;AAC1B,EAAc,aAAA,CAAA,EAAA;AAAA,IACV,gBAAA;AAAA,IACA,CAAO,GAAA,KAAA;AACH,MAAA,IAAI,eAAe,aAAe,EAAA;AAC9B,QAAa,UAAA,GAAA,GAAA;AACb,QAAA,eAAA,CAAgB,KAAM,EAAA;AACtB,QAAA,0BAAA,CAA2B,GAAG,CAAA;AAAA;AAClC,KACJ;AAAA,IACA;AAAA,GACJ;AACA,EAAc,aAAA,CAAA,EAAA;AAAA,IACV,eAAA;AAAA,IACA,CAAQ,IAAA,KAAA;AACJ,MAAc,aAAA,CAAA,OAAA,CAAQ,CAAC,KAAA,EAAO,WAAgB,KAAA;AAC1C,QAAA,IAAI,MAAM,WAAa,EAAA;AACnB,UAAM,MAAA,EAAE,QAAW,GAAA,KAAA;AACnB,UAAc,aAAA,CAAA,GAAA,CAAI,aAAa,EAAE,WAAA,EAAa,OAAO,YAAc,EAAA,IAAI,CAAA;AACvE,UAAA,MAAA,CAAO,IAAa,CAAA;AAAA,SACjB,MAAA;AACH,UAAA,KAAA,CAAM,aAAa,IAAK,CAAA;AAAA,YACpB,MAAQ,EAAA,CAAA;AAAA,YACR;AAAA,WACH,CAAA;AAAA;AACL,OACH,CAAA;AAAA,KACL;AAAA,IACA;AAAA,GACJ;AACA,EAAO,OAAA;AAAA,IACH,QAAQ,MAAO,CAAA,aAAa,CAAI,GAAA;AAC5B,MAAA,IAAI,YAAY,OAAS,EAAA;AACrB,QAAA;AAAA;AAEJ,MAAA,IAAI,eAAe,aAAe,EAAA;AAC9B,QAAM,MAAA,UAAA;AAAA;AAEV,MAAA,MAAM,cAAc,MAAO,EAAA;AAC3B,MAAc,aAAA,CAAA,GAAA,CAAI,aAAa,EAAE,WAAA,EAAa,OAAO,YAAc,EAAA,IAAI,CAAA;AACvE,MAAI,IAAA;AACA,QAAA,OAAO,IAAM,EAAA;AACT,UAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,GAAA,CAAI,WAAW,CAAA;AAC3C,UAAA,IAAI,CAAC,KAAO,EAAA;AAER,YAAM,MAAA,IAAIC,mBAAYC,6EAAsE,CAAA;AAAA;AAEhG,UAAA,IAAI,MAAM,WAAa,EAAA;AAEnB,YAAA,MAAM,IAAID,kBAAA;AAAA,cACNE;AAAA,aACJ;AAAA;AAEJ,UAAA,MAAM,eAAe,KAAM,CAAA,YAAA;AAC3B,UAAI,IAAA;AACA,YAAA,IAAI,aAAa,MAAQ,EAAA;AACrB,cAAA,KAAA,CAAM,eAAe,EAAC;AACtB,cAAA,KAAA,MAAW,QAAQ,YAAc,EAAA;AAC7B,gBAAI,IAAA,IAAA,CAAK,WAAW,CAAkB,aAAA;AAClC,kBAAA,MAAM,IAAK,CAAA,IAAA;AAAA,iBACR,MAAA;AACH,kBAAA,MAAM,IAAK,CAAA,GAAA;AAAA;AACf;AACJ,aACG,MAAA;AACH,cAAA,MAAM,MAAM,IAAI,OAAe,CAAA,CAAC,SAAS,MAAW,KAAA;AAChD,gBAAA,aAAA,CAAc,IAAI,WAAa,EAAA;AAAA,kBAC3B,WAAa,EAAA,IAAA;AAAA,kBACb,MAAQ,EAAA,OAAA;AAAA,kBACR,OAAS,EAAA;AAAA,iBACZ,CAAA;AAAA,eACJ,CAAA;AAAA;AACL,mBACK,CAAG,EAAA;AACR,YAAI,IAAA,CAAA,MAAO,oBAAyB,KAAA,wBAAA,EAA6B,CAAA,EAAA;AAC7D,cAAA;AAAA,aACG,MAAA;AACH,cAAM,MAAA,CAAA;AAAA;AACV;AACJ;AACJ,OACF,SAAA;AACE,QAAA,aAAA,CAAc,OAAO,WAAW,CAAA;AAAA;AACpC;AACJ,GACJ;AACJ;;;ACnLO,SAAS,iCACZ,YAGD,EAAA;AACC,EAAO,OAAA;AAAA,IACH,EAAA,CAAG,WAAa,EAAA,UAAA,EAAY,OAAS,EAAA;AACjC,MAAA,SAAS,cAAc,EAAW,EAAA;AAC9B,QAAA,IAAI,cAAc,WAAa,EAAA;AAC3B,UAAA,MAAM,OAAQ,EAAkD,CAAA,MAAA;AAChE,UAAC,WAAwE,IAAI,CAAA;AAAA,SAC1E,MAAA;AACH,UAAC,UAA0B,EAAA;AAAA;AAC/B;AAEJ,MAAa,YAAA,CAAA,gBAAA,CAAiB,WAAa,EAAA,aAAA,EAAe,OAAO,CAAA;AACjE,MAAA,OAAO,MAAM;AACT,QAAa,YAAA,CAAA,mBAAA,CAAoB,aAAa,aAAa,CAAA;AAAA,OAC/D;AAAA;AACJ,GACJ;AACJ;;;ACrCO,SAAS,wBAAA,CAIZ,SACA,EAAA,iBAAA,EACA,kBAKa,EAAA;AACb,EAAI,IAAA,mBAAA;AAMJ,EAAM,MAAA,WAAA,GAAc,IAAI,CAAY,EAAA;AACpC,EAAM,MAAA,0BAAA,GAA6B,iCAAiC,WAAW,CAAA;AAC/E,EAAO,OAAA;AAAA,IACH,GAAG,0BAAA;AAAA,IACH,EAAA,CAAG,WAAa,EAAA,UAAA,EAAY,OAAS,EAAA;AACjC,MAAA,IAAI,CAAC,mBAAqB,EAAA;AACtB,QAAA,MAAM,yBAA4B,GAAA,SAAA,CAAU,EAAG,CAAA,iBAAA,EAAmB,CAAiB,aAAA,KAAA;AAC/E,UAAM,MAAA,eAAA,GAAkB,mBAAmB,aAAa,CAAA;AACxD,UAAA,IAAI,CAAC,eAAiB,EAAA;AAClB,YAAA;AAAA;AAEJ,UAAM,MAAA,CAAC,sBAAwB,EAAA,OAAO,CAAI,GAAA,eAAA;AAC1C,UAAY,WAAA,CAAA,aAAA;AAAA,YACR,IAAI,YAAY,sBAAwB,EAAA;AAAA,cACpC,MAAQ,EAAA;AAAA,aACX;AAAA,WACL;AAAA,SACH,CAAA;AACD,QAAsB,mBAAA,GAAA;AAAA,UAClB,OAAS,EAAA,yBAAA;AAAA,UACT,cAAgB,EAAA;AAAA,SACpB;AAAA;AAEJ,MAAoB,mBAAA,CAAA,cAAA,EAAA;AACpB,MAAA,MAAM,WAAc,GAAA,0BAAA,CAA2B,EAAG,CAAA,WAAA,EAAa,YAAY,OAAO,CAAA;AAClF,MAAA,IAAI,QAAW,GAAA,IAAA;AACf,MAAA,SAAS,iBAAoB,GAAA;AACzB,QAAA,IAAI,CAAC,QAAU,EAAA;AACX,UAAA;AAAA;AAEJ,QAAW,QAAA,GAAA,KAAA;AACX,QAAS,OAAA,EAAA,MAAA,CAAO,mBAAoB,CAAA,OAAA,EAAS,iBAAiB,CAAA;AAC9D,QAAqB,mBAAA,CAAA,cAAA,EAAA;AACrB,QAAI,IAAA,mBAAA,CAAqB,mBAAmB,CAAG,EAAA;AAC3C,UAAA,mBAAA,CAAqB,OAAQ,EAAA;AAC7B,UAAsB,mBAAA,GAAA,MAAA;AAAA;AAE1B,QAAY,WAAA,EAAA;AAAA;AAEhB,MAAS,OAAA,EAAA,MAAA,CAAO,gBAAiB,CAAA,OAAA,EAAS,iBAAiB,CAAA;AAC3D,MAAO,OAAA,iBAAA;AAAA;AACX,GACJ;AACJ", "file": "index.browser.cjs", "sourcesContent": ["export const AbortController = globalThis.AbortController;\nexport const EventTarget = globalThis.EventTarget;\n", "import {\n    SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE,\n    SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING,\n    SolanaError,\n} from '@solana/errors';\nimport { AbortController } from '@solana/event-target-impl';\n\nimport { DataPublisher } from './data-publisher';\n\ntype Config = Readonly<{\n    /**\n     * Triggering this abort signal will cause all iterators spawned from this iterator to return\n     * once they have published all queued messages.\n     */\n    abortSignal: AbortSignal;\n    /**\n     * Messages from this channel of `dataPublisher` will be the ones yielded through the iterators.\n     *\n     * Messages only begin to be queued after the first time an iterator begins to poll. Channel\n     * messages published before that time will be dropped.\n     */\n    dataChannelName: string;\n    // FIXME: It would be nice to be able to constrain the type of `dataPublisher` to one that\n    //        definitely supports the `dataChannelName` and `errorChannelName` channels, and\n    //        furthermore publishes `TData` on the `dataChannelName` channel. This is more difficult\n    //        than it should be: https://tsplay.dev/NlZelW\n    dataPublisher: DataPublisher;\n    /**\n     * Messages from this channel of `dataPublisher` will be the ones thrown through the iterators.\n     *\n     * Any new iterators created after the first error is encountered will reject with that error\n     * when polled.\n     */\n    errorChannelName: string;\n}>;\n\nconst enum PublishType {\n    DATA,\n    ERROR,\n}\n\ntype IteratorKey = symbol;\ntype IteratorState<TData> =\n    | {\n          __hasPolled: false;\n          publishQueue: (\n              | {\n                    __type: PublishType.DATA;\n                    data: TData;\n                }\n              | {\n                    __type: PublishType.ERROR;\n                    err: unknown;\n                }\n          )[];\n      }\n    | {\n          __hasPolled: true;\n          onData: (data: TData) => void;\n          onError: Parameters<ConstructorParameters<typeof Promise>[0]>[1];\n      };\n\nlet EXPLICIT_ABORT_TOKEN: symbol;\nfunction createExplicitAbortToken() {\n    // This function is an annoying workaround to prevent `process.env.NODE_ENV` from appearing at\n    // the top level of this module and thwarting an optimizing compiler's attempt to tree-shake.\n    return Symbol(\n        process.env.NODE_ENV !== \"production\"\n            ? \"This symbol is thrown from a socket's iterator when the connection is explicitly \" +\n                  'aborted by the user'\n            : undefined,\n    );\n}\n\nconst UNINITIALIZED = Symbol();\n\n/**\n * Returns an `AsyncIterable` given a data publisher.\n *\n * The iterable will produce iterators that vend messages published to `dataChannelName` and will\n * throw the first time a message is published to `errorChannelName`. Triggering the abort signal\n * will cause all iterators spawned from this iterator to return once they have published all queued\n * messages.\n *\n * Things to note:\n *\n * - If a message is published over a channel before the `AsyncIterator` attached to it has polled\n *   for the next result, the message will be queued in memory.\n * - Messages only begin to be queued after the first time an iterator begins to poll. Channel\n *   messages published before that time will be dropped.\n * - If there are messages in the queue and an error occurs, all queued messages will be vended to\n *   the iterator before the error is thrown.\n * - If there are messages in the queue and the abort signal fires, all queued messages will be\n *   vended to the iterator after which it will return.\n * - Any new iterators created after the first error is encountered will reject with that error when\n *   polled.\n *\n * @param config\n *\n * @example\n * ```ts\n * const iterable = createAsyncIterableFromDataPublisher({\n *     abortSignal: AbortSignal.timeout(10_000),\n *     dataChannelName: 'message',\n *     dataPublisher,\n *     errorChannelName: 'error',\n * });\n * try {\n *     for await (const message of iterable) {\n *         console.log('Got message', message);\n *     }\n * } catch (e) {\n *     console.error('An error was published to the error channel', e);\n * } finally {\n *     console.log(\"It's been 10 seconds; that's enough for now.\");\n * }\n * ```\n */\nexport function createAsyncIterableFromDataPublisher<TData>({\n    abortSignal,\n    dataChannelName,\n    dataPublisher,\n    errorChannelName,\n}: Config): AsyncIterable<TData> {\n    const iteratorState: Map<IteratorKey, IteratorState<TData>> = new Map();\n    function publishErrorToAllIterators(reason: unknown) {\n        for (const [iteratorKey, state] of iteratorState.entries()) {\n            if (state.__hasPolled) {\n                iteratorState.delete(iteratorKey);\n                state.onError(reason);\n            } else {\n                state.publishQueue.push({\n                    __type: PublishType.ERROR,\n                    err: reason,\n                });\n            }\n        }\n    }\n    const abortController = new AbortController();\n    abortSignal.addEventListener('abort', () => {\n        abortController.abort();\n        publishErrorToAllIterators((EXPLICIT_ABORT_TOKEN ||= createExplicitAbortToken()));\n    });\n    const options = { signal: abortController.signal } as const;\n    let firstError: unknown = UNINITIALIZED;\n    dataPublisher.on(\n        errorChannelName,\n        err => {\n            if (firstError === UNINITIALIZED) {\n                firstError = err;\n                abortController.abort();\n                publishErrorToAllIterators(err);\n            }\n        },\n        options,\n    );\n    dataPublisher.on(\n        dataChannelName,\n        data => {\n            iteratorState.forEach((state, iteratorKey) => {\n                if (state.__hasPolled) {\n                    const { onData } = state;\n                    iteratorState.set(iteratorKey, { __hasPolled: false, publishQueue: [] });\n                    onData(data as TData);\n                } else {\n                    state.publishQueue.push({\n                        __type: PublishType.DATA,\n                        data: data as TData,\n                    });\n                }\n            });\n        },\n        options,\n    );\n    return {\n        async *[Symbol.asyncIterator]() {\n            if (abortSignal.aborted) {\n                return;\n            }\n            if (firstError !== UNINITIALIZED) {\n                throw firstError;\n            }\n            const iteratorKey = Symbol();\n            iteratorState.set(iteratorKey, { __hasPolled: false, publishQueue: [] });\n            try {\n                while (true) {\n                    const state = iteratorState.get(iteratorKey);\n                    if (!state) {\n                        // There should always be state by now.\n                        throw new SolanaError(SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING);\n                    }\n                    if (state.__hasPolled) {\n                        // You should never be able to poll twice in a row.\n                        throw new SolanaError(\n                            SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE,\n                        );\n                    }\n                    const publishQueue = state.publishQueue;\n                    try {\n                        if (publishQueue.length) {\n                            state.publishQueue = [];\n                            for (const item of publishQueue) {\n                                if (item.__type === PublishType.DATA) {\n                                    yield item.data;\n                                } else {\n                                    throw item.err;\n                                }\n                            }\n                        } else {\n                            yield await new Promise<TData>((resolve, reject) => {\n                                iteratorState.set(iteratorKey, {\n                                    __hasPolled: true,\n                                    onData: resolve,\n                                    onError: reject,\n                                });\n                            });\n                        }\n                    } catch (e) {\n                        if (e === (EXPLICIT_ABORT_TOKEN ||= createExplicitAbortToken())) {\n                            return;\n                        } else {\n                            throw e;\n                        }\n                    }\n                }\n            } finally {\n                iteratorState.delete(iteratorKey);\n            }\n        },\n    };\n}\n", "import { TypedEventEmitter, TypedEventTarget } from './event-emitter';\n\ntype UnsubscribeFn = () => void;\n\n/**\n * Represents an object with an `on` function that you can call to subscribe to certain data over a\n * named channel.\n *\n * @example\n * ```ts\n * let dataPublisher: DataPublisher<{ error: SolanaError }>;\n * dataPublisher.on('data', handleData); // ERROR. `data` is not a known channel name.\n * dataPublisher.on('error', e => {\n *     console.error(e);\n * }); // OK.\n * ```\n */\nexport interface DataPublisher<TDataByChannelName extends Record<string, unknown> = Record<string, unknown>> {\n    /**\n     * Call this to subscribe to data over a named channel.\n     *\n     * @param channelName The name of the channel on which to subscribe for messages\n     * @param subscriber The function to call when a message becomes available\n     * @param options.signal An abort signal you can fire to unsubscribe\n     *\n     * @returns A function that you can call to unsubscribe\n     */\n    on<const TChannelName extends keyof TDataByChannelName>(\n        channelName: TChannelName,\n        subscriber: (data: TDataByChannelName[TChannelName]) => void,\n        options?: { signal: AbortSignal },\n    ): UnsubscribeFn;\n}\n\n/**\n * Returns an object with an `on` function that you can call to subscribe to certain data over a\n * named channel.\n *\n * The `on` function returns an unsubscribe function.\n *\n * @example\n * ```ts\n * const socketDataPublisher = getDataPublisherFromEventEmitter(new WebSocket('wss://api.devnet.solana.com'));\n * const unsubscribe = socketDataPublisher.on('message', message => {\n *     if (JSON.parse(message.data).id === 42) {\n *         console.log('Got response 42');\n *         unsubscribe();\n *     }\n * });\n * ```\n */\nexport function getDataPublisherFromEventEmitter<TEventMap extends Record<string, Event>>(\n    eventEmitter: TypedEventEmitter<TEventMap> | TypedEventTarget<TEventMap>,\n): DataPublisher<{\n    [TEventType in keyof TEventMap]: TEventMap[TEventType] extends CustomEvent ? TEventMap[TEventType]['detail'] : null;\n}> {\n    return {\n        on(channelName, subscriber, options) {\n            function innerListener(ev: Event) {\n                if (ev instanceof CustomEvent) {\n                    const data = (ev as CustomEvent<TEventMap[typeof channelName]>).detail;\n                    (subscriber as unknown as (data: TEventMap[typeof channelName]) => void)(data);\n                } else {\n                    (subscriber as () => void)();\n                }\n            }\n            eventEmitter.addEventListener(channelName, innerListener, options);\n            return () => {\n                eventEmitter.removeEventListener(channelName, innerListener);\n            };\n        },\n    };\n}\n", "import { EventTarget } from '@solana/event-target-impl';\n\nimport { DataPublisher, getDataPublisherFromEventEmitter } from './data-publisher';\n\n/**\n * Given a channel that carries messages for multiple subscribers on a single channel name, this\n * function returns a new {@link DataPublisher} that splits them into multiple channel names.\n *\n * @param messageTransformer A function that receives the message as the first argument, and returns\n * a tuple of the derived channel name and the message.\n *\n * @example\n * Imagine a channel that carries multiple notifications whose destination is contained within the\n * message itself.\n *\n * ```ts\n * const demuxedDataPublisher = demultiplexDataPublisher(channel, 'message', message => {\n *     const destinationChannelName = `notification-for:${message.subscriberId}`;\n *     return [destinationChannelName, message];\n * });\n * ```\n *\n * Now you can subscribe to _only_ the messages you are interested in, without having to subscribe\n * to the entire `'message'` channel and filter out the messages that are not for you.\n *\n * ```ts\n * demuxedDataPublisher.on(\n *     'notification-for:123',\n *     message => {\n *         console.log('Got a message for subscriber 123', message);\n *     },\n *     { signal: AbortSignal.timeout(5_000) },\n * );\n * ```\n */\nexport function demultiplexDataPublisher<\n    TDataPublisher extends DataPublisher,\n    const TChannelName extends Parameters<TDataPublisher['on']>[0],\n>(\n    publisher: TDataPublisher,\n    sourceChannelName: TChannelName,\n    messageTransformer: (\n        // FIXME: Deriving the type of the message from `TDataPublisher` and `TChannelName` would\n        //        help callers to constrain their transform functions.\n        message: unknown,\n    ) => [destinationChannelName: string, message: unknown] | void,\n): DataPublisher {\n    let innerPublisherState:\n        | {\n              readonly dispose: () => void;\n              numSubscribers: number;\n          }\n        | undefined;\n    const eventTarget = new EventTarget();\n    const demultiplexedDataPublisher = getDataPublisherFromEventEmitter(eventTarget);\n    return {\n        ...demultiplexedDataPublisher,\n        on(channelName, subscriber, options) {\n            if (!innerPublisherState) {\n                const innerPublisherUnsubscribe = publisher.on(sourceChannelName, sourceMessage => {\n                    const transformResult = messageTransformer(sourceMessage);\n                    if (!transformResult) {\n                        return;\n                    }\n                    const [destinationChannelName, message] = transformResult;\n                    eventTarget.dispatchEvent(\n                        new CustomEvent(destinationChannelName, {\n                            detail: message,\n                        }),\n                    );\n                });\n                innerPublisherState = {\n                    dispose: innerPublisherUnsubscribe,\n                    numSubscribers: 0,\n                };\n            }\n            innerPublisherState.numSubscribers++;\n            const unsubscribe = demultiplexedDataPublisher.on(channelName, subscriber, options);\n            let isActive = true;\n            function handleUnsubscribe() {\n                if (!isActive) {\n                    return;\n                }\n                isActive = false;\n                options?.signal.removeEventListener('abort', handleUnsubscribe);\n                innerPublisherState!.numSubscribers--;\n                if (innerPublisherState!.numSubscribers === 0) {\n                    innerPublisherState!.dispose();\n                    innerPublisherState = undefined;\n                }\n                unsubscribe();\n            }\n            options?.signal.addEventListener('abort', handleUnsubscribe);\n            return handleUnsubscribe;\n        },\n    };\n}\n"]}