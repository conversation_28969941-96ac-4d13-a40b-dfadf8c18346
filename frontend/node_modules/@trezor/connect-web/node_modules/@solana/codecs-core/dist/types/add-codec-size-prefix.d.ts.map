{"version": 3, "file": "add-codec-size-prefix.d.ts", "sourceRoot": "", "sources": ["../../src/add-codec-size-prefix.ts"], "names": [], "mappings": "AACA,OAAO,EACH,KAAK,EAGL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,SAAS,CAAC;AAGjB,KAAK,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAChE,KAAK,sBAAsB,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM,IACnD,gBAAgB,CAAC,MAAM,GAAG,MAAM,EAAE,KAAK,CAAC,GACxC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACtC,KAAK,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACvD,KAAK,sBAAsB,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM,IACnD,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,GAC/B,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACtC,KAAK,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAClE,KAAK,oBAAoB,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM,IACjD,cAAc,CAAC,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,GAC9C,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAE5C;;;;;;;;GAQG;AACH,wBAAgB,oBAAoB,CAAC,KAAK,EACtC,OAAO,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAChC,MAAM,EAAE,sBAAsB,GAC/B,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC3B,wBAAgB,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,aAAa,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AA8BxH;;;;;;;;GAQG;AACH,wBAAgB,oBAAoB,CAAC,GAAG,EACpC,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC9B,MAAM,EAAE,sBAAsB,GAC/B,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACzB,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,aAAa,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;AA0BlH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EACvD,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EACjC,MAAM,EAAE,oBAAoB,GAC7B,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9B,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EACvD,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EACxB,MAAM,EAAE,WAAW,GACpB,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC"}