{"version": 3, "file": "getBlock.d.ts", "sourceRoot": "", "sources": ["../../src/getBlock.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACR,kBAAkB,EAClB,SAAS,EACT,UAAU,EACV,MAAM,EACN,IAAI,EACJ,sBAAsB,EACtB,wBAAwB,EACxB,wBAAwB,EACxB,sBAAsB,EACtB,4BAA4B,EAC5B,aAAa,EAChB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAIvE,KAAK,uBAAuB,GAAG,QAAQ,CAAC;IACpC,8CAA8C;IAC9C,WAAW,EAAE,MAAM,CAAC;IACpB,mDAAmD;IACnD,SAAS,EAAE,aAAa,CAAC;IACzB,kCAAkC;IAClC,SAAS,EAAE,SAAS,CAAC;IACrB,4CAA4C;IAC5C,UAAU,EAAE,IAAI,CAAC;IACjB,2CAA2C;IAC3C,iBAAiB,EAAE,SAAS,CAAC;CAChC,CAAC,CAAC;AAEH,KAAK,8BAA8B,GAAG,QAAQ,CAAC;IAC3C,0BAA0B;IAC1B,OAAO,EAAE,SAAS,MAAM,EAAE,CAAC;CAC9B,CAAC,CAAC;AAEH,KAAK,iCAAiC,GAAG,QAAQ,CAAC;IAC9C,+DAA+D;IAC/D,UAAU,EAAE,SAAS,kBAAkB,EAAE,CAAC;CAC7C,CAAC,CAAC;AAEH,KAAK,mCAAmC,CAAC,YAAY,IAAI,QAAQ,CAAC;IAC9D,YAAY,EAAE,SAAS,YAAY,EAAE,CAAC;CACzC,CAAC,CAAC;AAIH,KAAK,oBAAoB,GAAG,QAAQ,CAAC;IACjC;;;;;;;OAOG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAC3C;;;;;;;;;;;;;;;;;OAiBG;IACH,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B;;;;;;;;;;OAUG;IACH,8BAA8B,CAAC,EAAE,sCAAsC,CAAC;IACxE;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;;;;;OAUG;IACH,kBAAkB,CAAC,EAAE,8BAA8B,CAAC;CACvD,CAAC,CAAC;AAEH,KAAK,gBAAgB,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,YAAY,CAAC;AACpE,KAAK,8BAA8B,GAAG,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,YAAY,CAAC;AAElF,KAAK,sCAAsC,GAAG,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;AAEpF,MAAM,MAAM,WAAW,GAAG;IACtB;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC,GACP,uBAAuB,GAAG,IAAI,CAAC;IAClC;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC,GACP,CAAC,uBAAuB,GAAG,8BAA8B,CAAC,GAAG,IAAI,CAAC;IACrE;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,EAAE,YAAY,CAAC;KACpC,CAAC,GACP,CAAC,uBAAuB,GAAG,iCAAiC,CAAC,GAAG,IAAI,CAAC;IACxE;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,EAAE,YAAY,CAAC;KACpC,CAAC,GACP,CAAC,uBAAuB,GAAG,8BAA8B,GAAG,iCAAiC,CAAC,GAAG,IAAI,CAAC;IACzG;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GAEJ,CAAC,uBAAuB,GACpB,mCAAmC,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GACxG,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GACP,CAAC,uBAAuB,GAAG,mCAAmC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACxG;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GACxG,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtE,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,mCAAmC,CAAC,wBAAwB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC1G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,CAAC,uBAAuB,GAAG,mCAAmC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC1G;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,wBAAwB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC1G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxE,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,mCAAmC,CAAC,wBAAwB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC1G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,CAAC,uBAAuB,GAAG,mCAAmC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC1G;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,wBAAwB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC1G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxE,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,mCAAmC,CAAC,4BAA4B,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC9G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,CAAC,uBAAuB,GAAG,mCAAmC,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC9G;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,4BAA4B,CAAC,sCAAsC,CAAC,CAAC,CAAC,GAC9G,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,GAC5E,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,mCAAmC,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GACxG,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GAChE,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,KAAK,CAAC;QACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,CAAC,uBAAuB,GAAG,mCAAmC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACxG;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,EAAE,oBAAoB,GACxB,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,8BAA8B,EAAE,sCAAsC,CAAC;QACvE,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC,CAAC,GACxG,IAAI,CAAC;IACX;;;;;OAKG;IACH,QAAQ,CACJ,IAAI,EAAE,IAAI,EAEV,MAAM,CAAC,EAAE,IAAI,CAAC,oBAAoB,EAAE,gCAAgC,CAAC,GACjE,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GAEJ,CAAC,uBAAuB,GACpB,8BAA8B,GAC9B,mCAAmC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtE,IAAI,CAAC;CACd,CAAC"}