{"version": 3, "sources": ["../src/algorithm.ts", "../src/private-key.ts", "../src/public-key.ts", "../src/signatures.ts", "../src/key-pair.ts"], "names": ["SolanaError", "SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH", "assertKeyExporterIsAvailable", "SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY", "getBase58Encoder", "SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE", "SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH", "assertSigningCapabilityIsAvailable", "signature", "assertVerificationCapabilityIsAvailable", "assertKeyGenerationIsAvailable", "assertPRNGIsAvailable", "SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH", "SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY", "privateKey"], "mappings": ";;;;;;;;;AAAO,IAAM,4BAAA;AAAA;AAAA;AAAA,EAGT,MAAO,CAAA,MAAA,CAAO,EAAE,IAAA,EAAM,WAAW;AAAA,CAAA;ACErC,SAAS,eAAe,KAA+C,EAAA;AAEnE,EAAA,OAAO,IAAI,UAAW,CAAA;AAAA;AAAA;AAAA;AAAA,IAIlB,EAAA;AAAA;AAAA,IACA,EAAA;AAAA;AAAA,IAEI,CAAA;AAAA;AAAA,IACA,CAAA;AAAA;AAAA,IACI,CAAA;AAAA;AAAA,IAEJ,EAAA;AAAA;AAAA,IACA,CAAA;AAAA;AAAA,IACI,CAAA;AAAA;AAAA,IACA,CAAA;AAAA;AAAA;AAAA,IAEQ,EAAA;AAAA;AAAA,IACA,GAAA;AAAA;AAAA;AAAA,IAEA,GAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKhB,CAAA;AAAA;AAAA,IACA,EAAA;AAAA;AAAA;AAAA,IAGI,CAAA;AAAA;AAAA,IACA,EAAA;AAAA;AAAA,IAEJ,GAAG;AAAA,GACN,CAAA;AACL;AAoBA,eAAsB,yBAAA,CAClB,KACA,EAAA,WAAA,GAAuB,KACL,EAAA;AAClB,EAAA,MAAM,eAAe,KAAM,CAAA,UAAA;AAC3B,EAAA,IAAI,iBAAiB,EAAI,EAAA;AACrB,IAAM,MAAA,IAAIA,mBAAYC,0DAAqD,EAAA;AAAA,MACvE;AAAA,KACH,CAAA;AAAA;AAEL,EAAM,MAAA,oBAAA,GAAuB,eAAe,KAAK,CAAA;AACjD,EAAA,OAAO,MAAM,MAAO,CAAA,MAAA,CAAO,UAAU,OAAS,EAAA,oBAAA,EAAsB,8BAA8B,WAAa,EAAA;AAAA,IAC3G;AAAA,GACH,CAAA;AACL;ACpDA,eAAsB,0BAAA,CAClB,UACA,EAAA,WAAA,GAAuB,KACL,EAAA;AAClB,EAA6BC,uCAAA,EAAA;AAE7B,EAAI,IAAA,UAAA,CAAW,gBAAgB,KAAO,EAAA;AAClC,IAAA,MAAM,IAAIF,kBAAY,CAAAG,qEAAA,EAAgE,EAAE,GAAA,EAAK,YAAY,CAAA;AAAA;AAI7G,EAAA,MAAM,MAAM,MAAM,MAAA,CAAO,MAAO,CAAA,SAAA,CAAU,OAAO,UAAU,CAAA;AAG3D,EAAO,OAAA,MAAM,OAAO,MAAO,CAAA,SAAA;AAAA,IACvB,KAAA;AAAA,IACA;AAAA,MACI,GAAiB,EAAA,SAAA;AAAA,MACjB,GAAuB,EAAA,WAAA;AAAA,MACvB,OAAA,EAA8B,CAAC,QAAQ,CAAA;AAAA,MACvC,GAAoB,EAAA,KAAA;AAAA,MACpB,GAAiC,GAAI,CAAA;AAAA,KACzC;AAAA,IACA,SAAA;AAAA,IACA,WAAA;AAAA,IACA,CAAC,QAAQ;AAAA,GACb;AACJ;ACxBA,IAAI,aAAA;AA8BG,SAAS,kBAAkB,iBAAmE,EAAA;AACjG,EAAI,IAAA,CAAC,aAAe,EAAA,aAAA,GAAgBC,8BAAiB,EAAA;AAErD,EAAA;AAAA;AAAA,IAEI,kBAAkB,MAAS,GAAA,EAAA;AAAA,IAE3B,kBAAkB,MAAS,GAAA;AAAA,IAC7B;AACE,IAAM,MAAA,IAAIJ,mBAAYK,+DAA0D,EAAA;AAAA,MAC5E,cAAc,iBAAkB,CAAA;AAAA,KACnC,CAAA;AAAA;AAGL,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,MAAA,CAAO,iBAAiB,CAAA;AACpD,EAAA,MAAM,WAAW,KAAM,CAAA,UAAA;AACvB,EAAA,IAAI,aAAa,EAAI,EAAA;AACjB,IAAM,MAAA,IAAIL,mBAAYM,wDAAmD,EAAA;AAAA,MACrE,YAAc,EAAA;AAAA,KACjB,CAAA;AAAA;AAET;AAsBO,SAAS,YAAY,iBAA2D,EAAA;AACnF,EAAI,IAAA,CAAC,aAAe,EAAA,aAAA,GAAgBF,8BAAiB,EAAA;AAGrD,EAAA;AAAA;AAAA,IAEI,kBAAkB,MAAS,GAAA,EAAA;AAAA,IAE3B,kBAAkB,MAAS,GAAA;AAAA,IAC7B;AACE,IAAO,OAAA,KAAA;AAAA;AAGX,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,MAAA,CAAO,iBAAiB,CAAA;AACpD,EAAA,MAAM,WAAW,KAAM,CAAA,UAAA;AACvB,EAAA,IAAI,aAAa,EAAI,EAAA;AACjB,IAAO,OAAA,KAAA;AAAA;AAEX,EAAO,OAAA,IAAA;AACX;AAeA,eAAsB,SAAA,CAAU,KAAgB,IAAmD,EAAA;AAC/F,EAAmCG,6CAAA,EAAA;AACnC,EAAA,MAAM,aAAa,MAAM,MAAA,CAAO,OAAO,IAAK,CAAA,4BAAA,EAA8B,KAAK,IAAI,CAAA;AACnF,EAAO,OAAA,IAAI,WAAW,UAAU,CAAA;AACpC;AAgBO,SAAS,UAAU,iBAAsC,EAAA;AAC5D,EAAA,iBAAA,CAAkB,iBAAiB,CAAA;AACnC,EAAO,OAAA,iBAAA;AACX;AAkBA,eAAsB,eAAA,CAClB,GACAC,EAAAA,UAAAA,EACA,IACgB,EAAA;AAChB,EAAwCC,kDAAA,EAAA;AACxC,EAAA,OAAO,MAAM,MAAO,CAAA,MAAA,CAAO,OAAO,4BAA8B,EAAA,GAAA,EAAKD,YAAW,IAAI,CAAA;AACxF;;;AC5JA,eAAsB,eAA0C,GAAA;AAC5D,EAAA,MAAME,yCAA+B,EAAA;AACrC,EAAM,MAAA,OAAA,GAAU,MAAM,MAAA,CAAO,MAAO,CAAA,WAAA;AAAA;AAAA,IAChB,4BAAA;AAAA;AAAA;AAAA,IACE,KAAA;AAAA;AAAA;AAAA,IACC,CAAC,QAAQ,QAAQ;AAAA,GACxC;AACA,EAAO,OAAA,OAAA;AACX;AA0BA,eAAsB,sBAAA,CAClB,KACA,EAAA,WAAA,GAAuB,KACD,EAAA;AACtB,EAAsBC,gCAAA,EAAA;AAEtB,EAAI,IAAA,KAAA,CAAM,eAAe,EAAI,EAAA;AACzB,IAAA,MAAM,IAAIX,kBAAY,CAAAY,uDAAA,EAAkD,EAAE,UAAY,EAAA,KAAA,CAAM,YAAY,CAAA;AAAA;AAE5G,EAAA,MAAM,CAAC,SAAW,EAAA,UAAU,CAAI,GAAA,MAAM,QAAQ,GAAI,CAAA;AAAA,IAC9C,OAAO,MAAO,CAAA,SAAA;AAAA,MAAU,KAAA;AAAA,MAAO,KAAA,CAAM,MAAM,EAAE,CAAA;AAAA,MAAG,4BAAA;AAAA;AAAA,MAAgD,IAAA;AAAA,MAAM;AAAA,QAClG;AAAA;AACJ,KAAC;AAAA,IACD,0BAA0B,KAAM,CAAA,KAAA,CAAM,CAAG,EAAA,EAAE,GAAG,WAAW;AAAA,GAC5D,CAAA;AAGD,EAAM,MAAA,WAAA,GAAc,IAAI,UAAA,CAAW,EAAE,CAAA;AACrC,EAAA,MAAA,CAAO,gBAAgB,WAAW,CAAA;AAClC,EAAA,MAAM,UAAa,GAAA,MAAM,SAAU,CAAA,UAAA,EAAY,WAAW,CAAA;AAC1D,EAAA,MAAM,OAAU,GAAA,MAAM,eAAgB,CAAA,SAAA,EAAW,YAAY,WAAW,CAAA;AACxE,EAAA,IAAI,CAAC,OAAS,EAAA;AACV,IAAM,MAAA,IAAIZ,mBAAYa,4DAAqD,CAAA;AAAA;AAG/E,EAAO,OAAA,EAAE,YAAY,SAAU,EAAA;AACnC;AAiCA,eAAsB,gCAAA,CAClB,KACA,EAAA,WAAA,GAAuB,KACD,EAAA;AACtB,EAAM,MAAA,iBAAA,GAAoB,yBAA0B,CAAA,KAAA,EAAO,WAAW,CAAA;AAOtE,EAAA,MAAM,CAAC,SAAW,EAAA,UAAU,CAAI,GAAA,MAAM,QAAQ,GAAI,CAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CAI7C,cAAc,iBAAoB,GAAA,yBAAA;AAAA,MAA0B,KAAA;AAAA,MAAO;AAAA;AAAA,KAAyB,EAAA,IAAA;AAAA,MACzF,OAAMC,gBAAc,MAAM,0BAAA;AAAA,QAA2BA,WAAAA;AAAA,QAAY;AAAA;AAAA;AAAsB,KAC3F;AAAA,IACA;AAAA,GACH,CAAA;AAED,EAAO,OAAA,EAAE,YAAY,SAAU,EAAA;AACnC", "file": "index.browser.cjs", "sourcesContent": ["export const ED25519_ALGORITHM_IDENTIFIER =\n    // Resist the temptation to convert this to a simple string; As of version 133.0.3, Firefox\n    // requires the object form of `AlgorithmIdentifier` and will throw a `DOMException` otherwise.\n    Object.freeze({ name: 'Ed25519' });\n", "import { ReadonlyUint8Array } from '@solana/codecs-core';\nimport { SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH, SolanaError } from '@solana/errors';\n\nimport { ED25519_ALGORITHM_IDENTIFIER } from './algorithm';\n\nfunction addPkcs8Header(bytes: ReadonlyUint8Array): ReadonlyUint8Array {\n    // prettier-ignore\n    return new Uint8Array([\n        /**\n         * PKCS#8 header\n         */\n        0x30, // ASN.1 sequence tag\n        0x2e, // Length of sequence (46 more bytes)\n\n            0x02, // ASN.1 integer tag\n            0x01, // Length of integer\n                0x00, // Version number\n\n            0x30, // ASN.1 sequence tag\n            0x05, // Length of sequence\n                0x06, // ASN.1 object identifier tag\n                0x03, // Length of object identifier\n                    // Edwards curve algorithms identifier https://oid-rep.orange-labs.fr/get/***********\n                        0x2b, // iso(1) / identified-organization(3) (The first node is multiplied by the decimal 40 and the result is added to the value of the second node)\n                        0x65, // thawte(101)\n                    // Ed25519 identifier\n                        0x70, // id-Ed25519(112)\n\n        /**\n         * Private key payload\n         */\n        0x04, // ASN.1 octet string tag\n        0x22, // String length (34 more bytes)\n\n            // Private key bytes as octet string\n            0x04, // ASN.1 octet string tag\n            0x20, // String length (32 bytes)\n\n        ...bytes\n    ]);\n}\n\n/**\n * Given a private key represented as a 32-byte `Uint8Array`, creates an Ed25519 private key for use\n * with other methods in this package that accept\n * [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey) objects.\n *\n * @param bytes 32 bytes that represent the private key\n * @param extractable Setting this to `true` makes it possible to extract the bytes of the private\n * key using the [`crypto.subtle.exportKey()`](https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/exportKey)\n * API. Defaults to `false`.\n *\n * @example\n * ```ts\n * import { createPrivateKeyFromBytes } from '@solana/keys';\n *\n * const privateKey = await createPrivateKeyFromBytes(new Uint8Array([...]));\n * const extractablePrivateKey = await createPrivateKeyFromBytes(new Uint8Array([...]), true);\n * ```\n */\nexport async function createPrivateKeyFromBytes(\n    bytes: ReadonlyUint8Array,\n    extractable: boolean = false,\n): Promise<CryptoKey> {\n    const actualLength = bytes.byteLength;\n    if (actualLength !== 32) {\n        throw new SolanaError(SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH, {\n            actualLength,\n        });\n    }\n    const privateKeyBytesPkcs8 = addPkcs8Header(bytes);\n    return await crypto.subtle.importKey('pkcs8', privateKeyBytesPkcs8, ED25519_ALGORITHM_IDENTIFIER, extractable, [\n        'sign',\n    ]);\n}\n", "import { assertKeyExporterIsAvailable } from '@solana/assertions';\nimport { SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY, SolanaError } from '@solana/errors';\n\n/**\n * Given an extractable [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey)\n * private key, gets the corresponding public key as a\n * [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey).\n *\n * @param extractable Setting this to `true` makes it possible to extract the bytes of the public\n * key using the [`crypto.subtle.exportKey()`](https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/exportKey)\n * API. Defaults to `false`.\n *\n * @example\n * ```ts\n * import { createPrivateKeyFromBytes, getPublicKeyFromPrivateKey } from '@solana/keys';\n *\n * const privateKey = await createPrivateKeyFromBytes(new Uint8Array([...]), true);\n *\n * const publicKey = await getPublicKeyFromPrivateKey(privateKey);\n * const extractablePublicKey = await getPublicKeyFromPrivateKey(privateKey, true);\n * ```\n */\nexport async function getPublicKeyFromPrivateKey(\n    privateKey: CryptoKey,\n    extractable: boolean = false,\n): Promise<CryptoKey> {\n    assertKeyExporterIsAvailable();\n\n    if (privateKey.extractable === false) {\n        throw new SolanaError(SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY, { key: privateKey });\n    }\n\n    // Export private key.\n    const jwk = await crypto.subtle.exportKey('jwk', privateKey);\n\n    // Import public key.\n    return await crypto.subtle.importKey(\n        'jwk',\n        {\n            crv /* curve */: 'Ed25519',\n            ext /* extractable */: extractable,\n            key_ops /* key operations */: ['verify'],\n            kty /* key type */: 'OKP' /* octet key pair */,\n            x /* public key x-coordinate */: jwk.x,\n        },\n        'Ed25519',\n        extractable,\n        ['verify'],\n    );\n}\n", "import { assertSigningCapabilityIsAvailable, assertVerificationCapabilityIsAvailable } from '@solana/assertions';\nimport { Encoder, ReadonlyUint8Array } from '@solana/codecs-core';\nimport { getBase58Encoder } from '@solana/codecs-strings';\nimport {\n    SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE,\n    SolanaError,\n} from '@solana/errors';\nimport { Brand, EncodedString } from '@solana/nominal-types';\n\nimport { ED25519_ALGORITHM_IDENTIFIER } from './algorithm';\n\n/**\n * A 64-byte Ed25519 signature as a base58-encoded string.\n */\nexport type Signature = Brand<EncodedString<string, 'base58'>, 'Signature'>;\n/**\n * A 64-byte Ed25519 signature.\n *\n * Whenever you need to verify that a particular signature is, in fact, the one that would have been\n * produced by signing some known bytes using the private key associated with some known public key,\n * use the {@link verifySignature} function in this package.\n */\nexport type SignatureBytes = Brand<Uint8Array, 'SignatureBytes'>;\n\nlet base58Encoder: Encoder<string> | undefined;\n\n/**\n * Asserts that an arbitrary string is a base58-encoded Ed25519 signature.\n *\n * Useful when you receive a string from user input or an untrusted network API that you expect to\n * represent an Ed25519 signature (eg. of a transaction).\n *\n * @example\n * ```ts\n * import { assertIsSignature } from '@solana/keys';\n *\n * // Imagine a function that asserts whether a user-supplied signature is valid or not.\n * function handleSubmit() {\n *     // We know only that what the user typed conforms to the `string` type.\n *     const signature: string = signatureInput.value;\n *     try {\n *         // If this type assertion function doesn't throw, then\n *         // Typescript will upcast `signature` to `Signature`.\n *         assertIsSignature(signature);\n *         // At this point, `signature` is a `Signature` that can be used with the RPC.\n *         const {\n *             value: [status],\n *         } = await rpc.getSignatureStatuses([signature]).send();\n *     } catch (e) {\n *         // `signature` turned out not to be a base58-encoded signature\n *     }\n * }\n * ```\n */\nexport function assertIsSignature(putativeSignature: string): asserts putativeSignature is Signature {\n    if (!base58Encoder) base58Encoder = getBase58Encoder();\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest value (64 bytes of zeroes)\n        putativeSignature.length < 64 ||\n        // Highest value (64 bytes of 255)\n        putativeSignature.length > 88\n    ) {\n        throw new SolanaError(SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE, {\n            actualLength: putativeSignature.length,\n        });\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const bytes = base58Encoder.encode(putativeSignature);\n    const numBytes = bytes.byteLength;\n    if (numBytes !== 64) {\n        throw new SolanaError(SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH, {\n            actualLength: numBytes,\n        });\n    }\n}\n\n/**\n * A type guard that accepts a string as input. It will both return `true` if the string conforms to\n * the {@link Signature} type and will refine the type for use in your program.\n *\n * @example\n * ```ts\n * import { isSignature } from '@solana/keys';\n *\n * if (isSignature(signature)) {\n *     // At this point, `signature` has been refined to a\n *     // `Signature` that can be used with the RPC.\n *     const {\n *         value: [status],\n *     } = await rpc.getSignatureStatuses([signature]).send();\n *     setSignatureStatus(status);\n * } else {\n *     setError(`${signature} is not a transaction signature`);\n * }\n * ```\n */\nexport function isSignature(putativeSignature: string): putativeSignature is Signature {\n    if (!base58Encoder) base58Encoder = getBase58Encoder();\n\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest value (64 bytes of zeroes)\n        putativeSignature.length < 64 ||\n        // Highest value (64 bytes of 255)\n        putativeSignature.length > 88\n    ) {\n        return false;\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const bytes = base58Encoder.encode(putativeSignature);\n    const numBytes = bytes.byteLength;\n    if (numBytes !== 64) {\n        return false;\n    }\n    return true;\n}\n\n/**\n * Given a private [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey) and a\n * `Uint8Array` of bytes, this method will return the 64-byte Ed25519 signature of that data as a\n * `Uint8Array`.\n *\n * @example\n * ```ts\n * import { signBytes } from '@solana/keys';\n *\n * const data = new Uint8Array([1, 2, 3]);\n * const signature = await signBytes(privateKey, data);\n * ```\n */\nexport async function signBytes(key: CryptoKey, data: ReadonlyUint8Array): Promise<SignatureBytes> {\n    assertSigningCapabilityIsAvailable();\n    const signedData = await crypto.subtle.sign(ED25519_ALGORITHM_IDENTIFIER, key, data);\n    return new Uint8Array(signedData) as SignatureBytes;\n}\n\n/**\n * This helper combines _asserting_ that a string is an Ed25519 signature with _coercing_ it to the\n * {@link Signature} type. It's best used with untrusted input.\n *\n * @example\n * ```ts\n * import { signature } from '@solana/keys';\n *\n * const signature = signature(userSuppliedSignature);\n * const {\n *     value: [status],\n * } = await rpc.getSignatureStatuses([signature]).send();\n * ```\n */\nexport function signature(putativeSignature: string): Signature {\n    assertIsSignature(putativeSignature);\n    return putativeSignature;\n}\n\n/**\n * Given a public [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey), some\n * {@link SignatureBytes}, and a `Uint8Array` of data, this method will return `true` if the\n * signature was produced by signing the data using the private key associated with the public key,\n * and `false` otherwise.\n *\n * @example\n * ```ts\n * import { verifySignature } from '@solana/keys';\n *\n * const data = new Uint8Array([1, 2, 3]);\n * if (!(await verifySignature(publicKey, signature, data))) {\n *     throw new Error('The data were *not* signed by the private key associated with `publicKey`');\n * }\n * ```\n */\nexport async function verifySignature(\n    key: CryptoKey,\n    signature: SignatureBytes,\n    data: ReadonlyUint8Array,\n): Promise<boolean> {\n    assertVerificationCapabilityIsAvailable();\n    return await crypto.subtle.verify(ED25519_ALGORITHM_IDENTIFIER, key, signature, data);\n}\n", "import { assertKeyGenerationIsAvailable, assertPRNGIsAvailable } from '@solana/assertions';\nimport { ReadonlyUint8Array } from '@solana/codecs-core';\nimport {\n    SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY,\n    SolanaError,\n} from '@solana/errors';\n\nimport { ED25519_ALGORITHM_IDENTIFIER } from './algorithm';\nimport { createPrivateKeyFromBytes } from './private-key';\nimport { getPublicKeyFromPrivateKey } from './public-key';\nimport { signBytes, verifySignature } from './signatures';\n\n/**\n * Generates an Ed25519 public/private key pair for use with other methods in this package that\n * accept [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey) objects.\n *\n * @example\n * ```ts\n * import { generateKeyPair } from '@solana/keys';\n *\n * const { privateKey, publicKey } = await generateKeyPair();\n * ```\n */\nexport async function generateKeyPair(): Promise<CryptoKeyPair> {\n    await assertKeyGenerationIsAvailable();\n    const keyPair = await crypto.subtle.generateKey(\n        /* algorithm */ ED25519_ALGORITHM_IDENTIFIER, // Native implementation status: https://github.com/WICG/webcrypto-secure-curves/issues/20\n        /* extractable */ false, // Prevents the bytes of the private key from being visible to JS.\n        /* allowed uses */ ['sign', 'verify'],\n    );\n    return keyPair;\n}\n\n/**\n * Given a 64-byte `Uint8Array` secret key, creates an Ed25519 public/private key pair for use with\n * other methods in this package that accept [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey)\n * objects.\n *\n * @param bytes 64 bytes, the first 32 of which represent the private key and the last 32 of which\n * represent its associated public key\n * @param extractable Setting this to `true` makes it possible to extract the bytes of the private\n * key using the [`crypto.subtle.exportKey()`](https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/exportKey)\n * API. Defaults to `false`.\n *\n * @example\n * ```ts\n * import fs from 'fs';\n * import { createKeyPairFromBytes } from '@solana/keys';\n *\n * // Get bytes from local keypair file.\n * const keypairFile = fs.readFileSync('~/.config/solana/id.json');\n * const keypairBytes = new Uint8Array(JSON.parse(keypairFile.toString()));\n *\n * // Create a CryptoKeyPair from the bytes.\n * const { privateKey, publicKey } = await createKeyPairFromBytes(keypairBytes);\n * ```\n */\nexport async function createKeyPairFromBytes(\n    bytes: ReadonlyUint8Array,\n    extractable: boolean = false,\n): Promise<CryptoKeyPair> {\n    assertPRNGIsAvailable();\n\n    if (bytes.byteLength !== 64) {\n        throw new SolanaError(SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH, { byteLength: bytes.byteLength });\n    }\n    const [publicKey, privateKey] = await Promise.all([\n        crypto.subtle.importKey('raw', bytes.slice(32), ED25519_ALGORITHM_IDENTIFIER, /* extractable */ true, [\n            'verify',\n        ]),\n        createPrivateKeyFromBytes(bytes.slice(0, 32), extractable),\n    ]);\n\n    // Verify the key pair\n    const randomBytes = new Uint8Array(32);\n    crypto.getRandomValues(randomBytes);\n    const signedData = await signBytes(privateKey, randomBytes);\n    const isValid = await verifySignature(publicKey, signedData, randomBytes);\n    if (!isValid) {\n        throw new SolanaError(SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY);\n    }\n\n    return { privateKey, publicKey } as CryptoKeyPair;\n}\n\n/**\n * Given a private key represented as a 32-byte `Uint8Array`, creates an Ed25519 public/private key\n * pair for use with other methods in this package that accept [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey)\n * objects.\n *\n * @param bytes 32 bytes that represent the private key\n * @param extractable Setting this to `true` makes it possible to extract the bytes of the private\n * key using the [`crypto.subtle.exportKey()`](https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/exportKey)\n * API. Defaults to `false`.\n *\n * @example\n * ```ts\n * import { createKeyPairFromPrivateKeyBytes } from '@solana/keys';\n *\n * const { privateKey, publicKey } = await createKeyPairFromPrivateKeyBytes(new Uint8Array([...]));\n * ```\n *\n * This can be useful when you have a private key but not the corresponding public key or when you\n * need to derive key pairs from seeds. For instance, the following code snippet derives a key pair\n * from the hash of a message.\n *\n * ```ts\n * import { getUtf8Encoder } from '@solana/codecs-strings';\n * import { createKeyPairFromPrivateKeyBytes } from '@solana/keys';\n *\n * const message = getUtf8Encoder().encode('Hello, World!');\n * const seed = new Uint8Array(await crypto.subtle.digest('SHA-256', message));\n *\n * const derivedKeypair = await createKeyPairFromPrivateKeyBytes(seed);\n * ```\n */\nexport async function createKeyPairFromPrivateKeyBytes(\n    bytes: ReadonlyUint8Array,\n    extractable: boolean = false,\n): Promise<CryptoKeyPair> {\n    const privateKeyPromise = createPrivateKeyFromBytes(bytes, extractable);\n\n    // Here we need the private key to be extractable in order to export\n    // it as a public key. Therefore, if the `extractable` parameter\n    // is `false`, we need to create two private keys such that:\n    //   - The extractable one is used to create the public key and\n    //   - The non-extractable one is the one we will return.\n    const [publicKey, privateKey] = await Promise.all([\n        // This nested promise makes things efficient by\n        // creating the public key in parallel with the\n        // second private key creation, if it is needed.\n        (extractable ? privateKeyPromise : createPrivateKeyFromBytes(bytes, true /* extractable */)).then(\n            async privateKey => await getPublicKeyFromPrivateKey(privateKey, true /* extractable */),\n        ),\n        privateKeyPromise,\n    ]);\n\n    return { privateKey, publicKey };\n}\n"]}