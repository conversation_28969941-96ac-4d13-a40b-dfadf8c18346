{"version": 3, "sources": ["../src/sysvar.ts", "../src/clock.ts", "../src/epoch-rewards.ts", "../src/epoch-schedule.ts", "../src/last-restart-slot.ts", "../src/recent-blockhashes.ts", "../src/rent.ts", "../src/slot-hashes.ts", "../src/slot-history.ts", "../src/stake-history.ts"], "names": ["fetchEncodedAccount", "fetch<PERSON>sonParsedAccount", "getStructEncoder", "getU64Encoder", "getI64Encoder", "getStructDecoder", "getU64Decoder", "getI64Decoder", "combineCodec", "assertAccountExists", "decodeAccount", "getBlockhashEncoder", "getU128<PERSON>ncoder", "getDefaultLamportsEncoder", "getBooleanEncoder", "getBlockhashDecoder", "getU128Decoder", "getDefaultLamportsDecoder", "getBooleanDecoder", "getArray<PERSON><PERSON>der", "getArrayDecoder", "getF64Encoder", "getU8Encoder", "getF64Decoder", "getU8Decoder", "getArrayCodec", "getU64Codec", "createEncoder", "createDecoder", "SolanaError", "SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH", "SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE", "SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS"], "mappings": ";;;;;;;;AAYO,IAAM,oBACT,GAAA;AACG,IAAM,4BACT,GAAA;AACG,IAAM,6BACT,GAAA;AACG,IAAM,2BACT,GAAA;AACG,IAAM,gCACT,GAAA;AACG,IAAM,iCACT,GAAA;AACG,IAAM,mBACT,GAAA;AACG,IAAM,0BACT,GAAA;AACG,IAAM,2BACT,GAAA;AACG,IAAM,4BACT,GAAA;AAoBJ,eAAsB,yBAAA,CAClB,GACA,EAAA,OAAA,EACA,MACsC,EAAA;AACtC,EAAA,OAAO,MAAMA,4BAAA,CAA8B,GAAK,EAAA,OAAA,EAAS,MAAM,CAAA;AACnE;AAQA,eAAsB,4BAAA,CAClB,GACA,EAAA,OAAA,EACA,MACwF,EAAA;AACxF,EAAA,OAAO,MAAMC,+BAAA,CAA0D,GAAK,EAAA,OAAA,EAAS,MAAM,CAAA;AAC/F;;;ACzBO,SAAS,qBAAwE,GAAA;AACpF,EAAA,OAAOC,uBAAiB,CAAA;AAAA,IACpB,CAAC,MAAQ,EAAAC,oBAAA,EAAe,CAAA;AAAA,IACxB,CAAC,qBAAuB,EAAAC,oBAAA,EAAe,CAAA;AAAA,IACvC,CAAC,OAAS,EAAAD,oBAAA,EAAe,CAAA;AAAA,IACzB,CAAC,qBAAuB,EAAAA,oBAAA,EAAe,CAAA;AAAA,IACvC,CAAC,eAAiB,EAAAC,oBAAA,EAAe;AAAA,GACpC,CAAA;AACL;AAMO,SAAS,qBAAwE,GAAA;AACpF,EAAA,OAAOC,uBAAiB,CAAA;AAAA,IACpB,CAAC,MAAQ,EAAAC,oBAAA,EAAe,CAAA;AAAA,IACxB,CAAC,qBAAuB,EAAAC,oBAAA,EAAe,CAAA;AAAA,IACvC,CAAC,OAAS,EAAAD,oBAAA,EAAe,CAAA;AAAA,IACzB,CAAC,qBAAuB,EAAAA,oBAAA,EAAe,CAAA;AAAA,IACvC,CAAC,eAAiB,EAAAC,oBAAA,EAAe;AAAA,GACpC,CAAA;AACL;AAQO,SAAS,mBAAiF,GAAA;AAC7F,EAAA,OAAOC,mBAAa,CAAA,qBAAA,EAAyB,EAAA,qBAAA,EAAuB,CAAA;AACxE;AAKA,eAAsB,gBAAA,CAAiB,KAA6B,MAAmD,EAAA;AACnH,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,sBAAsB,MAAM,CAAA;AACjF,EAAAC,4BAAA,CAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAU,GAAAC,sBAAA,CAAc,OAAS,EAAA,qBAAA,EAAuB,CAAA;AAC9D,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;ACpBO,SAAS,4BAA6F,GAAA;AACzG,EAAA,OAAOR,uBAAiB,CAAA;AAAA,IACpB,CAAC,iCAAmCC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACnD,CAAC,eAAiBA,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,iBAAmB,EAAAQ,4BAAA,EAAqB,CAAA;AAAA,IACzC,CAAC,aAAe,EAAAC,qBAAA,EAAgB,CAAA;AAAA,IAChC,CAAC,cAAgB,EAAAC,kCAAA,EAA2B,CAAA;AAAA,IAC5C,CAAC,oBAAsB,EAAAA,kCAAA,EAA2B,CAAA;AAAA,IAClD,CAAC,QAAU,EAAAC,wBAAA,EAAmB;AAAA,GACjC,CAAA;AACL;AAMO,SAAS,4BAA6F,GAAA;AACzG,EAAA,OAAOT,uBAAiB,CAAA;AAAA,IACpB,CAAC,iCAAmCC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACnD,CAAC,eAAiBA,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,iBAAmB,EAAAS,4BAAA,EAAqB,CAAA;AAAA,IACzC,CAAC,aAAe,EAAAC,qBAAA,EAAgB,CAAA;AAAA,IAChC,CAAC,cAAgB,EAAAC,kCAAA,EAA2B,CAAA;AAAA,IAC5C,CAAC,oBAAsB,EAAAA,kCAAA,EAA2B,CAAA;AAAA,IAClD,CAAC,QAAU,EAAAC,wBAAA,EAAmB;AAAA,GACjC,CAAA;AACL;AAQO,SAAS,0BAId,GAAA;AACE,EAAA,OAAOV,mBAAa,CAAA,4BAAA,EAAgC,EAAA,4BAAA,EAA8B,CAAA;AACtF;AAMA,eAAsB,uBAAA,CAClB,KACA,MAC2B,EAAA;AAC3B,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,8BAA8B,MAAM,CAAA;AACzF,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,4BAAA,EAA8B,CAAA;AACrE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;ACxEO,SAAS,6BAAgG,GAAA;AAC5G,EAAA,OAAOR,uBAAiB,CAAA;AAAA,IACpB,CAAC,eAAiBC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,0BAA4BA,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IAC5C,CAAC,QAAUW,EAAAA,wBAAAA,EAAmB,CAAA;AAAA,IAC9B,CAAC,kBAAoBX,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACpC,CAAC,iBAAmBA,EAAAA,oBAAAA,EAAe;AAAA,GACtC,CAAA;AACL;AAMO,SAAS,6BAAgG,GAAA;AAC5G,EAAA,OAAOE,uBAAiB,CAAA;AAAA,IACpB,CAAC,eAAiBC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,0BAA4BA,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IAC5C,CAAC,QAAUY,EAAAA,wBAAAA,EAAmB,CAAA;AAAA,IAC9B,CAAC,kBAAoBZ,EAAAA,oBAAAA,EAAe,CAAA;AAAA,IACpC,CAAC,iBAAmBA,EAAAA,oBAAAA,EAAe;AAAA,GACtC,CAAA;AACL;AAQO,SAAS,2BAId,GAAA;AACE,EAAA,OAAOE,mBAAa,CAAA,6BAAA,EAAiC,EAAA,6BAAA,EAA+B,CAAA;AACxF;AAMA,eAAsB,wBAAA,CAClB,KACA,MAC4B,EAAA;AAC5B,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,+BAA+B,MAAM,CAAA;AAC1F,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,6BAAA,EAA+B,CAAA;AACtE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;ACjEO,SAAS,+BAAsG,GAAA;AAClH,EAAA,OAAOR,wBAAiB,CAAC,CAAC,mBAAmBC,oBAAc,EAAC,CAAC,CAAC,CAAA;AAIlE;AAMO,SAAS,+BAAsG,GAAA;AAClH,EAAA,OAAOE,wBAAiB,CAAC,CAAC,mBAAmBC,oBAAc,EAAC,CAAC,CAAC,CAAA;AAIlE;AAQO,SAAS,6BAId,GAAA;AACE,EAAA,OAAOE,mBAAa,CAAA,+BAAA,EAAmC,EAAA,+BAAA,EAAiC,CAAA;AAC5F;AAMA,eAAsB,0BAAA,CAClB,KACA,MAC8B,EAAA;AAC9B,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,kCAAkC,MAAM,CAAA;AAC7F,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,+BAAA,EAAiC,CAAA;AACxE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;ACzBO,SAAS,iCAAkF,GAAA;AAC9F,EAAO,OAAAS,sBAAA;AAAA,IACHjB,uBAAiB,CAAA;AAAA,MACb,CAAC,WAAaS,EAAAA,4BAAAA,EAAqB,CAAA;AAAA,MACnC,CAAC,eAAiBT,EAAAA,uBAAAA,CAAiB,CAAC,CAAC,wBAAwBW,kCAA0B,EAAC,CAAC,CAAC,CAAC;AAAA,KAC9F;AAAA,GACL;AACJ;AAUO,SAAS,iCAAkF,GAAA;AAC9F,EAAO,OAAAO,sBAAA;AAAA,IACHf,uBAAiB,CAAA;AAAA,MACb,CAAC,WAAaU,EAAAA,4BAAAA,EAAqB,CAAA;AAAA,MACnC,CAAC,eAAiBV,EAAAA,uBAAAA,CAAiB,CAAC,CAAC,wBAAwBY,kCAA0B,EAAC,CAAC,CAAC,CAAC;AAAA,KAC9F;AAAA,GACL;AACJ;AAYO,SAAS,+BAA8E,GAAA;AAC1F,EAAA,OAAOT,mBAAa,CAAA,iCAAA,EAAqC,EAAA,iCAAA,EAAmC,CAAA;AAChG;AAUA,eAAsB,4BAAA,CAClB,KACA,MACgC,EAAA;AAChC,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,mCAAmC,MAAM,CAAA;AAC9F,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,iCAAA,EAAmC,CAAA;AAC1E,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;AC/DO,SAAS,oBAAqE,GAAA;AACjF,EAAA,OAAOR,uBAAiB,CAAA;AAAA,IACpB,CAAC,qBAAuBW,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,IACnD,CAAC,oBAAsB,EAAAQ,oBAAA,EAAe,CAAA;AAAA,IACtC,CAAC,aAAe,EAAAC,mBAAA,EAAc;AAAA,GACjC,CAAA;AACL;AAMO,SAAS,oBAAqE,GAAA;AACjF,EAAA,OAAOjB,uBAAiB,CAAA;AAAA,IACpB,CAAC,qBAAuBY,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,IACnD,CAAC,oBAAsB,EAAAM,oBAAA,EAAe,CAAA;AAAA,IACtC,CAAC,aAAe,EAAAC,mBAAA,EAAc;AAAA,GACjC,CAAA;AACL;AAQO,SAAS,kBAA6E,GAAA;AACzF,EAAA,OAAOhB,mBAAa,CAAA,oBAAA,EAAwB,EAAA,oBAAA,EAAsB,CAAA;AACtE;AAKA,eAAsB,eAAA,CAAgB,KAA6B,MAAkD,EAAA;AACjH,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,qBAAqB,MAAM,CAAA;AAChF,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,oBAAA,EAAsB,CAAA;AAC7D,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;ACtDO,SAAS,0BAAoE,GAAA;AAChF,EAAOS,OAAAA,sBAAAA;AAAA,IACHjB,uBAAiB,CAAA;AAAA,MACb,CAAC,MAAQC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,MACxB,CAAC,MAAQQ,EAAAA,4BAAAA,EAAqB;AAAA,KACjC;AAAA,GACL;AACJ;AAMO,SAAS,0BAAoE,GAAA;AAChF,EAAOS,OAAAA,sBAAAA;AAAA,IACHf,uBAAiB,CAAA;AAAA,MACb,CAAC,MAAQC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,MACxB,CAAC,MAAQS,EAAAA,4BAAAA,EAAqB;AAAA,KACjC;AAAA,GACL;AACJ;AAQO,SAAS,wBAAgE,GAAA;AAC5E,EAAA,OAAOP,mBAAa,CAAA,0BAAA,EAA8B,EAAA,0BAAA,EAA4B,CAAA;AAClF;AAKA,eAAsB,qBAAA,CAClB,KACA,MACyB,EAAA;AACzB,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,4BAA4B,MAAM,CAAA;AACvF,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,0BAAA,EAA4B,CAAA;AACnE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;AChDA,IAAM,oBAAuB,GAAA,CAAA;AAM7B,IAAM,kBAAkB,IAAO,GAAA,IAAA;AAG/B,IAAM,gBAAgB,eAAkB,GAAA,EAAA;AAExC,IAAM,qCACF,GAAA,CAAA;AACA,CAAA;AACA,aAAA,GAAgB,CAChB,GAAA,CAAA;AACA,CAAA;AAEJ,IAAI,kBAAA;AACJ,IAAI,kBAAA;AACJ,IAAI,uBAAA;AACJ,IAAI,uBAAA;AAEJ,SAAS,qBAAqD,GAAA;AAC1D,EAAI,IAAA,CAAC,kBAAoB,EAAA,kBAAA,GAAqBP,oBAAc,EAAA;AAC5D,EAAO,OAAA,kBAAA;AACX;AACA,SAAS,qBAAqD,GAAA;AAC1D,EAAI,IAAA,CAAC,kBAAoB,EAAA,kBAAA,GAAqBG,oBAAc,EAAA;AAC5D,EAAO,OAAA,kBAAA;AACX;AACA,SAAS,0BAA+E,GAAA;AACpF,EAAI,IAAA,CAAC,yBAAmD,uBAAA,GAAAmB,oBAAA,CAAcC,oBAAe,EAAA,EAAE,IAAM,EAAA,aAAA,EAAe,CAAA;AAC5G,EAAO,OAAA,uBAAA;AACX;AACA,SAAS,0BAA+E,GAAA;AACpF,EAAI,IAAA,CAAC,yBAAmD,uBAAA,GAAAD,oBAAA,CAAcC,oBAAe,EAAA,EAAE,IAAM,EAAA,aAAA,EAAe,CAAA;AAC5G,EAAO,OAAA,uBAAA;AACX;AAuBO,SAAS,2BAA0F,GAAA;AACtG,EAAA,OAAOC,oBAAc,CAAA;AAAA,IACjB,SAAW,EAAA,qCAAA;AAAA,IACX,KAAO,EAAA,CAAC,KAA0B,EAAA,KAAA,EAAO,MAAW,KAAA;AAEhD,MAAA,KAAA,CAAM,GAAI,CAAA,CAAC,oBAAoB,CAAA,EAAG,MAAM,CAAA;AACxC,MAAU,MAAA,IAAA,CAAA;AAEV,MAAA,qBAAA,GAAwB,KAAM,CAAA,MAAA,CAAO,aAAa,CAAA,EAAG,OAAO,MAAM,CAAA;AAClE,MAAU,MAAA,IAAA,CAAA;AAGV,MAAA,0BAAA,EAA6B,CAAA,KAAA,CAAM,KAAM,CAAA,IAAA,EAAM,OAAO,MAAM,CAAA;AAC5D,MAAA,MAAA,IAAU,aAAgB,GAAA,CAAA;AAE1B,MAAA,qBAAA,GAAwB,KAAM,CAAA,MAAA,CAAO,eAAe,CAAA,EAAG,OAAO,MAAM,CAAA;AACpE,MAAU,MAAA,IAAA,CAAA;AAEV,MAAA,qBAAA,EAAwB,CAAA,KAAA,CAAM,KAAM,CAAA,QAAA,EAAU,OAAO,MAAM,CAAA;AAC3D,MAAU,MAAA,IAAA,CAAA;AACV,MAAO,OAAA,MAAA;AAAA;AACX,GACH,CAAA;AACL;AAMO,SAAS,2BAA0F,GAAA;AACtG,EAAA,OAAOC,oBAAc,CAAA;AAAA,IACjB,SAAW,EAAA,qCAAA;AAAA,IACX,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AAEtD,MAAI,IAAA,KAAA,CAAM,UAAU,qCAAuC,EAAA;AACvD,QAAM,MAAA,IAAIC,mBAAYC,gDAA2C,EAAA;AAAA,UAC7D,QAAQ,KAAM,CAAA,MAAA;AAAA,UACd,QAAU,EAAA;AAAA,SACb,CAAA;AAAA;AAGL,MAAM,MAAA,aAAA,GAAgB,MAAM,MAAM,CAAA;AAClC,MAAU,MAAA,IAAA,CAAA;AACV,MAAA,IAAI,kBAAkB,oBAAsB,EAAA;AACxC,QAAM,MAAA,IAAID,mBAAYE,4DAAuD,EAAA;AAAA,UACzE,MAAQ,EAAA,aAAA;AAAA,UACR,QAAU,EAAA;AAAA,SACb,CAAA;AAAA;AAGL,MAAA,MAAM,eAAe,qBAAsB,EAAA,CAAE,KAAK,KAAO,EAAA,MAAM,EAAE,CAAC,CAAA;AAClE,MAAU,MAAA,IAAA,CAAA;AACV,MAAI,IAAA,YAAA,KAAiB,MAAO,CAAA,aAAa,CAAG,EAAA;AACxC,QAAM,MAAA,IAAIF,mBAAYG,oDAA+C,EAAA;AAAA,UACjE,MAAQ,EAAA,YAAA;AAAA,UACR,gBAAkB,EAAA,wBAAA;AAAA,UAClB,QAAU,EAAA;AAAA,SACb,CAAA;AAAA;AAGL,MAAA,MAAM,OAAO,0BAA2B,EAAA,CAAE,KAAK,KAAO,EAAA,MAAM,EAAE,CAAC,CAAA;AAC/D,MAAA,MAAA,IAAU,aAAgB,GAAA,CAAA;AAE1B,MAAA,MAAM,UAAU,qBAAsB,EAAA,CAAE,KAAK,KAAO,EAAA,MAAM,EAAE,CAAC,CAAA;AAC7D,MAAU,MAAA,IAAA,CAAA;AACV,MAAI,IAAA,OAAA,KAAY,MAAO,CAAA,eAAe,CAAG,EAAA;AACrC,QAAM,MAAA,IAAIH,mBAAYG,oDAA+C,EAAA;AAAA,UACjE,MAAQ,EAAA,OAAA;AAAA,UACR,gBAAkB,EAAA,wBAAA;AAAA,UAClB,QAAU,EAAA;AAAA,SACb,CAAA;AAAA;AAGL,MAAA,MAAM,WAAW,qBAAsB,EAAA,CAAE,KAAK,KAAO,EAAA,MAAM,EAAE,CAAC,CAAA;AAC9D,MAAU,MAAA,IAAA,CAAA;AACV,MAAO,OAAA;AAAA,QACH;AAAA,UACI,IAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,OACJ;AAAA;AACJ,GACH,CAAA;AACL;AAQO,SAAS,yBAId,GAAA;AACE,EAAA,OAAOxB,mBAAa,CAAA,2BAAA,EAA+B,EAAA,2BAAA,EAA6B,CAAA;AACpF;AAMA,eAAsB,sBAAA,CAClB,KACA,MAC0B,EAAA;AAC1B,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,6BAA6B,MAAM,CAAA;AACxF,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,2BAAA,EAA6B,CAAA;AACpE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB;AC1JO,SAAS,4BAAwE,GAAA;AACpF,EAAOS,OAAAA,sBAAAA;AAAA,IACHjB,uBAAiB,CAAA;AAAA,MACb,CAAC,OAASC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,MACzB;AAAA,QACI,cAAA;AAAA,QACAD,uBAAiB,CAAA;AAAA,UACb,CAAC,WAAaW,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,UACzC,CAAC,YAAcA,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,UAC1C,CAAC,cAAgBA,EAAAA,kCAAAA,EAA2B;AAAA,SAC/C;AAAA;AACL,KACH,CAAA;AAAA,IACD,EAAE,IAAMV,EAAAA,oBAAAA,EAAgB;AAAA,GAC5B;AACJ;AAMO,SAAS,4BAAwE,GAAA;AACpF,EAAOiB,OAAAA,sBAAAA;AAAA,IACHf,uBAAiB,CAAA;AAAA,MACb,CAAC,OAASC,EAAAA,oBAAAA,EAAe,CAAA;AAAA,MACzB;AAAA,QACI,cAAA;AAAA,QACAD,uBAAiB,CAAA;AAAA,UACb,CAAC,WAAaY,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,UACzC,CAAC,YAAcA,EAAAA,kCAAAA,EAA2B,CAAA;AAAA,UAC1C,CAAC,cAAgBA,EAAAA,kCAAAA,EAA2B;AAAA,SAC/C;AAAA;AACL,KACH,CAAA;AAAA,IACD,EAAE,IAAMX,EAAAA,oBAAAA,EAAgB;AAAA,GAC5B;AACJ;AAQO,SAAS,0BAAoE,GAAA;AAChF,EAAA,OAAOE,mBAAa,CAAA,4BAAA,EAAgC,EAAA,4BAAA,EAA8B,CAAA;AACtF;AAMA,eAAsB,uBAAA,CAClB,KACA,MAC2B,EAAA;AAC3B,EAAA,MAAM,OAAU,GAAA,MAAM,yBAA0B,CAAA,GAAA,EAAK,8BAA8B,MAAM,CAAA;AACzF,EAAAC,6BAAoB,OAAO,CAAA;AAC3B,EAAA,MAAM,OAAUC,GAAAA,sBAAAA,CAAc,OAAS,EAAA,4BAAA,EAA8B,CAAA;AACrE,EAAA,OAAO,OAAQ,CAAA,IAAA;AACnB", "file": "index.browser.cjs", "sourcesContent": ["import {\n    type FetchAccountConfig,\n    fetchEncodedAccount,\n    fetchJsonParsedAccount,\n    type MaybeAccount,\n    type MaybeEncodedAccount,\n} from '@solana/accounts';\nimport type { Address } from '@solana/addresses';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { JsonParsedSysvarAccount } from '@solana/rpc-parsed-types';\nimport type { Rpc } from '@solana/rpc-spec';\n\nexport const SYSVAR_CLOCK_ADDRESS =\n    'SysvarC1ock11111111111111111111111111111111' as Address<'SysvarC1ock11111111111111111111111111111111'>;\nexport const SYSVAR_EPOCH_REWARDS_ADDRESS =\n    'SysvarEpochRewards1111111111111111111111111' as Address<'SysvarEpochRewards1111111111111111111111111'>;\nexport const SYSVAR_EPOCH_SCHEDULE_ADDRESS =\n    'SysvarEpochSchedu1e111111111111111111111111' as Address<'SysvarEpochSchedu1e111111111111111111111111'>;\nexport const SYSVAR_INSTRUCTIONS_ADDRESS =\n    'Sysvar1nstructions1111111111111111111111111' as Address<'Sysvar1nstructions1111111111111111111111111'>;\nexport const SYSVAR_LAST_RESTART_SLOT_ADDRESS =\n    'SysvarLastRestartS1ot1111111111111111111111' as Address<'SysvarLastRestartS1ot1111111111111111111111'>;\nexport const SYSVAR_RECENT_BLOCKHASHES_ADDRESS =\n    'SysvarRecentB1ockHashes11111111111111111111' as Address<'SysvarRecentB1ockHashes11111111111111111111'>;\nexport const SYSVAR_RENT_ADDRESS =\n    'SysvarRent111111111111111111111111111111111' as Address<'SysvarRent111111111111111111111111111111111'>;\nexport const SYSVAR_SLOT_HASHES_ADDRESS =\n    'SysvarS1otHashes111111111111111111111111111' as Address<'SysvarS1otHashes111111111111111111111111111'>;\nexport const SYSVAR_SLOT_HISTORY_ADDRESS =\n    'SysvarS1otHistory11111111111111111111111111' as Address<'SysvarS1otHistory11111111111111111111111111'>;\nexport const SYSVAR_STAKE_HISTORY_ADDRESS =\n    'SysvarStakeHistory1111111111111111111111111' as Address<'SysvarStakeHistory1111111111111111111111111'>;\n\ntype SysvarAddress =\n    | typeof SYSVAR_CLOCK_ADDRESS\n    | typeof SYSVAR_EPOCH_REWARDS_ADDRESS\n    | typeof SYSVAR_EPOCH_SCHEDULE_ADDRESS\n    | typeof SYSVAR_INSTRUCTIONS_ADDRESS\n    | typeof SYSVAR_LAST_RESTART_SLOT_ADDRESS\n    | typeof SYSVAR_RECENT_BLOCKHASHES_ADDRESS\n    | typeof SYSVAR_RENT_ADDRESS\n    | typeof SYSVAR_SLOT_HASHES_ADDRESS\n    | typeof SYSVAR_SLOT_HISTORY_ADDRESS\n    | typeof SYSVAR_STAKE_HISTORY_ADDRESS;\n\n/**\n * Fetch an encoded sysvar account.\n *\n * Sysvars are special accounts that contain dynamically-updated data about the network cluster, the\n * blockchain history, and the executing transaction.\n */\nexport async function fetchEncodedSysvarAccount<TAddress extends SysvarAddress>(\n    rpc: Rpc<GetAccountInfoApi>,\n    address: TAddress,\n    config?: FetchAccountConfig,\n): Promise<MaybeEncodedAccount<TAddress>> {\n    return await fetchEncodedAccount<TAddress>(rpc, address, config);\n}\n\n/**\n * Fetch a JSON-parsed sysvar account.\n *\n * Sysvars are special accounts that contain dynamically-updated data about the network cluster, the\n * blockchain history, and the executing transaction.\n */\nexport async function fetchJsonParsedSysvarAccount<TAddress extends SysvarAddress>(\n    rpc: Rpc<GetAccountInfoApi>,\n    address: TAddress,\n    config?: FetchAccountConfig,\n): Promise<MaybeAccount<JsonParsedSysvarAccount, TAddress> | MaybeEncodedAccount<TAddress>> {\n    return await fetchJsonParsedAccount<JsonParsedSysvarAccount, TAddress>(rpc, address, config);\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getI64Decoder,\n    getI64Encoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport type { Epoch, Slot, UnixTimestamp } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_CLOCK_ADDRESS } from './sysvar';\n\ntype SysvarClockSize = 40;\n\n/**\n * Contains data on cluster time, including the current slot, epoch, and estimated wall-clock Unix\n * timestamp. It is updated every slot.\n */\nexport type SysvarClock = Readonly<{\n    /** The current epoch */\n    epoch: Epoch;\n    /**\n     * The Unix timestamp of the first slot in this epoch.\n     *\n     * In the first slot of an epoch, this timestamp is identical to the `unixTimestamp`.\n     */\n    epochStartTimestamp: UnixTimestamp;\n    /** The most recent epoch for which the leader schedule has already been generated */\n    leaderScheduleEpoch: Epoch;\n    /** The current slot */\n    slot: Slot;\n    /** The Unix timestamp of this slot */\n    unixTimestamp: UnixTimestamp;\n}>;\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarClock} to a byte array representing\n * the `Clock` sysvar's account data.\n */\nexport function getSysvarClockEncoder(): FixedSizeEncoder<SysvarClock, SysvarClockSize> {\n    return getStructEncoder([\n        ['slot', getU64Encoder()],\n        ['epochStartTimestamp', getI64Encoder()],\n        ['epoch', getU64Encoder()],\n        ['leaderScheduleEpoch', getU64Encoder()],\n        ['unixTimestamp', getI64Encoder()],\n    ]) as FixedSizeEncoder<SysvarClock, SysvarClockSize>;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `Clock` sysvar's\n * account data to a {@link SysvarClock}.\n */\nexport function getSysvarClockDecoder(): FixedSizeDecoder<SysvarClock, SysvarClockSize> {\n    return getStructDecoder([\n        ['slot', getU64Decoder()],\n        ['epochStartTimestamp', getI64Decoder()],\n        ['epoch', getU64Decoder()],\n        ['leaderScheduleEpoch', getU64Decoder()],\n        ['unixTimestamp', getI64Decoder()],\n    ]) as FixedSizeDecoder<SysvarClock, SysvarClockSize>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarClock}\n *\n * @see {@link getSysvarClockDecoder}\n * @see {@link getSysvarClockEncoder}\n */\nexport function getSysvarClockCodec(): FixedSizeCodec<SysvarClock, SysvarClock, SysvarClockSize> {\n    return combineCodec(getSysvarClockEncoder(), getSysvarClockDecoder());\n}\n\n/**\n * Fetches the `Clock` sysvar account using any RPC that supports the {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarClock(rpc: Rpc<GetAccountInfoApi>, config?: FetchAccountConfig): Promise<SysvarClock> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_CLOCK_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarClockDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getBooleanDecoder,\n    getBooleanEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n    getU128Decoder,\n    getU128Encoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport {\n    Blockhash,\n    getBlockhashDecoder,\n    getBlockhashEncoder,\n    getDefaultLamportsDecoder,\n    getDefaultLamportsEncoder,\n    Lamports,\n} from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_EPOCH_REWARDS_ADDRESS } from './sysvar';\n\ntype SysvarEpochRewardsSize = 81;\n\n/**\n * Tracks whether the rewards period (including calculation and distribution) is in progress, as\n * well as the details needed to resume distribution when starting from a snapshot during the\n * rewards period.\n *\n * The sysvar is repopulated at the start of the first block of each epoch. Therefore, the sysvar\n * contains data about the current epoch until a new epoch begins.\n */\nexport type SysvarEpochRewards = Readonly<{\n    /** Whether the rewards period (including calculation and distribution) is active */\n    active: boolean;\n    /** The rewards currently distributed for the current epoch, in {@link Lamports} */\n    distributedRewards: Lamports;\n    /** The starting block height of the rewards distribution in the current epoch */\n    distributionStartingBlockHeight: bigint;\n    /**\n     * Number of partitions in the rewards distribution in the current epoch, used to generate an\n     * `EpochRewardsHasher`\n     */\n    numPartitions: bigint;\n    /**\n     * The {@link Blockhash} of the parent block of the first block in the epoch, used to seed an\n     * `EpochRewardsHasher`\n     */\n    parentBlockhash: Blockhash;\n    /**\n     * The total rewards points calculated for the current epoch, where points equals the sum of\n     * (delegated stake * credits observed) for all  delegations\n     */\n    totalPoints: bigint;\n    /** The total rewards for the current epoch, in {@link Lamports} */\n    totalRewards: Lamports;\n}>;\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarEpochRewards} to a byte array\n * representing the `EpochRewards` sysvar's account data.\n */\nexport function getSysvarEpochRewardsEncoder(): FixedSizeEncoder<SysvarEpochRewards, SysvarEpochRewardsSize> {\n    return getStructEncoder([\n        ['distributionStartingBlockHeight', getU64Encoder()],\n        ['numPartitions', getU64Encoder()],\n        ['parentBlockhash', getBlockhashEncoder()],\n        ['totalPoints', getU128Encoder()],\n        ['totalRewards', getDefaultLamportsEncoder()],\n        ['distributedRewards', getDefaultLamportsEncoder()],\n        ['active', getBooleanEncoder()],\n    ]) as FixedSizeEncoder<SysvarEpochRewards, SysvarEpochRewardsSize>;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `EpochRewards`\n * sysvar's account data to a {@link SysvarEpochRewards}.\n */\nexport function getSysvarEpochRewardsDecoder(): FixedSizeDecoder<SysvarEpochRewards, SysvarEpochRewardsSize> {\n    return getStructDecoder([\n        ['distributionStartingBlockHeight', getU64Decoder()],\n        ['numPartitions', getU64Decoder()],\n        ['parentBlockhash', getBlockhashDecoder()],\n        ['totalPoints', getU128Decoder()],\n        ['totalRewards', getDefaultLamportsDecoder()],\n        ['distributedRewards', getDefaultLamportsDecoder()],\n        ['active', getBooleanDecoder()],\n    ]) as FixedSizeDecoder<SysvarEpochRewards, SysvarEpochRewardsSize>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarEpochRewards}\n *\n * @see {@link getSysvarEpochRewardsDecoder}\n * @see {@link getSysvarEpochRewardsEncoder}\n */\nexport function getSysvarEpochRewardsCodec(): FixedSizeCodec<\n    SysvarEpochRewards,\n    SysvarEpochRewards,\n    SysvarEpochRewardsSize\n> {\n    return combineCodec(getSysvarEpochRewardsEncoder(), getSysvarEpochRewardsDecoder());\n}\n\n/**\n * Fetch the `EpochRewards` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarEpochRewards(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarEpochRewards> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_EPOCH_REWARDS_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarEpochRewardsDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getBooleanDecoder,\n    getBooleanEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport type { Epoch, Slot } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_EPOCH_SCHEDULE_ADDRESS } from './sysvar';\n\ntype SysvarEpochScheduleSize = 33;\n\n/**\n * Includes the number of slots per epoch, timing of leader schedule selection, and information\n * about epoch warm-up time.\n */\nexport type SysvarEpochSchedule = Readonly<{\n    /**\n     * First normal-length epoch after the warmup period,\n     * log2(slotsPerEpoch) - log2(MINIMUM_SLOTS_PER_EPOCH)\n     */\n    firstNormalEpoch: Epoch;\n    /**\n     * The first slot after the warmup period, MINIMUM_SLOTS_PER_EPOCH * (2^(firstNormalEpoch) - 1)\n     */\n    firstNormalSlot: Slot;\n    /**\n     * A number of slots before beginning of an epoch to calculate a leader schedule for that\n     * epoch.\n     */\n    leaderScheduleSlotOffset: bigint;\n    /** The maximum number of slots in each epoch */\n    slotsPerEpoch: bigint;\n    /** Whether epochs start short and grow */\n    warmup: boolean;\n}>;\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarEpochSchedule} to a byte array\n * representing the `EpochSchedule` sysvar's account data.\n */\nexport function getSysvarEpochScheduleEncoder(): FixedSizeEncoder<SysvarEpochSchedule, SysvarEpochScheduleSize> {\n    return getStructEncoder([\n        ['slotsPerEpoch', getU64Encoder()],\n        ['leaderScheduleSlotOffset', getU64Encoder()],\n        ['warmup', getBooleanEncoder()],\n        ['firstNormalEpoch', getU64Encoder()],\n        ['firstNormalSlot', getU64Encoder()],\n    ]) as FixedSizeEncoder<SysvarEpochSchedule, SysvarEpochScheduleSize>;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `EpochSchedule`\n * sysvar's account data to a {@link SysvarEpochSchedule}.\n */\nexport function getSysvarEpochScheduleDecoder(): FixedSizeDecoder<SysvarEpochSchedule, SysvarEpochScheduleSize> {\n    return getStructDecoder([\n        ['slotsPerEpoch', getU64Decoder()],\n        ['leaderScheduleSlotOffset', getU64Decoder()],\n        ['warmup', getBooleanDecoder()],\n        ['firstNormalEpoch', getU64Decoder()],\n        ['firstNormalSlot', getU64Decoder()],\n    ]) as FixedSizeDecoder<SysvarEpochSchedule, SysvarEpochScheduleSize>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarEpochSchedule}\n *\n * @see {@link getSysvarEpochScheduleDecoder}\n * @see {@link getSysvarEpochScheduleEncoder}\n */\nexport function getSysvarEpochScheduleCodec(): FixedSizeCodec<\n    SysvarEpochSchedule,\n    SysvarEpochSchedule,\n    SysvarEpochScheduleSize\n> {\n    return combineCodec(getSysvarEpochScheduleEncoder(), getSysvarEpochScheduleDecoder());\n}\n\n/**\n * Fetches the `EpochSchedule` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarEpochSchedule(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarEpochSchedule> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_EPOCH_SCHEDULE_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarEpochScheduleDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport type { Slot } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_LAST_RESTART_SLOT_ADDRESS } from './sysvar';\n\ntype SysvarLastRestartSlotSize = 8;\n\n/**\n * Information about the last restart slot (hard fork).\n *\n * The `LastRestartSlot` sysvar provides access to the last restart slot kept in the bank fork for\n * the slot on the fork that executes the current transaction. In case there was no fork it returns\n * `0`.\n */\nexport type SysvarLastRestartSlot = Readonly<{\n    /** The last restart {@link Slot} */\n    lastRestartSlot: Slot;\n}>;\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarLastRestartSlot} to a byte array\n * representing the `LastRestartSlot` sysvar's account data.\n */\nexport function getSysvarLastRestartSlotEncoder(): FixedSizeEncoder<SysvarLastRestartSlot, SysvarLastRestartSlotSize> {\n    return getStructEncoder([['lastRestartSlot', getU64Encoder()]]) as FixedSizeEncoder<\n        SysvarLastRestartSlot,\n        SysvarLastRestartSlotSize\n    >;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `LastRestartSlot`\n * sysvar's account data to a {@link SysvarLastRestartSlot}.\n */\nexport function getSysvarLastRestartSlotDecoder(): FixedSizeDecoder<SysvarLastRestartSlot, SysvarLastRestartSlotSize> {\n    return getStructDecoder([['lastRestartSlot', getU64Decoder()]]) as FixedSizeDecoder<\n        SysvarLastRestartSlot,\n        SysvarLastRestartSlotSize\n    >;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarLastRestartSlot}\n *\n * @see {@link getSysvarLastRestartSlotDecoder}\n * @see {@link getSysvarLastRestartSlotEncoder}\n */\nexport function getSysvarLastRestartSlotCodec(): FixedSizeCodec<\n    SysvarLastRestartSlot,\n    SysvarLastRestartSlot,\n    SysvarLastRestartSlotSize\n> {\n    return combineCodec(getSysvarLastRestartSlotEncoder(), getSysvarLastRestartSlotDecoder());\n}\n\n/**\n * Fetches the `LastRestartSlot` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarLastRestartSlot(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarLastRestartSlot> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_LAST_RESTART_SLOT_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarLastRestartSlotDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    getArrayDecoder,\n    getArrayEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    type VariableSizeCodec,\n    type VariableSizeDecoder,\n    type VariableSizeEncoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport {\n    type Blockhash,\n    getBlockhashDecoder,\n    getBlockhashEncoder,\n    getDefaultLamportsDecoder,\n    getDefaultLamportsEncoder,\n    type Lam<PERSON>,\n} from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_RECENT_BLOCKHASHES_ADDRESS } from './sysvar';\n\ntype FeeCalculator = Readonly<{\n    /**\n     * The current cost of a signature.\n     *\n     * This amount may increase/decrease over time based on cluster processing load\n     */\n    lamportsPerSignature: Lamports;\n}>;\ntype Entry = Readonly<{\n    blockhash: Blockhash;\n    feeCalculator: FeeCalculator;\n}>;\n\n/**\n * Information about recent blocks and their fee calculators.\n *\n * @deprecated Transaction fees should be determined with the\n * {@link GetFeeForMessageApi.getFeeForMessage} RPC method. For additional context see the\n * [Comprehensive Compute Fees proposal](https://docs.anza.xyz/proposals/comprehensive-compute-fees/).\n */\nexport type SysvarRecentBlockhashes = Entry[];\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarRecentBlockhashes} to a byte array\n * representing the `RecentBlockhashes` sysvar's account data.\n *\n * @deprecated Transaction fees should be determined with the\n * {@link GetFeeForMessageApi.getFeeForMessage} RPC method. For additional context see the\n * [Comprehensive Compute Fees proposal](https://docs.anza.xyz/proposals/comprehensive-compute-fees/).\n */\nexport function getSysvarRecentBlockhashesEncoder(): VariableSizeEncoder<SysvarRecentBlockhashes> {\n    return getArrayEncoder(\n        getStructEncoder([\n            ['blockhash', getBlockhashEncoder()],\n            ['feeCalculator', getStructEncoder([['lamportsPerSignature', getDefaultLamportsEncoder()]])],\n        ]),\n    );\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `RecentBlockhashes`\n * sysvar's account data to a {@link SysvarRecentBlockhashes}.\n *\n * @deprecated Transaction fees should be determined with the\n * {@link GetFeeForMessageApi.getFeeForMessage} RPC method. For additional context see the\n * [Comprehensive Compute Fees proposal](https://docs.anza.xyz/proposals/comprehensive-compute-fees/).\n */\nexport function getSysvarRecentBlockhashesDecoder(): VariableSizeDecoder<SysvarRecentBlockhashes> {\n    return getArrayDecoder(\n        getStructDecoder([\n            ['blockhash', getBlockhashDecoder()],\n            ['feeCalculator', getStructDecoder([['lamportsPerSignature', getDefaultLamportsDecoder()]])],\n        ]),\n    );\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarRecentBlockhashes}\n *\n * @deprecated Transaction fees should be determined with the\n * {@link GetFeeForMessageApi.getFeeForMessage} RPC method. For additional context see the\n * [Comprehensive Compute Fees proposal](https://docs.anza.xyz/proposals/comprehensive-compute-fees/).\n *\n * @see {@link getSysvarRecentBlockhashesDecoder}\n * @see {@link getSysvarRecentBlockhashesEncoder}\n */\nexport function getSysvarRecentBlockhashesCodec(): VariableSizeCodec<SysvarRecentBlockhashes> {\n    return combineCodec(getSysvarRecentBlockhashesEncoder(), getSysvarRecentBlockhashesDecoder());\n}\n\n/**\n * Fetches the `RecentBlockhashes` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n *\n * @deprecated Transaction fees should be determined with the\n * {@link GetFeeForMessageApi.getFeeForMessage} RPC method. For additional context see the\n * [Comprehensive Compute Fees proposal](https://docs.anza.xyz/proposals/comprehensive-compute-fees/).\n */\nexport async function fetchSysvarRecentBlockhashes(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarRecentBlockhashes> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_RECENT_BLOCKHASHES_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarRecentBlockhashesDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getF64Decoder,\n    getF64Encoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU8Decoder,\n    getU8Encoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport {\n    F64UnsafeSeeDocumentation,\n    getDefaultLamportsDecoder,\n    getDefaultLamportsEncoder,\n    type Lamports,\n} from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_RENT_ADDRESS } from './sysvar';\n\ntype SysvarRentSize = 17;\n\n/**\n * Configuration for network rent.\n */\nexport type SysvarRent = Readonly<{\n    /**\n     * The percentage of collected rent that is burned.\n     *\n     * Valid values are in the range [0, 100]. The remaining percentage is distributed to\n     * validators.\n     */\n    burnPercent: number;\n    /** Amount of time (in years) a balance must include rent for the account to be rent exempt */\n    exemptionThreshold: F64UnsafeSeeDocumentation;\n    /** Rental rate in {@link Lamports}/byte-year. */\n    lamportsPerByteYear: Lamports;\n}>;\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarRent} to a byte array representing\n * the `Rent` sysvar's account data.\n */\nexport function getSysvarRentEncoder(): FixedSizeEncoder<SysvarRent, SysvarRentSize> {\n    return getStructEncoder([\n        ['lamportsPerByteYear', getDefaultLamportsEncoder()],\n        ['exemptionThreshold', getF64Encoder()],\n        ['burnPercent', getU8Encoder()],\n    ]) as FixedSizeEncoder<SysvarRent, SysvarRentSize>;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `Rent` sysvar's\n * account data to a {@link SysvarRent}.\n */\nexport function getSysvarRentDecoder(): FixedSizeDecoder<SysvarRent, SysvarRentSize> {\n    return getStructDecoder([\n        ['lamportsPerByteYear', getDefaultLamportsDecoder()],\n        ['exemptionThreshold', getF64Decoder()],\n        ['burnPercent', getU8Decoder()],\n    ]) as FixedSizeDecoder<SysvarRent, SysvarRentSize>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarRent}\n *\n * @see {@link getSysvarRentDecoder}\n * @see {@link getSysvarRentEncoder}\n */\nexport function getSysvarRentCodec(): FixedSizeCodec<SysvarRent, SysvarRent, SysvarRentSize> {\n    return combineCodec(getSysvarRentEncoder(), getSysvarRentDecoder());\n}\n\n/**\n * Fetches the `Rent` sysvar account using any RPC that supports the {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarRent(rpc: Rpc<GetAccountInfoApi>, config?: FetchAccountConfig): Promise<SysvarRent> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_RENT_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarRentDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    getArrayDecoder,\n    getArrayEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n    type VariableSizeCodec,\n    type VariableSizeDecoder,\n    type VariableSizeEncoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport { type Blockhash, getBlockhashDecoder, getBlockhashEncoder, type Slot } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_SLOT_HASHES_ADDRESS } from './sysvar';\n\ntype Entry = Readonly<{\n    hash: Blockhash;\n    slot: Slot;\n}>;\n\n/** The most recent hashes of a slot's parent banks. */\nexport type SysvarSlotHashes = Entry[];\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarSlotHashes} to a byte array\n * representing the `SlotHashes` sysvar's account data.\n */\nexport function getSysvarSlotHashesEncoder(): VariableSizeEncoder<SysvarSlotHashes> {\n    return getArrayEncoder(\n        getStructEncoder([\n            ['slot', getU64Encoder()],\n            ['hash', getBlockhashEncoder()],\n        ]),\n    );\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `SlotHashes` sysvar's\n * account data to a {@link SysvarSlotHashes}.\n */\nexport function getSysvarSlotHashesDecoder(): VariableSizeDecoder<SysvarSlotHashes> {\n    return getArrayDecoder(\n        getStructDecoder([\n            ['slot', getU64Decoder()],\n            ['hash', getBlockhashDecoder()],\n        ]),\n    );\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarSlotHashes}\n *\n * @see {@link getSysvarSlotHashesDecoder}\n * @see {@link getSysvarSlotHashesEncoder}\n */\nexport function getSysvarSlotHashesCodec(): VariableSizeCodec<SysvarSlotHashes> {\n    return combineCodec(getSysvarSlotHashesEncoder(), getSysvarSlotHashesDecoder());\n}\n\n/**\n * Fetches the `SlotHashes` sysvar account using any RPC that supports the {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarSlotHashes(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarSlotHashes> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_SLOT_HASHES_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarSlotHashesDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    type FixedSizeCodec,\n    type FixedSizeDecoder,\n    type FixedSizeEncoder,\n    getArrayCodec,\n    getU64<PERSON>odec,\n    getU64Decoder,\n    getU64Encoder,\n    ReadonlyUint8Array,\n} from '@solana/codecs';\nimport {\n    SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS,\n    SolanaError,\n} from '@solana/errors';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport type { Slot } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_SLOT_HISTORY_ADDRESS } from './sysvar';\n\nconst BITVEC_DISCRIMINATOR = 1;\n// Max number of bits in the bitvector.\n// The Solana SDK defines a constant `MAX_ENTRIES` representing the maximum\n// number of bits that can be represented by the bitvector in the `SlotHistory`\n// sysvar. This value is 1024 * 1024 = 1_048_576.\n// See https://github.com/anza-xyz/agave/blob/e0203f22dc83cb792fa97f91dbe6e924cbd08af1/sdk/program/src/slot_history.rs#L43\nconst BITVEC_NUM_BITS = 1024 * 1024;\n// The length of the bitvector in blocks.\n// At 64 bits per block, this is 1024 * 1024 / 64 = 16_384.\nconst BITVEC_LENGTH = BITVEC_NUM_BITS / 64;\n\nconst SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE =\n    1 + // Discriminator\n    8 + // bitvector length (u64)\n    BITVEC_LENGTH * 8 +\n    8 + // Number of bits (u64)\n    8; // Next slot (u64)\n\nlet memoizedU64Encoder: FixedSizeEncoder<bigint, 8> | undefined;\nlet memoizedU64Decoder: FixedSizeDecoder<bigint, 8> | undefined;\nlet memoizedU64ArrayEncoder: FixedSizeEncoder<bigint[]> | undefined;\nlet memoizedU64ArrayDecoder: FixedSizeDecoder<bigint[]> | undefined;\n\nfunction getMemoizedU64Encoder(): FixedSizeEncoder<bigint, 8> {\n    if (!memoizedU64Encoder) memoizedU64Encoder = getU64Encoder();\n    return memoizedU64Encoder;\n}\nfunction getMemoizedU64Decoder(): FixedSizeDecoder<bigint, 8> {\n    if (!memoizedU64Decoder) memoizedU64Decoder = getU64Decoder();\n    return memoizedU64Decoder;\n}\nfunction getMemoizedU64ArrayEncoder(): FixedSizeEncoder<bigint[], typeof BITVEC_LENGTH> {\n    if (!memoizedU64ArrayEncoder) memoizedU64ArrayEncoder = getArrayCodec(getU64Codec(), { size: BITVEC_LENGTH });\n    return memoizedU64ArrayEncoder;\n}\nfunction getMemoizedU64ArrayDecoder(): FixedSizeDecoder<bigint[], typeof BITVEC_LENGTH> {\n    if (!memoizedU64ArrayDecoder) memoizedU64ArrayDecoder = getArrayCodec(getU64Codec(), { size: BITVEC_LENGTH });\n    return memoizedU64ArrayDecoder;\n}\n\ntype SysvarSlotHistorySize = typeof SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE;\n\n/** A bitvector of slots present over the last epoch. */\nexport type SysvarSlotHistory = {\n    /**\n     * A vector of 64-bit numbers which, when their bits are strung together, represent a record of\n     * non-skipped slots.\n     *\n     * The bit in position (slot % MAX_ENTRIES) is 0 if the slot was skipped and 1 otherwise, valid\n     * only when the candidate slot is less than `nextSlot` and greater than or equal to\n     * `MAX_ENTRIES - nextSlot`.\n     */\n    bits: bigint[];\n    /** The number of the slot one newer than tracked by the bitvector */\n    nextSlot: Slot;\n};\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarSlotHistory} to a byte array\n * representing the `SlotHistory` sysvar's account data.\n */\nexport function getSysvarSlotHistoryEncoder(): FixedSizeEncoder<SysvarSlotHistory, SysvarSlotHistorySize> {\n    return createEncoder({\n        fixedSize: SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE,\n        write: (value: SysvarSlotHistory, bytes, offset) => {\n            // First byte is the bitvector discriminator.\n            bytes.set([BITVEC_DISCRIMINATOR], offset);\n            offset += 1;\n            // Next 8 bytes are the bitvector length.\n            getMemoizedU64Encoder().write(BigInt(BITVEC_LENGTH), bytes, offset);\n            offset += 8;\n            // Next `BITVEC_LENGTH` bytes are the bitvector.\n            // Any missing bits are assumed to be 0.\n            getMemoizedU64ArrayEncoder().write(value.bits, bytes, offset);\n            offset += BITVEC_LENGTH * 8;\n            // Next 8 bytes are the number of bits.\n            getMemoizedU64Encoder().write(BigInt(BITVEC_NUM_BITS), bytes, offset);\n            offset += 8;\n            // Next 8 bytes are the next slot.\n            getMemoizedU64Encoder().write(value.nextSlot, bytes, offset);\n            offset += 8;\n            return offset;\n        },\n    });\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `SlotHistory` sysvar's\n * account data to a {@link SysvarSlotHistory}.\n */\nexport function getSysvarSlotHistoryDecoder(): FixedSizeDecoder<SysvarSlotHistory, SysvarSlotHistorySize> {\n    return createDecoder({\n        fixedSize: SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE,\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            // Byte length should be exact.\n            if (bytes.length != SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH, {\n                    actual: bytes.length,\n                    expected: SLOT_HISTORY_ACCOUNT_DATA_STATIC_SIZE,\n                });\n            }\n            // First byte is the bitvector discriminator.\n            const discriminator = bytes[offset];\n            offset += 1;\n            if (discriminator !== BITVEC_DISCRIMINATOR) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE, {\n                    actual: discriminator,\n                    expected: BITVEC_DISCRIMINATOR,\n                });\n            }\n            // Next 8 bytes are the bitvector length.\n            const bitVecLength = getMemoizedU64Decoder().read(bytes, offset)[0];\n            offset += 8;\n            if (bitVecLength !== BigInt(BITVEC_LENGTH)) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, {\n                    actual: bitVecLength,\n                    codecDescription: 'SysvarSlotHistoryCodec',\n                    expected: BITVEC_LENGTH,\n                });\n            }\n            // Next `BITVEC_LENGTH` bytes are the bitvector.\n            const bits = getMemoizedU64ArrayDecoder().read(bytes, offset)[0];\n            offset += BITVEC_LENGTH * 8;\n            // Next 8 bytes are the number of bits.\n            const numBits = getMemoizedU64Decoder().read(bytes, offset)[0];\n            offset += 8;\n            if (numBits !== BigInt(BITVEC_NUM_BITS)) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, {\n                    actual: numBits,\n                    codecDescription: 'SysvarSlotHistoryCodec',\n                    expected: BITVEC_NUM_BITS,\n                });\n            }\n            // Next 8 bytes are the next slot.\n            const nextSlot = getMemoizedU64Decoder().read(bytes, offset)[0];\n            offset += 8;\n            return [\n                {\n                    bits,\n                    nextSlot,\n                },\n                offset,\n            ];\n        },\n    });\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarSlotHistory}\n *\n * @see {@link getSysvarSlotHistoryDecoder}\n * @see {@link getSysvarSlotHistoryEncoder}\n */\nexport function getSysvarSlotHistoryCodec(): FixedSizeCodec<\n    SysvarSlotHistory,\n    SysvarSlotHistory,\n    SysvarSlotHistorySize\n> {\n    return combineCodec(getSysvarSlotHistoryEncoder(), getSysvarSlotHistoryDecoder());\n}\n\n/**\n * Fetches the `SlotHistory` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarSlotHistory(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarSlotHistory> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_SLOT_HISTORY_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarSlotHistoryDecoder());\n    return decoded.data;\n}\n", "import { assertAccountExists, decodeAccount, type FetchAccountConfig } from '@solana/accounts';\nimport {\n    combineCodec,\n    getArrayDecoder,\n    getArrayEncoder,\n    getStructDecoder,\n    getStructEncoder,\n    getU64Decoder,\n    getU64Encoder,\n    type VariableSizeCodec,\n    type VariableSizeDecoder,\n    type VariableSizeEncoder,\n} from '@solana/codecs';\nimport type { GetAccountInfoApi } from '@solana/rpc-api';\nimport type { Rpc } from '@solana/rpc-spec';\nimport { Epoch, getDefaultLamportsDecoder, getDefaultLamportsEncoder, type Lamports } from '@solana/rpc-types';\n\nimport { fetchEncodedSysvarAccount, SYSVAR_STAKE_HISTORY_ADDRESS } from './sysvar';\n\ntype Entry = Readonly<{\n    /** The epoch to which this stake history entry pertains */\n    epoch: Epoch;\n    stakeHistory: Readonly<{\n        /**\n         * Sum of portion of stakes requested to be warmed up, but not fully activated yet, in\n         * {@link Lamports}\n         */\n        activating: Lamports;\n        /**\n         * Sum of portion of stakes requested to be cooled down, but not fully deactivated yet, in\n         * {@link Lamports}\n         */\n        deactivating: Lamports;\n        /** Effective stake at this epoch, in {@link Lamports} */\n        effective: Lamports;\n    }>;\n}>;\n\n/** History of stake activations and de-activations. */\nexport type SysvarStakeHistory = Entry[];\n\n/**\n * Returns an encoder that you can use to encode a {@link SysvarStakeHistory} to a byte array\n * representing the `StakeHistory` sysvar's account data.\n */\nexport function getSysvarStakeHistoryEncoder(): VariableSizeEncoder<SysvarStakeHistory> {\n    return getArrayEncoder(\n        getStructEncoder([\n            ['epoch', getU64Encoder()],\n            [\n                'stakeHistory',\n                getStructEncoder([\n                    ['effective', getDefaultLamportsEncoder()],\n                    ['activating', getDefaultLamportsEncoder()],\n                    ['deactivating', getDefaultLamportsEncoder()],\n                ]),\n            ],\n        ]),\n        { size: getU64Encoder() },\n    );\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing the `StakeHistory`\n * sysvar's account data to a {@link SysvarStakeHistory}.\n */\nexport function getSysvarStakeHistoryDecoder(): VariableSizeDecoder<SysvarStakeHistory> {\n    return getArrayDecoder(\n        getStructDecoder([\n            ['epoch', getU64Decoder()],\n            [\n                'stakeHistory',\n                getStructDecoder([\n                    ['effective', getDefaultLamportsDecoder()],\n                    ['activating', getDefaultLamportsDecoder()],\n                    ['deactivating', getDefaultLamportsDecoder()],\n                ]),\n            ],\n        ]),\n        { size: getU64Decoder() },\n    );\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link SysvarStakeHistory}\n *\n * @see {@link getSysvarStakeHistoryDecoder}\n * @see {@link getSysvarStakeHistoryEncoder}\n */\nexport function getSysvarStakeHistoryCodec(): VariableSizeCodec<SysvarStakeHistory> {\n    return combineCodec(getSysvarStakeHistoryEncoder(), getSysvarStakeHistoryDecoder());\n}\n\n/**\n * Fetches the `StakeHistory` sysvar account using any RPC that supports the\n * {@link GetAccountInfoApi}.\n */\nexport async function fetchSysvarStakeHistory(\n    rpc: Rpc<GetAccountInfoApi>,\n    config?: FetchAccountConfig,\n): Promise<SysvarStakeHistory> {\n    const account = await fetchEncodedSysvarAccount(rpc, SYSVAR_STAKE_HISTORY_ADDRESS, config);\n    assertAccountExists(account);\n    const decoded = decodeAccount(account, getSysvarStakeHistoryDecoder());\n    return decoded.data;\n}\n"]}