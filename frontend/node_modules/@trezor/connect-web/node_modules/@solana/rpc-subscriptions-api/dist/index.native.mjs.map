{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAiDA,SAAS,yCACL,MACyB,EAAA;AACzB,EAAM,MAAA,kBAAA,GAAqB,yCAAyC,MAAM,CAAA;AAC1E,EAAA,MAAM,sBAAsB,sDAAuD,CAAA;AAAA,IAC/E,wBAAwB,yBAA0B;AAAA,GACrD,CAAA;AACD,EAAA,OAAO,yBAAgC,CAAA;AAAA,IACnC,YAAa,CAAA,EAAE,OAAS,EAAA,GAAG,MAAQ,EAAA;AAC/B,MAAA,OAAO,gCAAiC,CAAA;AAAA,QACpC,GAAG,IAAA;AAAA,QACH,mBAAA;AAAA,QACA,gBAAA,EAAkB,EAAE,GAAG,OAAS,EAAA,UAAA,EAAY,QAAQ,UAAW,CAAA,OAAA,CAAQ,gBAAkB,EAAA,WAAW,CAAE,EAAA;AAAA,QACtG,qBAAuB,EAAA,OAAA,CAAQ,UAAW,CAAA,OAAA,CAAQ,kBAAkB,aAAa;AAAA,OACpF,CAAA;AAAA,KACL;AAAA,IACA;AAAA,GACH,CAAA;AACL;AAEO,SAAS,gCACZ,MACyB,EAAA;AACzB,EAAA,OAAO,yCAA+C,MAAM,CAAA;AAChE;AAEO,SAAS,yCAAyC,MAAiB,EAAA;AACtE,EAAO,OAAA,wCAAA;AAAA,IACH;AAAA,GACJ;AACJ;AAEA,IAAI,gBAAA;AAQJ,SAAS,yBAEP,GAAA;AACE,EAAA,IAAI,CAAC,gBAAkB,EAAA;AACnB,IAAmB,gBAAA,GAAA;AAAA,MACf,oBAAA,EAAsB,0BAA0B,GAAI,CAAA,CAAA,CAAA,KAAK,CAAC,OAAS,EAAA,GAAG,CAAC,CAAC,CAAA;AAAA,MACxE,kBAAoB,EAAA;AAAA,QAChB;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,kBAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,kBAAA;AAAA,UACA,gBAAA;AAAA,UACA,eAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,mBAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,mBAAA;AAAA,UACA,gBAAA;AAAA,UACA,eAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA,CAAC,SAAS,OAAS,EAAA,cAAA,EAAgB,kBAAkB,MAAQ,EAAA,SAAA,EAAW,kBAAkB,YAAY,CAAA;AAAA,QACtG;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,mBAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,mBAAA;AAAA,UACA,gBAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,MAAA;AAAA,UACA,mBAAA;AAAA,UACA,gBAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,UAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,qBAAA;AAAA,UACA,gBAAA;AAAA,UACA,iBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,qBAAA;AAAA,UACA,gBAAA;AAAA,UACA,iBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,UAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,QAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,QAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA;AAAA,UACI,OAAA;AAAA,UACA,OAAA;AAAA,UACA,cAAA;AAAA,UACA,gBAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,QAAA;AAAA,UACA;AAAA,SACJ;AAAA,QACA,CAAC,OAAA,EAAS,OAAS,EAAA,SAAA,EAAW,kBAAkB,YAAY;AAAA,OAChE;AAAA,MACA,oBAAA,EAAsB,yBAA0B,CAAA,OAAA,CAAQ,CAAK,CAAA,KAAA;AAAA,QACzD,CAAC,OAAA,EAAS,gBAAkB,EAAA,SAAA,EAAW,GAAG,CAAC,CAAA;AAAA,QAC3C,CAAC,gBAAA,EAAkB,SAAW,EAAA,GAAG,CAAC;AAAA,OACrC;AAAA,KACL;AAAA;AAEJ,EAAO,OAAA,gBAAA;AACX", "file": "index.native.mjs", "sourcesContent": ["import {\n    createRpcSubscriptionsApi,\n    executeRpcPubSubSubscriptionPlan,\n    RpcSubscriptionsApi,\n    RpcSubscriptionsApiMethods,\n} from '@solana/rpc-subscriptions-spec';\nimport {\n    AllowedNumericKeypaths,\n    getDefaultRequestTransformerForSolanaRpc,\n    getDefaultResponseTransformerForSolanaRpcSubscriptions,\n    jsonParsedAccountsConfigs,\n    KEYPATH_WILDCARD,\n    RequestTransformerConfig,\n} from '@solana/rpc-transformers';\n\nimport { AccountNotificationsApi } from './account-notifications';\nimport { BlockNotificationsApi } from './block-notifications';\nimport { LogsNotificationsApi } from './logs-notifications';\nimport { ProgramNotificationsApi } from './program-notifications';\nimport { RootNotificationsApi } from './root-notifications';\nimport { SignatureNotificationsApi } from './signature-notifications';\nimport { SlotNotificationsApi } from './slot-notifications';\nimport { SlotsUpdatesNotificationsApi } from './slots-updates-notifications';\nimport { VoteNotificationsApi } from './vote-notifications';\n\nexport type SolanaRpcSubscriptionsApi = AccountNotificationsApi &\n    LogsNotificationsApi &\n    ProgramNotificationsApi &\n    RootNotificationsApi &\n    SignatureNotificationsApi &\n    SlotNotificationsApi;\nexport type SolanaRpcSubscriptionsApiUnstable = BlockNotificationsApi &\n    SlotsUpdatesNotificationsApi &\n    VoteNotificationsApi;\n\nexport type {\n    AccountNotificationsApi,\n    BlockNotificationsApi,\n    LogsNotificationsApi,\n    ProgramNotificationsApi,\n    RootNotificationsApi,\n    SignatureNotificationsApi,\n    SlotNotificationsApi,\n    SlotsUpdatesNotificationsApi,\n    VoteNotificationsApi,\n};\n\ntype Config = RequestTransformerConfig;\n\nfunction createSolanaRpcSubscriptionsApi_INTERNAL<TApi extends RpcSubscriptionsApiMethods>(\n    config?: Config,\n): RpcSubscriptionsApi<TApi> {\n    const requestTransformer = getDefaultRequestTransformerForSolanaRpc(config);\n    const responseTransformer = getDefaultResponseTransformerForSolanaRpcSubscriptions({\n        allowedNumericKeyPaths: getAllowedNumericKeypaths(),\n    });\n    return createRpcSubscriptionsApi<TApi>({\n        planExecutor({ request, ...rest }) {\n            return executeRpcPubSubSubscriptionPlan({\n                ...rest,\n                responseTransformer,\n                subscribeRequest: { ...request, methodName: request.methodName.replace(/Notifications$/, 'Subscribe') },\n                unsubscribeMethodName: request.methodName.replace(/Notifications$/, 'Unsubscribe'),\n            });\n        },\n        requestTransformer,\n    });\n}\n\nexport function createSolanaRpcSubscriptionsApi<TApi extends RpcSubscriptionsApiMethods = SolanaRpcSubscriptionsApi>(\n    config?: Config,\n): RpcSubscriptionsApi<TApi> {\n    return createSolanaRpcSubscriptionsApi_INTERNAL<TApi>(config);\n}\n\nexport function createSolanaRpcSubscriptionsApi_UNSTABLE(config?: Config) {\n    return createSolanaRpcSubscriptionsApi_INTERNAL<SolanaRpcSubscriptionsApi & SolanaRpcSubscriptionsApiUnstable>(\n        config,\n    );\n}\n\nlet memoizedKeypaths: AllowedNumericKeypaths<\n    RpcSubscriptionsApi<SolanaRpcSubscriptionsApi & SolanaRpcSubscriptionsApiUnstable>\n>;\n\n/**\n * These are keypaths at the end of which you will find a numeric value that should *not* be upcast\n * to a `bigint`. These are values that are legitimately defined as `u8` or `usize` on the backend.\n */\nfunction getAllowedNumericKeypaths(): AllowedNumericKeypaths<\n    RpcSubscriptionsApi<SolanaRpcSubscriptionsApi & SolanaRpcSubscriptionsApiUnstable>\n> {\n    if (!memoizedKeypaths) {\n        memoizedKeypaths = {\n            accountNotifications: jsonParsedAccountsConfigs.map(c => ['value', ...c]),\n            blockNotifications: [\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'preTokenBalances',\n                    KEYPATH_WILDCARD,\n                    'accountIndex',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'preTokenBalances',\n                    KEYPATH_WILDCARD,\n                    'uiTokenAmount',\n                    'decimals',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'postTokenBalances',\n                    KEYPATH_WILDCARD,\n                    'accountIndex',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'postTokenBalances',\n                    KEYPATH_WILDCARD,\n                    'uiTokenAmount',\n                    'decimals',\n                ],\n                ['value', 'block', 'transactions', KEYPATH_WILDCARD, 'meta', 'rewards', KEYPATH_WILDCARD, 'commission'],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'innerInstructions',\n                    KEYPATH_WILDCARD,\n                    'index',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'innerInstructions',\n                    KEYPATH_WILDCARD,\n                    'instructions',\n                    KEYPATH_WILDCARD,\n                    'programIdIndex',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'meta',\n                    'innerInstructions',\n                    KEYPATH_WILDCARD,\n                    'instructions',\n                    KEYPATH_WILDCARD,\n                    'accounts',\n                    KEYPATH_WILDCARD,\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'addressTableLookups',\n                    KEYPATH_WILDCARD,\n                    'writableIndexes',\n                    KEYPATH_WILDCARD,\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'addressTableLookups',\n                    KEYPATH_WILDCARD,\n                    'readonlyIndexes',\n                    KEYPATH_WILDCARD,\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'instructions',\n                    KEYPATH_WILDCARD,\n                    'programIdIndex',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'instructions',\n                    KEYPATH_WILDCARD,\n                    'accounts',\n                    KEYPATH_WILDCARD,\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'header',\n                    'numReadonlySignedAccounts',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'header',\n                    'numReadonlyUnsignedAccounts',\n                ],\n                [\n                    'value',\n                    'block',\n                    'transactions',\n                    KEYPATH_WILDCARD,\n                    'transaction',\n                    'message',\n                    'header',\n                    'numRequiredSignatures',\n                ],\n                ['value', 'block', 'rewards', KEYPATH_WILDCARD, 'commission'],\n            ],\n            programNotifications: jsonParsedAccountsConfigs.flatMap(c => [\n                ['value', KEYPATH_WILDCARD, 'account', ...c],\n                [KEYPATH_WILDCARD, 'account', ...c],\n            ]),\n        };\n    }\n    return memoizedKeypaths;\n}\n"]}