{"version": 3, "file": "block-notifications.d.ts", "sourceRoot": "", "sources": ["../../src/block-notifications.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAC5C,OAAO,KAAK,EACR,kBAAkB,EAClB,SAAS,EACT,UAAU,EACV,MAAM,EACN,IAAI,EACJ,iBAAiB,EACjB,sBAAsB,EACtB,wBAAwB,EACxB,wBAAwB,EACxB,sBAAsB,EACtB,4BAA4B,EAC5B,aAAa,EAChB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAIvE,KAAK,kCAAkC,GAAG,QAAQ,CAAC;IAC/C;;;;;OAKG;IACH,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IACnB,IAAI,EAAE,IAAI,CAAC;CACd,CAAC,CAAC;AAEH,KAAK,mCAAmC,GAAG,QAAQ,CAAC;IAChD,8CAA8C;IAC9C,WAAW,EAAE,MAAM,CAAC;IACpB,mDAAmD;IACnD,SAAS,EAAE,aAAa,CAAC;IACzB,kCAAkC;IAClC,SAAS,EAAE,SAAS,CAAC;IACrB,4CAA4C;IAC5C,UAAU,EAAE,IAAI,CAAC;IACjB,2CAA2C;IAC3C,iBAAiB,EAAE,SAAS,CAAC;CAChC,CAAC,CAAC;AAEH,KAAK,8CAA8C,GAAG,QAAQ,CAAC;IAC3D,0BAA0B;IAC1B,OAAO,EAAE,SAAS,MAAM,EAAE,CAAC;CAC9B,CAAC,CAAC;AAEH,KAAK,iDAAiD,GAAG,QAAQ,CAAC;IAC9D,+DAA+D;IAC/D,UAAU,EAAE,SAAS,kBAAkB,EAAE,CAAC;CAC7C,CAAC,CAAC;AAEH,KAAK,mDAAmD,CAAC,YAAY,IAAI,QAAQ,CAAC;IAC9E,YAAY,EAAE,SAAS,YAAY,EAAE,CAAC;CACzC,CAAC,CAAC;AAIH,KAAK,wBAAwB,GACvB,KAAK,GACL;IACI;;;;OAIG;IACH,wBAAwB,EAAE,OAAO,CAAC;CACrC,CAAC;AAER,KAAK,8BAA8B,GAAG,QAAQ,CAAC;IAC3C;;;;;;;OAOG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAC3C;;;;;;;;;;;;;;;;;OAiBG;IACH,QAAQ,CAAC,EAAE,0BAA0B,CAAC;IACtC;;;;;;;;;;OAUG;IACH,8BAA8B,CAAC,EAAE,gDAAgD,CAAC;IAClF;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;;;;;OAUG;IACH,kBAAkB,CAAC,EAAE,uCAAuC,CAAC;CAChE,CAAC,CAAC;AAEH,KAAK,0BAA0B,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,YAAY,CAAC;AAC9E,KAAK,uCAAuC,GAAG,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,YAAY,CAAC;AAE3F,KAAK,gDAAgD,GAAG,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;AAE9F,MAAM,MAAM,qBAAqB,GAAG;IAChC;;;;;;;;;;;OAWG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,CAAC,EAAE,gDAAgD,CAAC;QAClF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EAAE,mCAAmC,GAAG,IAAI,CAAC;KACrD,CAAC,CACT,CAAC;IACF;;;;;;;;;;;OAWG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,CAAC,EAAE,gDAAgD,CAAC;QAClF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EAAE,CAAC,mCAAmC,GAAG,8CAA8C,CAAC,GAAG,IAAI,CAAC;KACxG,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,CAAC,EAAE,gDAAgD,CAAC;QAClF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,EAAE,YAAY,CAAC;KACpC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EAAE,CAAC,mCAAmC,GAAG,iDAAiD,CAAC,GAAG,IAAI,CAAC;KAC3G,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,CAAC,EAAE,gDAAgD,CAAC;QAClF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,EAAE,YAAY,CAAC;KACpC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,iDAAiD,CAAC,GACtD,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAC/C,sBAAsB,CAAC,gDAAgD,CAAC,CAC3E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAC/C,sBAAsB,CAAC,gDAAgD,CAAC,CAC3E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,0BAA0B,CAAC;QACtC,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,EAAE,UAAU,CAAC;KAClC,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAC/C,wBAAwB,CAAC,gDAAgD,CAAC,CAC7E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAC/C,wBAAwB,CAAC,gDAAgD,CAAC,CAC7E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAC/C,wBAAwB,CAAC,gDAAgD,CAAC,CAC7E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAC/C,wBAAwB,CAAC,gDAAgD,CAAC,CAC7E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,QAAQ,CAAC;QACnB,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,GACxF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAC/C,4BAA4B,CAAC,gDAAgD,CAAC,CACjF,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,GAC5F,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAC/C,4BAA4B,CAAC,gDAAgD,CAAC,CACjF,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,EAAE,YAAY,CAAC;QACvB,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,GAC5F,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAC/C,sBAAsB,CAAC,gDAAgD,CAAC,CAC3E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,KAAK,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,mDAAmD,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,EAAE,8BAA8B,GAClC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,8BAA8B,EAAE,gDAAgD,CAAC;QACjF,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAC/C,sBAAsB,CAAC,gDAAgD,CAAC,CAC3E,CAAC,GACN,IAAI,CAAC;KACd,CAAC,CACT,CAAC;IACF;;;;;;;;;;;;OAYG;IACH,kBAAkB,CACd,MAAM,EAAE,wBAAwB,EAEhC,MAAM,CAAC,EAAE,8BAA8B,GACnC,QAAQ,CAAC;QACL,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,WAAW,CAAC,EAAE,IAAI,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC/B,CAAC,GACP,iBAAiB,CAChB,kCAAkC,GAC9B,QAAQ,CAAC;QACL,KAAK,EACC,CAAC,mCAAmC,GAChC,8CAA8C,GAC9C,mDAAmD,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,GACtF,IAAI,CAAC;KACd,CAAC,CACT,CAAC;CACL,CAAC"}