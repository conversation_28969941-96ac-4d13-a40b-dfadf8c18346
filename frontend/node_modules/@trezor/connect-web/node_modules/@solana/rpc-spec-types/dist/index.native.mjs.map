{"version": 3, "sources": ["../src/parse-json-with-bigints.ts", "../src/rpc-message.ts", "../src/stringify-json-with-bigints.ts"], "names": ["unwrapBigIntValueObject", "wrapBigIntValueObject"], "mappings": ";AAKO,SAAS,qBAAqB,IAAuB,EAAA;AACxD,EAAA,OAAO,KAAK,KAAM,CAAA,+BAAA,CAAgC,IAAI,CAAG,EAAA,CAAC,GAAG,KAAU,KAAA;AACnE,IAAA,OAAO,mBAAoB,CAAA,KAAK,CAAI,GAAA,uBAAA,CAAwB,KAAK,CAAI,GAAA,KAAA;AAAA,GACxE,CAAA;AACL;AAEA,SAAS,gCAAgC,IAAsB,EAAA;AAC3D,EAAA,MAAM,MAAM,EAAC;AACb,EAAA,IAAI,OAAU,GAAA,KAAA;AACd,EAAA,KAAA,IAAS,EAAK,GAAA,CAAA,EAAG,EAAK,GAAA,IAAA,CAAK,QAAQ,EAAM,EAAA,EAAA;AACrC,IAAA,IAAI,SAAY,GAAA,KAAA;AAChB,IAAI,IAAA,IAAA,CAAK,EAAE,CAAA,KAAM,IAAM,EAAA;AACnB,MAAI,GAAA,CAAA,IAAA,CAAK,IAAK,CAAA,EAAA,EAAI,CAAC,CAAA;AACnB,MAAA,SAAA,GAAY,CAAC,SAAA;AAAA;AAEjB,IAAI,IAAA,IAAA,CAAK,EAAE,CAAA,KAAM,GAAK,EAAA;AAClB,MAAI,GAAA,CAAA,IAAA,CAAK,IAAK,CAAA,EAAE,CAAC,CAAA;AACjB,MAAA,IAAI,CAAC,SAAW,EAAA;AACZ,QAAA,OAAA,GAAU,CAAC,OAAA;AAAA;AAEf,MAAA;AAAA;AAEJ,IAAA,IAAI,CAAC,OAAS,EAAA;AACV,MAAM,MAAA,cAAA,GAAiB,aAAc,CAAA,IAAA,EAAM,EAAE,CAAA;AAC7C,MAAA,IAAI,gBAAgB,MAAQ,EAAA;AACxB,QAAA,EAAA,IAAM,eAAe,MAAS,GAAA,CAAA;AAE9B,QAAI,IAAA,cAAA,CAAe,KAAM,CAAA,UAAU,CAAG,EAAA;AAClC,UAAA,GAAA,CAAI,KAAK,cAAc,CAAA;AAAA,SACpB,MAAA;AACH,UAAI,GAAA,CAAA,IAAA,CAAK,qBAAsB,CAAA,cAAc,CAAC,CAAA;AAAA;AAElD,QAAA;AAAA;AACJ;AAEJ,IAAI,GAAA,CAAA,IAAA,CAAK,IAAK,CAAA,EAAE,CAAC,CAAA;AAAA;AAGrB,EAAO,OAAA,GAAA,CAAI,KAAK,EAAE,CAAA;AACtB;AAEA,SAAS,aAAA,CAAc,MAAc,EAA2B,EAAA;AAE5D,EAAA,MAAM,iBAAoB,GAAA,8CAAA;AAG1B,EAAA,IAAI,CAAC,IAAK,CAAA,EAAE,CAAG,EAAA,KAAA,CAAM,OAAO,CAAG,EAAA;AAC3B,IAAO,OAAA,IAAA;AAAA;AAIX,EAAA,MAAM,cAAc,IAAK,CAAA,KAAA,CAAM,EAAE,CAAA,CAAE,MAAM,iBAAiB,CAAA;AAC1D,EAAO,OAAA,WAAA,GAAc,WAAY,CAAA,CAAC,CAAI,GAAA,IAAA;AAC1C;AAQA,SAAS,sBAAsB,KAAuB,EAAA;AAClD,EAAA,OAAO,UAAU,KAAK,CAAA,EAAA,CAAA;AAC1B;AAEA,SAAS,uBAAA,CAAwB,EAAE,EAAA,EAAiC,EAAA;AAChE,EAAI,IAAA,EAAA,CAAG,KAAM,CAAA,MAAM,CAAG,EAAA;AAClB,IAAA,MAAM,CAAC,KAAO,EAAA,QAAQ,CAAI,GAAA,EAAA,CAAG,MAAM,MAAM,CAAA;AACzC,IAAA,OAAO,OAAO,KAAK,CAAA,GAAI,OAAO,EAAE,CAAA,IAAK,OAAO,QAAQ,CAAA;AAAA;AAExD,EAAA,OAAO,OAAO,EAAE,CAAA;AACpB;AAEA,SAAS,oBAAoB,KAA4C,EAAA;AACrE,EAAO,OAAA,CAAC,CAAC,KAAA,IAAS,OAAO,KAAA,KAAU,YAAY,IAAQ,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,EAAO,KAAA,QAAA;AACxF;;;AC9EA,IAAI,cAAiB,GAAA,EAAA;AACrB,SAAS,gBAA2B,GAAA;AAChC,EAAA,MAAM,EAAK,GAAA,cAAA;AACX,EAAA,cAAA,EAAA;AACA,EAAA,OAAO,GAAG,QAAS,EAAA;AACvB;AAOO,SAAS,iBAA0B,OAA8B,EAAA;AACpE,EAAO,OAAA;AAAA,IACH,IAAI,gBAAiB,EAAA;AAAA,IACrB,OAAS,EAAA,KAAA;AAAA,IACT,QAAQ,OAAQ,CAAA,UAAA;AAAA,IAChB,QAAQ,OAAQ,CAAA;AAAA,GACpB;AACJ;;;AClBO,SAAS,wBAAA,CAAyB,OAAgB,KAAiC,EAAA;AACtF,EAAOA,OAAAA,wBAAAA;AAAA,IACH,IAAK,CAAA,SAAA,CAAU,KAAO,EAAA,CAAC,CAAG,EAAA,CAAA,KAAO,OAAO,CAAA,KAAM,QAAWC,GAAAA,sBAAAA,CAAsB,CAAC,CAAA,GAAI,GAAI,KAAK;AAAA,GACjG;AACJ;AAQA,SAASA,uBAAsB,KAAkC,EAAA;AAC7D,EAAA,OAAO,EAAE,EAAA,EAAI,CAAG,EAAA,KAAK,CAAG,CAAA,EAAA;AAC5B;AAEA,SAASD,yBAAwB,KAAuB,EAAA;AACpD,EAAO,OAAA,KAAA,CAAM,OAAQ,CAAA,kCAAA,EAAoC,IAAI,CAAA;AACjE", "file": "index.native.mjs", "sourcesContent": ["/**\n * This function is a replacement for `JSON.parse` that can handle large\n * unsafe integers by parsing them as BigInts. It transforms every\n * numerical value into a BigInt without loss of precision.\n */\nexport function parseJsonWithBigInts(json: string): unknown {\n    return JSON.parse(wrapIntegersInBigIntValueObject(json), (_, value) => {\n        return isBigIntValueObject(value) ? unwrapBigIntValueObject(value) : value;\n    });\n}\n\nfunction wrapIntegersInBigIntValueObject(json: string): string {\n    const out = [];\n    let inQuote = false;\n    for (let ii = 0; ii < json.length; ii++) {\n        let isEscaped = false;\n        if (json[ii] === '\\\\') {\n            out.push(json[ii++]);\n            isEscaped = !isEscaped;\n        }\n        if (json[ii] === '\"') {\n            out.push(json[ii]);\n            if (!isEscaped) {\n                inQuote = !inQuote;\n            }\n            continue;\n        }\n        if (!inQuote) {\n            const consumedNumber = consumeNumber(json, ii);\n            if (consumedNumber?.length) {\n                ii += consumedNumber.length - 1;\n                // Don't wrap numbers that contain a decimal point or a negative exponent.\n                if (consumedNumber.match(/\\.|[eE]-/)) {\n                    out.push(consumedNumber);\n                } else {\n                    out.push(wrapBigIntValueObject(consumedNumber));\n                }\n                continue;\n            }\n        }\n        out.push(json[ii]);\n    }\n\n    return out.join('');\n}\n\nfunction consumeNumber(json: string, ii: number): string | null {\n    /** @see https://stackoverflow.com/a/13340826/11440277 */\n    const JSON_NUMBER_REGEX = /^-?(?:0|[1-9]\\d*)(?:\\.\\d+)?(?:[eE][+-]?\\d+)?/;\n\n    // Stop early if the first character isn't a digit or a minus sign.\n    if (!json[ii]?.match(/[-\\d]/)) {\n        return null;\n    }\n\n    // Otherwise, check if the next characters form a valid JSON number.\n    const numberMatch = json.slice(ii).match(JSON_NUMBER_REGEX);\n    return numberMatch ? numberMatch[0] : null;\n}\n\ntype BigIntValueObject = {\n    // `$` implies 'this is a value object'.\n    // `n` implies 'interpret the value as a bigint'.\n    $n: string;\n};\n\nfunction wrapBigIntValueObject(value: string): string {\n    return `{\"$n\":\"${value}\"}`;\n}\n\nfunction unwrapBigIntValueObject({ $n }: BigIntValueObject): bigint {\n    if ($n.match(/[eE]/)) {\n        const [units, exponent] = $n.split(/[eE]/);\n        return BigInt(units) * BigInt(10) ** BigInt(exponent);\n    }\n    return BigInt($n);\n}\n\nfunction isBigIntValueObject(value: unknown): value is BigIntValueObject {\n    return !!value && typeof value === 'object' && '$n' in value && typeof value.$n === 'string';\n}\n", "import { RpcRequest } from './rpc-request';\n\nlet _nextMessageId = 0n;\nfunction getNextMessageId(): string {\n    const id = _nextMessageId;\n    _nextMessageId++;\n    return id.toString();\n}\n\n/**\n * Returns a spec-compliant JSON RPC 2.0 message, given a method name and some params.\n *\n * Generates a new `id` on each call by incrementing a `bigint` and casting it to a string.\n */\nexport function createRpcMessage<TParams>(request: RpcRequest<TParams>) {\n    return {\n        id: getNextMessageId(),\n        jsonrpc: '2.0',\n        method: request.methodName,\n        params: request.params,\n    };\n}\n", "/**\n * Transforms a value into a JSON string, whilst rendering bigints as large unsafe integers.\n */\nexport function stringifyJsonWithBigints(value: unknown, space?: number | string): string {\n    return unwrapBigIntValueObject(\n        JSON.stringify(value, (_, v) => (typeof v === 'bigint' ? wrapBigIntValueObject(v) : v), space),\n    );\n}\n\ntype BigIntValueObject = {\n    // `$` implies 'this is a value object'.\n    // `n` implies 'interpret the value as a bigint'.\n    $n: string;\n};\n\nfunction wrapBigIntValueObject(value: bigint): BigIntValueObject {\n    return { $n: `${value}` };\n}\n\nfunction unwrapBigIntValueObject(value: string): string {\n    return value.replace(/\\{\\s*\"\\$n\"\\s*:\\s*\"(-?\\d+)\"\\s*\\}/g, '$1');\n}\n"]}