{"version": 3, "sources": ["../src/rpc-integer-overflow-error.ts", "../src/rpc-default-config.ts", "../../event-target-impl/src/index.browser.ts", "../src/rpc-request-coalescer.ts", "../src/rpc-request-deduplication.ts", "../src/rpc-transport.ts", "../src/rpc.ts"], "names": ["SolanaError", "SOLANA_ERROR__RPC__INTEGER_OVERFLOW", "safeCaptureStackTrace", "AbortController", "isJsonRpcPayload", "fastStableStringify", "pipe", "createHttpTransportForSolanaRpc", "createRpc", "createSolanaRpcApi"], "mappings": ";;;;;;;;;;;;;;AAGO,SAAS,uCAAA,CACZ,UACA,EAAA,OAAA,EACA,KACuD,EAAA;AACvD,EAAA,IAAI,aAAgB,GAAA,EAAA;AACpB,EAAA,IAAI,OAAO,OAAA,CAAQ,CAAC,CAAA,KAAM,QAAU,EAAA;AAChC,IAAM,MAAA,WAAA,GAAc,OAAQ,CAAA,CAAC,CAAI,GAAA,CAAA;AACjC,IAAA,MAAM,YAAY,WAAc,GAAA,EAAA;AAChC,IAAA,MAAM,gBAAgB,WAAc,GAAA,GAAA;AACpC,IAAI,IAAA,SAAA,IAAa,CAAK,IAAA,aAAA,IAAiB,EAAI,EAAA;AACvC,MAAA,aAAA,GAAgB,WAAc,GAAA,IAAA;AAAA,KACvB,MAAA,IAAA,SAAA,IAAa,CAAK,IAAA,aAAA,IAAiB,EAAI,EAAA;AAC9C,MAAA,aAAA,GAAgB,WAAc,GAAA,IAAA;AAAA,KACvB,MAAA,IAAA,SAAA,IAAa,CAAK,IAAA,aAAA,IAAiB,EAAI,EAAA;AAC9C,MAAA,aAAA,GAAgB,WAAc,GAAA,IAAA;AAAA,KAC3B,MAAA;AACH,MAAA,aAAA,GAAgB,WAAc,GAAA,IAAA;AAAA;AAClC,GACG,MAAA;AACH,IAAA,aAAA,GAAgB,CAAK,EAAA,EAAA,OAAA,CAAQ,CAAC,CAAA,CAAE,UAAU,CAAA,EAAA,CAAA;AAAA;AAE9C,EAAM,MAAA,IAAA,GACF,QAAQ,MAAS,GAAA,CAAA,GACX,QACK,KAAM,CAAA,CAAC,EACP,GAAI,CAAA,CAAA,QAAA,KAAa,OAAO,QAAa,KAAA,QAAA,GAAW,IAAI,QAAQ,CAAA,CAAA,CAAA,GAAM,QAAS,CAC3E,CAAA,IAAA,CAAK,GAAG,CACb,GAAA,MAAA;AACV,EAAM,MAAA,KAAA,GAAQ,IAAIA,kBAAA,CAAYC,0CAAqC,EAAA;AAAA,IAC/D,aAAA;AAAA,IACA,OAAA;AAAA,IACA,UAAA;AAAA,IACA,iBAAmB,EAAA,IAAA,GAAO,CAAc,WAAA,EAAA,IAAI,CAAO,EAAA,CAAA,GAAA,EAAA;AAAA,IACnD,KAAA;AAAA,IACA,GAAI,IAAA,KAAS,MAAY,GAAA,EAAE,MAAS,GAAA;AAAA,GACvC,CAAA;AACD,EAAAC,4BAAA,CAAsB,OAAO,uCAAuC,CAAA;AACpE,EAAO,OAAA,KAAA;AACX;;;AC1BO,IAAM,kBAAqF,GAAA;AAAA,EAC9F,iBAAmB,EAAA,WAAA;AAAA,EACnB,iBAAA,CAAkB,OAAS,EAAA,OAAA,EAAS,KAAO,EAAA;AACvC,IAAA,MAAM,uCAAwC,CAAA,OAAA,CAAQ,UAAY,EAAA,OAAA,EAAS,KAAK,CAAA;AAAA;AAExF;;;ACrBO,IAAMC,IAAkB,UAAW,CAAA,eAAA;;;ACgB1C,IAAI,oBAAA;AACJ,SAAS,wBAA2B,GAAA;AAGhC,EAAA,OAAO,yBAAyB,YAC1B,GAAA;AAAA,IACI,oBACI,EAAA;AAAA,MAGR,EAAC;AACX;AAEO,SAAS,oCAAA,CACZ,WACA,mBACU,EAAA;AACV,EAAI,IAAA,mCAAA;AACJ,EAAO,OAAA,eAAe,yBAClB,OAC+B,EAAA;AAC/B,IAAM,MAAA,EAAE,OAAS,EAAA,MAAA,EAAW,GAAA,OAAA;AAC5B,IAAM,MAAA,gBAAA,GAAmB,oBAAoB,OAAO,CAAA;AACpD,IAAA,IAAI,qBAAqB,MAAW,EAAA;AAChC,MAAO,OAAA,MAAM,UAAU,OAAO,CAAA;AAAA;AAElC,IAAA,IAAI,CAAC,mCAAqC,EAAA;AACtC,MAAA,cAAA,CAAe,MAAM;AACjB,QAAsC,mCAAA,GAAA,MAAA;AAAA,OACzC,CAAA;AACD,MAAA,mCAAA,GAAsC,EAAC;AAAA;AAE3C,IAAI,IAAA,mCAAA,CAAoC,gBAAgB,CAAA,IAAK,IAAM,EAAA;AAC/D,MAAM,MAAA,eAAA,GAAkB,IAAI,CAAgB,EAAA;AAC5C,MAAA,MAAM,mBAAmB,YAAY;AACjC,QAAI,IAAA;AACA,UAAA,OAAO,MAAM,SAAqB,CAAA;AAAA,YAC9B,GAAG,OAAA;AAAA,YACH,QAAQ,eAAgB,CAAA;AAAA,WAC3B,CAAA;AAAA,iBACI,CAAG,EAAA;AACR,UAAI,IAAA,CAAA,MAAO,oBAAyB,KAAA,wBAAA,EAA6B,CAAA,EAAA;AAI7D,YAAA;AAAA;AAEJ,UAAM,MAAA,CAAA;AAAA;AACV,OACD,GAAA;AACH,MAAA,mCAAA,CAAoC,gBAAgB,CAAI,GAAA;AAAA,QACpD,eAAA;AAAA,QACA,YAAc,EAAA,CAAA;AAAA,QACd;AAAA,OACJ;AAAA;AAEJ,IAAM,MAAA,gBAAA,GAAmB,oCAAoC,gBAAgB,CAAA;AAC7E,IAAiB,gBAAA,CAAA,YAAA,EAAA;AACjB,IAAA,IAAI,MAAQ,EAAA;AACR,MAAA,MAAM,kBAAkB,gBAAiB,CAAA,eAAA;AACzC,MAAA,OAAO,MAAM,IAAI,OAAgC,CAAA,CAAC,SAAS,MAAW,KAAA;AAClE,QAAM,MAAA,WAAA,GAAc,CAAC,CAAoC,KAAA;AACrD,UAAO,MAAA,CAAA,mBAAA,CAAoB,SAAS,WAAW,CAAA;AAC/C,UAAA,gBAAA,CAAiB,YAAgB,IAAA,CAAA;AACjC,UAAA,cAAA,CAAe,MAAM;AACjB,YAAI,IAAA,gBAAA,CAAiB,iBAAiB,CAAG,EAAA;AACrC,cAAA,MAAM,kBAAkB,gBAAiB,CAAA,eAAA;AACzC,cAAgB,eAAA,CAAA,KAAA,CAAO,oBAAyB,KAAA,wBAAA,EAA2B,CAAA;AAAA;AAC/E,WACH,CAAA;AAED,UAAQ,MAAA,CAAA,CAAA,CAAE,OAAuB,MAAM,CAAA;AAAA,SAC3C;AACA,QAAO,MAAA,CAAA,gBAAA,CAAiB,SAAS,WAAW,CAAA;AAC5C,QAAA,eAAA,CACK,KAAK,OAAO,CAAA,CACZ,MAAM,MAAM,CAAA,CACZ,QAAQ,MAAM;AACX,UAAO,MAAA,CAAA,mBAAA,CAAoB,SAAS,WAAW,CAAA;AAAA,SAClD,CAAA;AAAA,OACR,CAAA;AAAA,KACE,MAAA;AACH,MAAA,OAAQ,MAAM,gBAAiB,CAAA,eAAA;AAAA;AACnC,GACJ;AACJ;AClGO,SAAS,oCAAoC,OAAsC,EAAA;AACtF,EAAO,OAAAC,wBAAA,CAAiB,OAAO,CAAA,GAAIC,oCAAoB,CAAA,CAAC,QAAQ,MAAQ,EAAA,OAAA,CAAQ,MAAM,CAAC,CAAI,GAAA,MAAA;AAC/F;;;ACQA,SAAS,iBACL,OACiD,EAAA;AACjD,EAAA,MAAM,MAA8B,EAAC;AACrC,EAAA,KAAA,MAAW,cAAc,OAAS,EAAA;AAE9B,IAAA,GAAA,CAAI,UAAW,CAAA,WAAA,EAAa,CAAA,GAAI,QAAQ,UAAU,CAAA;AAAA;AAEtD,EAAO,OAAA,GAAA;AACX;AAcO,SAAS,0BACZ,MACuC,EAAA;AACvC,EAAO,OAAAC,eAAA;AAAA,IACHC,gDAAgC,CAAA;AAAA,MAC5B,GAAG,MAAA;AAAA,MACH,OAAS,EAAA;AAAA,QACL,GAAI,KAAA;AAAA,QAOJ,GAAI,MAAO,CAAA,OAAA,GAAU,gBAAiB,CAAA,MAAA,CAAO,OAAO,CAAI,GAAA,MAAA;AAAA,QACxD,GAAI;AAAA;AAAA,UAEA,eAAiB,EAAc,CAAM,GAAA,EAAA,OAAW,CAAK,CAAA;AAAA;AACzD;AACJ,KACH,CAAA;AAAA,IACD,CAAA,SAAA,KAAa,oCAAqC,CAAA,SAAA,EAAW,mCAAmC;AAAA,GACpG;AACJ;;;AC1CO,SAAS,eAAA,CACZ,YACA,MACF,EAAA;AACE,EAAO,OAAA,4BAAA,CAA6B,0BAA0B,EAAE,GAAA,EAAK,YAAY,GAAG,MAAA,EAAQ,CAAC,CAAA;AACjG;AAMO,SAAS,6BAA8D,SAAuB,EAAA;AACjG,EAAA,OAAOC,iBAAU,CAAA;AAAA,IACb,GAAA,EAAKC,0BAAmB,kBAAkB,CAAA;AAAA,IAC1C;AAAA,GACH,CAAA;AACL", "file": "index.browser.cjs", "sourcesContent": ["import { safeCaptureStackTrace, SOLANA_ERROR__RPC__INTEGER_OVERFLOW, SolanaError } from '@solana/errors';\nimport type { KeyPath } from '@solana/rpc-transformers';\n\nexport function createSolanaJsonRpcIntegerOverflowError(\n    methodName: string,\n    keyPath: KeyPath,\n    value: bigint,\n): SolanaError<typeof SOLANA_ERROR__RPC__INTEGER_OVERFLOW> {\n    let argumentLabel = '';\n    if (typeof keyPath[0] === 'number') {\n        const argPosition = keyPath[0] + 1;\n        const lastDigit = argPosition % 10;\n        const lastTwoDigits = argPosition % 100;\n        if (lastDigit == 1 && lastTwoDigits != 11) {\n            argumentLabel = argPosition + 'st';\n        } else if (lastDigit == 2 && lastTwoDigits != 12) {\n            argumentLabel = argPosition + 'nd';\n        } else if (lastDigit == 3 && lastTwoDigits != 13) {\n            argumentLabel = argPosition + 'rd';\n        } else {\n            argumentLabel = argPosition + 'th';\n        }\n    } else {\n        argumentLabel = `\\`${keyPath[0].toString()}\\``;\n    }\n    const path =\n        keyPath.length > 1\n            ? keyPath\n                  .slice(1)\n                  .map(pathPart => (typeof pathPart === 'number' ? `[${pathPart}]` : pathPart))\n                  .join('.')\n            : undefined;\n    const error = new SolanaError(SOLANA_ERROR__RPC__INTEGER_OVERFLOW, {\n        argumentLabel,\n        keyPath: keyPath as readonly (number | string | symbol)[],\n        methodName,\n        optionalPathLabel: path ? ` at path \\`${path}\\`` : '',\n        value,\n        ...(path !== undefined ? { path } : undefined),\n    });\n    safeCaptureStackTrace(error, createSolanaJsonRpcIntegerOverflowError);\n    return error;\n}\n", "import type { createSolanaRpcApi } from '@solana/rpc-api';\n\nimport { createSolanaJsonRpcIntegerOverflowError } from './rpc-integer-overflow-error';\n\n/**\n * When you create {@link Rpc} instances with custom transports but otherwise the default RPC API\n * behaviours, use this.\n *\n * @example\n * ```ts\n * const myCustomRpc = createRpc({\n *     api: createSolanaRpcApi(DEFAULT_RPC_CONFIG),\n *     transport: myCustomTransport,\n * });\n * ```\n */\nexport const DEFAULT_RPC_CONFIG: Partial<NonNullable<Parameters<typeof createSolanaRpcApi>[0]>> = {\n    defaultCommitment: 'confirmed',\n    onIntegerOverflow(request, keyPath, value) {\n        throw createSolanaJsonRpcIntegerOverflowError(request.methodName, keyPath, value);\n    },\n};\n", "export const AbortController = globalThis.AbortController;\nexport const EventTarget = globalThis.EventTarget;\n", "import { AbortController } from '@solana/event-target-impl';\nimport type { RpcTransport } from '@solana/rpc-spec';\nimport type { RpcResponse } from '@solana/rpc-spec-types';\n\ntype CoalescedRequest = {\n    readonly abortController: AbortController;\n    numConsumers: number;\n    readonly responsePromise: Promise<RpcResponse>;\n};\n\ntype GetDeduplicationKeyFn = (payload: unknown) => string | undefined;\n\n// This used to be a `Symbol()`, but there's a bug in Node <21 where the `undici` library passes\n// the `reason` property of the `AbortSignal` straight to `Error.captureStackTrace()` without first\n// typechecking it. `Error.captureStackTrace()` fatals when given a `Symbol`.\n// See https://github.com/nodejs/undici/pull/2597\nlet EXPLICIT_ABORT_TOKEN: ReturnType<typeof createExplicitAbortToken>;\nfunction createExplicitAbortToken() {\n    // This function is an annoying workaround to prevent `process.env.NODE_ENV` from appearing at\n    // the top level of this module and thwarting an optimizing compiler's attempt to tree-shake.\n    return process.env.NODE_ENV !== \"production\"\n        ? {\n              EXPLICIT_ABORT_TOKEN:\n                  'This object is thrown from the request that underlies a series of coalesced ' +\n                  'requests when the last request in that series aborts',\n          }\n        : {};\n}\n\nexport function getRpcTransportWithRequestCoalescing<TTransport extends RpcTransport>(\n    transport: TTransport,\n    getDeduplicationKey: GetDeduplicationKeyFn,\n): TTransport {\n    let coalescedRequestsByDeduplicationKey: Record<string, CoalescedRequest> | undefined;\n    return async function makeCoalescedHttpRequest<TResponse>(\n        request: Parameters<RpcTransport>[0],\n    ): Promise<RpcResponse<TResponse>> {\n        const { payload, signal } = request;\n        const deduplicationKey = getDeduplicationKey(payload);\n        if (deduplicationKey === undefined) {\n            return await transport(request);\n        }\n        if (!coalescedRequestsByDeduplicationKey) {\n            queueMicrotask(() => {\n                coalescedRequestsByDeduplicationKey = undefined;\n            });\n            coalescedRequestsByDeduplicationKey = {};\n        }\n        if (coalescedRequestsByDeduplicationKey[deduplicationKey] == null) {\n            const abortController = new AbortController();\n            const responsePromise = (async () => {\n                try {\n                    return await transport<TResponse>({\n                        ...request,\n                        signal: abortController.signal,\n                    });\n                } catch (e) {\n                    if (e === (EXPLICIT_ABORT_TOKEN ||= createExplicitAbortToken())) {\n                        // We triggered this error when the last subscriber aborted. Letting this\n                        // error bubble up from here would cause runtime fatals where there should\n                        // be none.\n                        return;\n                    }\n                    throw e;\n                }\n            })();\n            coalescedRequestsByDeduplicationKey[deduplicationKey] = {\n                abortController,\n                numConsumers: 0,\n                responsePromise,\n            };\n        }\n        const coalescedRequest = coalescedRequestsByDeduplicationKey[deduplicationKey];\n        coalescedRequest.numConsumers++;\n        if (signal) {\n            const responsePromise = coalescedRequest.responsePromise as Promise<RpcResponse<TResponse>>;\n            return await new Promise<RpcResponse<TResponse>>((resolve, reject) => {\n                const handleAbort = (e: AbortSignalEventMap['abort']) => {\n                    signal.removeEventListener('abort', handleAbort);\n                    coalescedRequest.numConsumers -= 1;\n                    queueMicrotask(() => {\n                        if (coalescedRequest.numConsumers === 0) {\n                            const abortController = coalescedRequest.abortController;\n                            abortController.abort((EXPLICIT_ABORT_TOKEN ||= createExplicitAbortToken()));\n                        }\n                    });\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n                    reject((e.target as AbortSignal).reason);\n                };\n                signal.addEventListener('abort', handleAbort);\n                responsePromise\n                    .then(resolve)\n                    .catch(reject)\n                    .finally(() => {\n                        signal.removeEventListener('abort', handleAbort);\n                    });\n            });\n        } else {\n            return (await coalescedRequest.responsePromise) as RpcResponse<TResponse>;\n        }\n    } as TTransport;\n}\n", "import fastStableStringify from '@solana/fast-stable-stringify';\nimport { isJsonRpcPayload } from '@solana/rpc-spec';\n\nexport function getSolanaRpcPayloadDeduplicationKey(payload: unknown): string | undefined {\n    return isJsonRpcPayload(payload) ? fastStableStringify([payload.method, payload.params]) : undefined;\n}\n", "import { pipe } from '@solana/functional';\nimport { createHttpTransport, createHttpTransportForSolanaRpc } from '@solana/rpc-transport-http';\nimport type { ClusterUrl } from '@solana/rpc-types';\n\nimport { RpcTransportFromClusterUrl } from './rpc-clusters';\nimport { getRpcTransportWithRequestCoalescing } from './rpc-request-coalescer';\nimport { getSolanaRpcPayloadDeduplicationKey } from './rpc-request-deduplication';\n\ntype RpcTransportConfig = Parameters<typeof createHttpTransport>[0];\ninterface DefaultRpcTransportConfig<TClusterUrl extends ClusterUrl> extends RpcTransportConfig {\n    url: TClusterUrl;\n}\n\nfunction normalizeHeaders<T extends Record<string, string>>(\n    headers: T,\n): { [K in string & keyof T as Lowercase<K>]: T[K] } {\n    const out: Record<string, string> = {};\n    for (const headerName in headers) {\n        // Lowercasing header names makes it easier to override user-supplied headers.\n        out[headerName.toLowerCase()] = headers[headerName];\n    }\n    return out as { [K in string & keyof T as Lowercase<K>]: T[K] };\n}\n\n/**\n * Creates a {@link RpcTransport} with some default behaviours.\n *\n * The default behaviours include:\n * - An automatically-set `Solana-Client` request header, containing the version of `@solana/kit`\n * - Logic that coalesces multiple calls in the same runloop, for the same methods with the same\n *   arguments, into a single network request.\n * - [node-only] An automatically-set `Accept-Encoding` request header asking the server to compress\n *   responses\n *\n * @param config\n */\nexport function createDefaultRpcTransport<TClusterUrl extends ClusterUrl>(\n    config: DefaultRpcTransportConfig<TClusterUrl>,\n): RpcTransportFromClusterUrl<TClusterUrl> {\n    return pipe(\n        createHttpTransportForSolanaRpc({\n            ...config,\n            headers: {\n                ...(__NODEJS__ &&\n                    ({\n                        // Keep these headers lowercase so they will be overridden by any user-supplied headers below.\n                        'accept-encoding':\n                            // Natively supported by Node LTS v20.18.0 and above.\n                            'br,gzip,deflate', // Brotli, gzip, and Deflate, in that order.\n                    } as { [overrideHeader: string]: string })),\n                ...(config.headers ? normalizeHeaders(config.headers) : undefined),\n                ...({\n                    // Keep these headers lowercase so they will override any user-supplied headers above.\n                    'solana-client': __VERSION__ ? `js/${__VERSION__}` : 'UNKNOWN',\n                } as { [overrideHeader: string]: string }),\n            },\n        }) as RpcTransportFromClusterUrl<TClusterUrl>,\n        transport => getRpcTransportWithRequestCoalescing(transport, getSolanaRpcPayloadDeduplicationKey),\n    );\n}\n", "import { createSolanaRpc<PERSON>pi } from '@solana/rpc-api';\nimport { createRpc, RpcTransport } from '@solana/rpc-spec';\nimport { ClusterUrl } from '@solana/rpc-types';\n\nimport type { RpcFromTransport, SolanaRpcApiFromTransport } from './rpc-clusters';\nimport { DEFAULT_RPC_CONFIG } from './rpc-default-config';\nimport { createDefaultRpcTransport } from './rpc-transport';\n\ntype DefaultRpcTransportConfig<TClusterUrl extends ClusterUrl> = Parameters<\n    typeof createDefaultRpcTransport<TClusterUrl>\n>[0];\n\n/**\n * Creates a {@link Rpc} instance that exposes the Solana JSON RPC API given a cluster URL and some\n * optional transport config. See {@link createDefaultRpcTransport} for the shape of the transport\n * config.\n */\nexport function createSolanaRpc<TClusterUrl extends ClusterUrl>(\n    clusterUrl: TClusterUrl,\n    config?: Omit<DefaultRpcTransportConfig<TClusterUrl>, 'url'>,\n) {\n    return createSolanaRpcFromTransport(createDefaultRpcTransport({ url: clusterUrl, ...config }));\n}\n\n/**\n * Creates a {@link Rpc} instance that exposes the Solana JSON RPC API given the supplied\n * {@link RpcTransport}.\n */\nexport function createSolanaRpcFromTransport<TTransport extends RpcTransport>(transport: TTransport) {\n    return createRpc({\n        api: createSolanaRpcApi(DEFAULT_RPC_CONFIG),\n        transport,\n    }) as RpcFromTransport<SolanaRpcApiFromTransport<TTransport>, TTransport>;\n}\n"]}