{"version": 3, "sources": ["../src/request-transformer-bigint-downcast-internal.ts", "../src/tree-traversal.ts", "../src/request-transformer-bigint-downcast.ts", "../src/request-transformer-default-commitment-internal.ts", "../src/request-transformer-default-commitment.ts", "../src/request-transformer-integer-overflow-internal.ts", "../src/request-transformer-integer-overflow.ts", "../src/request-transformer-options-object-position-config.ts", "../src/request-transformer.ts", "../src/response-transformer-bigint-upcast-internal.ts", "../src/response-transformer-bigint-upcast.ts", "../src/response-transformer-result.ts", "../src/response-transformer-throw-solana-error.ts", "../src/response-transformer.ts", "../src/response-transformer-allowed-numeric-values.ts"], "names": ["pipe"], "mappings": ";;;;;;AAEO,SAAS,6BAA6B,KAAyB,EAAA;AAClE,EAAA,OAAO,OAAO,KAAU,KAAA,QAAA;AAAA;AAAA;AAAA;AAAA,IAIlB,OAAO,KAAK;AAAA,MACZ,KAAA;AACV;;;ACJO,IAAM,mBAAmB;AAOhC,SAAS,cAAc,QAAyB,EAAA;AAC5C,EAAO,OAAA,SAAS,QAAwC,CAAA,IAAA,EAAe,KAAwB,EAAA;AAC3F,IAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,IAAI,CAAG,EAAA;AACrB,MAAA,OAAO,IAAK,CAAA,GAAA,CAAI,CAAC,OAAA,EAAS,EAAO,KAAA;AAC7B,QAAA,MAAM,SAAY,GAAA;AAAA,UACd,GAAG,KAAA;AAAA,UACH,OAAS,EAAA,CAAC,GAAG,KAAA,CAAM,SAAS,EAAE;AAAA,SAClC;AACA,QAAO,OAAA,QAAA,CAAS,SAAS,SAAS,CAAA;AAAA,OACrC,CAAA;AAAA,KACM,MAAA,IAAA,OAAO,IAAS,KAAA,QAAA,IAAY,SAAS,IAAM,EAAA;AAClD,MAAA,MAAM,MAAiD,EAAC;AACxD,MAAA,KAAA,MAAW,YAAY,IAAM,EAAA;AACzB,QAAA,IAAI,CAAC,MAAO,CAAA,SAAA,CAAU,eAAe,IAAK,CAAA,IAAA,EAAM,QAAQ,CAAG,EAAA;AACvD,UAAA;AAAA;AAEJ,QAAA,MAAM,SAAY,GAAA;AAAA,UACd,GAAG,KAAA;AAAA,UACH,OAAS,EAAA,CAAC,GAAG,KAAA,CAAM,SAAS,QAAQ;AAAA,SACxC;AACA,QAAA,GAAA,CAAI,QAAQ,CAAI,GAAA,QAAA,CAAS,IAAK,CAAA,QAA6B,GAAG,SAAS,CAAA;AAAA;AAE3E,MAAO,OAAA,GAAA;AAAA,KACJ,MAAA;AACH,MAAO,OAAA,QAAA,CAAS,OAAO,CAAC,GAAA,EAAK,cAAc,SAAU,CAAA,GAAA,EAAK,KAAK,CAAA,EAAG,IAAI,CAAA;AAAA;AAC1E,GACJ;AACJ;AAqBO,SAAS,+BAAA,CACZ,UACA,YACqB,EAAA;AACrB,EAAA,OAAO,CAAU,OAA6C,KAAA;AAC1D,IAAM,MAAA,QAAA,GAAW,cAAc,QAAQ,CAAA;AACvC,IAAA,OAAO,OAAO,MAAO,CAAA;AAAA,MACjB,GAAG,OAAA;AAAA,MACH,MAAQ,EAAA,QAAA,CAAS,OAAQ,CAAA,MAAA,EAAQ,YAAY;AAAA,KAChD,CAAA;AAAA,GACL;AACJ;AAEO,SAAS,gCAAA,CACZ,UACA,YACsB,EAAA;AACtB,EAAA,OAAO,CAAQ,IAAA,KAAA,aAAA,CAAc,QAAQ,CAAA,CAAE,MAAM,YAAY,CAAA;AAC7D;;;AChEO,SAAS,mCAAsC,GAAA;AAClD,EAAO,OAAA,+BAAA,CAAgC,CAAC,4BAA4B,CAAA,EAAG,EAAE,OAAS,EAAA,IAAI,CAAA;AAC1F;;;ACdO,SAAS,sBAAuB,CAAA;AAAA,EACnC,sBAAA;AAAA,EACA,MAAA;AAAA,EACA,6BAAA;AAAA,EACA;AACJ,CAKI,EAAA;AACA,EAAM,MAAA,qBAAA,GAAwB,OAAO,6BAA6B,CAAA;AAClE,EAAA;AAAA;AAAA,IAEI,qBAA0B,KAAA,MAAA;AAAA,IAEzB,yBAAyB,OAAO,qBAAA,KAA0B,YAAY,CAAC,KAAA,CAAM,QAAQ,qBAAqB;AAAA,IAC7G;AACE,IAAA;AAAA;AAAA,MAEI,yBACA,sBAA0B,IAAA;AAAA,MAC5B;AACE,MAAA,IACI,CAAC,qBAAsB,CAAA,sBAA4D,KACnF,qBAAsB,CAAA,sBAA4D,MAAM,WAC1F,EAAA;AAEE,QAAM,MAAA,UAAA,GAAa,CAAC,GAAG,MAAM,CAAA;AAC7B,QAAM,MAAA;AAAA,UACF,CAAC,sBAA4D,GAAG,CAAA;AAAA;AAAA,UAChE,GAAG;AAAA,SACH,GAAA,qBAAA;AACJ,QAAA,IAAI,MAAO,CAAA,IAAA,CAAK,IAAI,CAAA,CAAE,SAAS,CAAG,EAAA;AAC9B,UAAA,UAAA,CAAW,6BAA6B,CAAI,GAAA,IAAA;AAAA,SACzC,MAAA;AACH,UAAI,IAAA,6BAAA,KAAkC,UAAW,CAAA,MAAA,GAAS,CAAG,EAAA;AACzD,YAAW,UAAA,CAAA,MAAA,EAAA;AAAA,WACR,MAAA;AACH,YAAA,UAAA,CAAW,6BAA6B,CAAI,GAAA,MAAA;AAAA;AAChD;AAEJ,QAAO,OAAA,UAAA;AAAA;AACX,KACJ,MAAA,IAAW,uBAAuB,WAAa,EAAA;AAE3C,MAAM,MAAA,UAAA,GAAa,CAAC,GAAG,MAAM,CAAA;AAC7B,MAAA,UAAA,CAAW,6BAA6B,CAAI,GAAA;AAAA,QACxC,GAAG,qBAAA;AAAA,QACH,CAAC,sBAAsB,GAAG;AAAA,OAC9B;AACA,MAAO,OAAA,UAAA;AAAA;AACX;AAEJ,EAAO,OAAA,MAAA;AACX;;;ACtCO,SAAS,sCAAuC,CAAA;AAAA,EACnD,iBAAA;AAAA,EACA;AACJ,CAG2B,EAAA;AACvB,EAAA,OAAO,CAAU,OAA6C,KAAA;AAC1D,IAAM,MAAA,EAAE,MAAQ,EAAA,UAAA,EAAe,GAAA,OAAA;AAG/B,IAAA,IAAI,CAAC,KAAA,CAAM,OAAQ,CAAA,MAAM,CAAG,EAAA;AACxB,MAAO,OAAA,OAAA;AAAA;AAIX,IAAM,MAAA,6BAAA,GAAgC,8BAA8B,UAAU,CAAA;AAC9E,IAAA,IAAI,iCAAiC,IAAM,EAAA;AACvC,MAAO,OAAA,OAAA;AAAA;AAGX,IAAA,OAAO,OAAO,MAAO,CAAA;AAAA,MACjB,UAAA;AAAA,MACA,QAAQ,sBAAuB,CAAA;AAAA,QAC3B,sBAAA,EAAwB,UAAe,KAAA,iBAAA,GAAoB,qBAAwB,GAAA,YAAA;AAAA,QACnF,6BAAA;AAAA,QACA,kBAAoB,EAAA,iBAAA;AAAA,QACpB;AAAA,OACH;AAAA,KACJ,CAAA;AAAA,GACL;AACJ;;;AChDO,SAAS,8BAA8B,iBAA8D,EAAA;AACxG,EAAA,OAAO,CAAI,KAAA,EAAU,EAAE,OAAA,EAAiC,KAAA;AACpD,IAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC3B,MAAA,IAAI,sBAAsB,KAAQ,GAAA,MAAA,CAAO,oBAAoB,KAAQ,GAAA,CAAC,OAAO,gBAAmB,CAAA,EAAA;AAC5F,QAAA,iBAAA,CAAkB,SAAgC,KAAK,CAAA;AAAA;AAC3D;AAEJ,IAAO,OAAA,KAAA;AAAA,GACX;AACJ;;;ACSO,SAAS,qCAAqC,iBAA2C,EAAA;AAC5F,EAAA,OAAO,CAAU,OAA6C,KAAA;AAC1D,IAAA,MAAM,WAAc,GAAA,+BAAA;AAAA,MAChB,CAAC,8BAA8B,CAAI,GAAA,IAAA,KAAS,kBAAkB,OAAS,EAAA,GAAG,IAAI,CAAC,CAAC,CAAA;AAAA,MAChF,EAAE,OAAS,EAAA,EAAG;AAAA,KAClB;AACA,IAAA,OAAO,YAAY,OAAO,CAAA;AAAA,GAC9B;AACJ;;;AC5BO,IAAM,iCAA4D,GAAA;AAAA,EACrE,oBAAsB,EAAA,CAAA;AAAA,EACtB,kBAAoB,EAAA,CAAA;AAAA,EACpB,cAAgB,EAAA,CAAA;AAAA,EAChB,UAAY,EAAA,CAAA;AAAA,EACZ,QAAU,EAAA,CAAA;AAAA,EACV,cAAgB,EAAA,CAAA;AAAA,EAChB,kBAAoB,EAAA,CAAA;AAAA,EACpB,SAAW,EAAA,CAAA;AAAA,EACX,kBAAoB,EAAA,CAAA;AAAA,EACpB,YAAc,EAAA,CAAA;AAAA,EACd,gBAAkB,EAAA,CAAA;AAAA,EAClB,oBAAsB,EAAA,CAAA;AAAA,EACtB,kBAAoB,EAAA,CAAA;AAAA,EACpB,kBAAoB,EAAA,CAAA;AAAA,EACpB,kBAAoB,EAAA,CAAA;AAAA,EACpB,iBAAmB,EAAA,CAAA;AAAA,EACnB,iCAAmC,EAAA,CAAA;AAAA,EACnC,mBAAqB,EAAA,CAAA;AAAA,EACrB,kBAAoB,EAAA,CAAA;AAAA,EACpB,uBAAyB,EAAA,CAAA;AAAA,EACzB,OAAS,EAAA,CAAA;AAAA,EACT,aAAe,EAAA,CAAA;AAAA,EACf,yBAA2B,EAAA,CAAA;AAAA,EAC3B,SAAW,EAAA,CAAA;AAAA,EACX,sBAAwB,EAAA,CAAA;AAAA,EACxB,0BAA4B,EAAA,CAAA;AAAA,EAC5B,uBAAyB,EAAA,CAAA;AAAA,EACzB,uBAAyB,EAAA,CAAA;AAAA,EACzB,cAAgB,EAAA,CAAA;AAAA,EAChB,cAAgB,EAAA,CAAA;AAAA,EAChB,mBAAqB,EAAA,CAAA;AAAA,EACrB,eAAiB,EAAA,CAAA;AAAA,EACjB,gBAAkB,EAAA,CAAA;AAAA,EAClB,iBAAmB,EAAA,CAAA;AAAA,EACnB,oBAAsB,EAAA,CAAA;AAAA,EACtB,cAAgB,EAAA,CAAA;AAAA,EAChB,eAAiB,EAAA,CAAA;AAAA,EACjB,sBAAwB,EAAA,CAAA;AAAA,EACxB,mBAAqB,EAAA;AACzB,CAAA;;;ACKO,SAAS,yCAAyC,MAA0D,EAAA;AAC/G,EAAA,MAAM,wBAAwB,MAAQ,EAAA,iBAAA;AACtC,EAAA,OAAO,CAAC,OAAoC,KAAA;AACxC,IAAO,OAAA,IAAA;AAAA,MACH,OAAA;AAAA,MACA,qBAAwB,GAAA,oCAAA,CAAqC,qBAAqB,CAAA,GAAI,CAAK,CAAA,KAAA,CAAA;AAAA,MAC3F,mCAAoC,EAAA;AAAA,MACpC,sCAAuC,CAAA;AAAA,QACnC,mBAAmB,MAAQ,EAAA,iBAAA;AAAA,QAC3B,6BAA+B,EAAA;AAAA,OAClC;AAAA,KACL;AAAA,GACJ;AACJ;;;ACxDO,SAAS,uBAAuB,sBAA4C,EAAA;AAC/E,EAAA,OAAO,SAAS,0BAAA,CAA2B,KAAgB,EAAA,EAAE,SAA2B,EAAA;AACpF,IAAM,MAAA,SAAA,GAAa,OAAO,KAAU,KAAA,QAAA,IAAY,OAAO,SAAU,CAAA,KAAK,CAAM,IAAA,OAAO,KAAU,KAAA,QAAA;AAC7F,IAAI,IAAA,CAAC,WAAkB,OAAA,KAAA;AACvB,IAAI,IAAA,2BAAA,CAA4B,OAAS,EAAA,sBAAsB,CAAG,EAAA;AAC9D,MAAA,OAAO,OAAO,KAAK,CAAA;AAAA,KAChB,MAAA;AACH,MAAA,OAAO,OAAO,KAAK,CAAA;AAAA;AACvB,GACJ;AACJ;AAEA,SAAS,2BAAA,CAA4B,SAAkB,sBAA4C,EAAA;AAC/F,EAAO,OAAA,sBAAA,CAAuB,KAAK,CAAqB,iBAAA,KAAA;AACpD,IAAI,IAAA,iBAAA,CAAkB,MAAW,KAAA,OAAA,CAAQ,MAAQ,EAAA;AAC7C,MAAO,OAAA,KAAA;AAAA;AAEX,IAAA,KAAA,IAAS,KAAK,OAAQ,CAAA,MAAA,GAAS,CAAG,EAAA,EAAA,IAAM,GAAG,EAAM,EAAA,EAAA;AAC7C,MAAM,MAAA,WAAA,GAAc,QAAQ,EAAE,CAAA;AAC9B,MAAM,MAAA,qBAAA,GAAwB,kBAAkB,EAAE,CAAA;AAClD,MAAA,IACI,0BAA0B,WACzB,KAAA,qBAAA,KAA0B,gBAAoB,IAAA,OAAO,gBAAgB,QACxE,CAAA,EAAA;AACE,QAAO,OAAA,KAAA;AAAA;AACX;AAEJ,IAAO,OAAA,IAAA;AAAA,GACV,CAAA;AACL;;;ACTO,SAAS,mCAAmC,sBAA4C,EAAA;AAC3F,EAAO,OAAA,gCAAA,CAAiC,CAAC,sBAAA,CAAuB,sBAAsB,CAAC,GAAG,EAAE,OAAA,EAAS,EAAC,EAAG,CAAA;AAC7G;;;ACRO,SAAS,4BAAuD,GAAA;AACnE,EAAA,OAAO,UAAS,IAAyB,CAAA,MAAA;AAC7C;ACFO,SAAS,sCAAiE,GAAA;AAC7E,EAAA,OAAO,CAAQ,IAAA,KAAA;AACX,IAAA,MAAM,eAAkB,GAAA,IAAA;AACxB,IAAA,IAAI,WAAW,eAAiB,EAAA;AAC5B,MAAM,MAAA,8BAAA,CAA+B,gBAAgB,KAAK,CAAA;AAAA;AAE9D,IAAO,OAAA,eAAA;AAAA,GACX;AACJ;;;ACUO,SAAS,0CACZ,MACsB,EAAA;AACtB,EAAO,OAAA,CAAC,UAAuB,OAAqC,KAAA;AAChE,IAAA,MAAM,aAAa,OAAQ,CAAA,UAAA;AAC3B,IAAA,MAAM,WACF,MAAQ,EAAA,sBAAA,IAA0B,aAAa,MAAO,CAAA,sBAAA,CAAuB,UAAU,CAAI,GAAA,MAAA;AAC/F,IAAOA,OAAAA,IAAAA;AAAA,MACH,QAAA;AAAA,MACA,CAAK,CAAA,KAAA,sCAAA,EAAyC,CAAA,CAAA,EAAG,OAAO,CAAA;AAAA,MACxD,CAAK,CAAA,KAAA,4BAAA,EAA+B,CAAA,CAAA,EAAG,OAAO,CAAA;AAAA,MAC9C,OAAK,kCAAmC,CAAA,QAAA,IAAY,EAAE,CAAA,CAAE,GAAG,OAAO;AAAA,KACtE;AAAA,GACJ;AACJ;AAgBO,SAAS,uDACZ,MACsB,EAAA;AACtB,EAAO,OAAA,CAAC,UAAuB,OAAqC,KAAA;AAChE,IAAA,MAAM,aAAa,OAAQ,CAAA,UAAA;AAC3B,IAAA,MAAM,WACF,MAAQ,EAAA,sBAAA,IAA0B,aAAa,MAAO,CAAA,sBAAA,CAAuB,UAAU,CAAI,GAAA,MAAA;AAC/F,IAAOA,OAAAA,IAAAA,CAAK,QAAU,EAAA,CAAA,CAAA,KAAK,kCAAmC,CAAA,QAAA,IAAY,EAAE,CAAA,CAAE,CAAG,EAAA,OAAO,CAAC,CAAA;AAAA,GAC7F;AACJ;;;ACpEO,IAAM,8BAAiC,GAAA;AAAA;AAAA,EAE1C,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,eAAe,UAAU,CAAA;AAAA,EACpD,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,eAAe,UAAU,CAAA;AAAA,EACpD,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,qBAAqB,UAAU,CAAA;AAAA,EAC1D,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,qBAAqB,UAAU,CAAA;AAAA,EAC1D,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,mBAAmB,UAAU,CAAA;AAAA,EACxD,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,mBAAmB,UAAU,CAAA;AAAA,EACxD,CAAC,QAAQ,QAAU,EAAA,MAAA,EAAQ,cAAc,gBAAkB,EAAA,OAAA,EAAS,oBAAoB,wBAAwB,CAAA;AAAA,EAChH,CAAC,QAAQ,QAAU,EAAA,MAAA,EAAQ,cAAc,gBAAkB,EAAA,OAAA,EAAS,oBAAoB,wBAAwB,CAAA;AAAA,EAChH,CAAC,MAAQ,EAAA,QAAA,EAAU,QAAQ,YAAc,EAAA,gBAAA,EAAkB,SAAS,sBAAsB,CAAA;AAAA,EAC1F,CAAC,MAAQ,EAAA,QAAA,EAAU,QAAQ,YAAc,EAAA,gBAAA,EAAkB,SAAS,aAAa;AACrF;AACO,IAAM,yBAA4B,GAAA;AAAA,EACrC,GAAG,8BAAA;AAAA;AAAA,EAEH,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,4BAA4B,CAAA;AAAA;AAAA,EAEvD,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,cAAc,CAAA;AAAA,EACzC,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,oBAAoB,CAAA;AAAA;AAAA,EAE/C,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,UAAU,CAAA;AAAA;AAAA,EAErC,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,oBAAoB,CAAA;AAAA,EAC/C,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,iBAAiB,CAAA;AAAA;AAAA,EAE5C,CAAC,MAAQ,EAAA,QAAA,EAAU,MAAQ,EAAA,OAAA,EAAS,cAAc,oBAAoB,CAAA;AAAA;AAAA,EAEtE,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,oBAAoB,CAAA;AAAA,EAC/C,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,aAAa,CAAA;AAAA;AAAA,EAExC,CAAC,MAAA,EAAQ,QAAU,EAAA,MAAA,EAAQ,YAAY,CAAA;AAAA,EACvC,CAAC,MAAQ,EAAA,QAAA,EAAU,MAAQ,EAAA,OAAA,EAAS,kBAAkB,mBAAmB;AAC7E;AACO,IAAM,wBAA2B,GAAA;AAAA,EACpC,CAAC,OAAO,CAAA;AAAA,EACR,CAAC,cAAA,EAAgB,gBAAkB,EAAA,UAAA,EAAY,gBAAgB,CAAA;AAAA,EAC/D,CAAC,cAAgB,EAAA,gBAAA,EAAkB,gBAAgB,CAAA;AAAA,EACnD,CAAC,cAAgB,EAAA,gBAAA,EAAkB,aAAa;AACpD;AACO,IAAM,aAAgB,GAAA;AAAA,EACzB,CAAC,qBAAA,EAAuB,gBAAkB,EAAA,iBAAA,EAAmB,gBAAgB,CAAA;AAAA,EAC7E,CAAC,qBAAA,EAAuB,gBAAkB,EAAA,iBAAA,EAAmB,gBAAgB,CAAA;AAAA,EAC7E,CAAC,UAAU,2BAA2B,CAAA;AAAA,EACtC,CAAC,UAAU,6BAA6B,CAAA;AAAA,EACxC,CAAC,UAAU,uBAAuB,CAAA;AAAA,EAClC,CAAC,cAAA,EAAgB,gBAAkB,EAAA,UAAA,EAAY,gBAAgB,CAAA;AAAA,EAC/D,CAAC,cAAgB,EAAA,gBAAA,EAAkB,gBAAgB,CAAA;AAAA,EACnD,CAAC,cAAgB,EAAA,gBAAA,EAAkB,aAAa;AACpD", "file": "index.browser.mjs", "sourcesContent": ["export function downcastNodeToNumberIfBigint(value: bigint): number;\nexport function downcastNodeToNumberIfBigint<T>(value: T): T;\nexport function downcastNodeToNumberIfBigint(value: unknown): unknown {\n    return typeof value === 'bigint'\n        ? // FIXME(solana-labs/solana/issues/30341) Create a data type to represent u64 in the Solana\n          // JSON RPC implementation so that we can throw away this entire patcher instead of unsafely\n          // downcasting `bigints` to `numbers`.\n          Number(value)\n        : value;\n}\n", "import { RpcRequest, RpcRequestTransformer, RpcResponseTransformer } from '@solana/rpc-spec-types';\n\nexport type KeyPathWildcard = { readonly ['__keyPathWildcard:@solana/kit']: unique symbol };\nexport type KeyPath = ReadonlyArray<KeyPath | KeyPathWildcard | number | string>;\n\nexport const KEYPATH_WILDCARD = {} as KeyPathWildcard;\n\ntype NodeVisitor = <TState extends TraversalState>(value: unknown, state: TState) => unknown;\nexport type TraversalState = Readonly<{\n    keyPath: KeyPath;\n}>;\n\nfunction getTreeWalker(visitors: NodeVisitor[]) {\n    return function traverse<TState extends TraversalState>(node: unknown, state: TState): unknown {\n        if (Array.isArray(node)) {\n            return node.map((element, ii) => {\n                const nextState = {\n                    ...state,\n                    keyPath: [...state.keyPath, ii],\n                };\n                return traverse(element, nextState);\n            });\n        } else if (typeof node === 'object' && node !== null) {\n            const out: Record<number | string | symbol, unknown> = {};\n            for (const propName in node) {\n                if (!Object.prototype.hasOwnProperty.call(node, propName)) {\n                    continue;\n                }\n                const nextState = {\n                    ...state,\n                    keyPath: [...state.keyPath, propName],\n                };\n                out[propName] = traverse(node[propName as keyof typeof node], nextState);\n            }\n            return out;\n        } else {\n            return visitors.reduce((acc, visitNode) => visitNode(acc, state), node);\n        }\n    };\n}\n\n/**\n * Creates a transformer that traverses the request parameters and executes the provided visitors at\n * each node. A custom initial state can be provided but must at least provide `{ keyPath: [] }`.\n *\n * @example\n * ```ts\n * import { getTreeWalkerRequestTransformer } from '@solana/rpc-transformers';\n *\n * const requestTransformer = getTreeWalkerRequestTransformer(\n *     [\n *         // Replaces foo.bar with \"baz\".\n *         (node, state) => (state.keyPath === ['foo', 'bar'] ? 'baz' : node),\n *         // Increments all numbers by 1.\n *         node => (typeof node === number ? node + 1 : node),\n *     ],\n *     { keyPath: [] },\n * );\n * ```\n */\nexport function getTreeWalkerRequestTransformer<TState extends TraversalState>(\n    visitors: NodeVisitor[],\n    initialState: TState,\n): RpcRequestTransformer {\n    return <TParams>(request: RpcRequest<TParams>): RpcRequest => {\n        const traverse = getTreeWalker(visitors);\n        return Object.freeze({\n            ...request,\n            params: traverse(request.params, initialState),\n        });\n    };\n}\n\nexport function getTreeWalkerResponseTransformer<TState extends TraversalState>(\n    visitors: NodeVisitor[],\n    initialState: TState,\n): RpcResponseTransformer {\n    return json => getTreeWalker(visitors)(json, initialState);\n}\n", "import { downcastNodeToNumberIfBigint } from './request-transformer-bigint-downcast-internal';\nimport { getTreeWalkerRequestTransformer } from './tree-traversal';\n\n/**\n * Creates a transformer that downcasts all `BigInt` values to `Number`.\n *\n * @example\n * ```ts\n * import { getBigIntDowncastRequestTransformer } from '@solana/rpc-transformers';\n *\n * const requestTransformer = getBigIntDowncastRequestTransformer();\n * ```\n *\n */\nexport function getBigIntDowncastRequestTransformer() {\n    return getTreeWalkerRequestTransformer([downcastNodeToNumberIfBigint], { keyPath: [] });\n}\n", "import { Commitment } from '@solana/rpc-types';\n\nexport function applyDefaultCommitment({\n    commitmentPropertyName,\n    params,\n    optionsObjectPositionInParams,\n    overrideCommitment,\n}: Readonly<{\n    commitmentPropertyName: string;\n    optionsObjectPositionInParams: number;\n    overrideCommitment?: Commitment;\n    params: unknown[];\n}>) {\n    const paramInTargetPosition = params[optionsObjectPositionInParams];\n    if (\n        // There's no config.\n        paramInTargetPosition === undefined ||\n        // There is a config object.\n        (paramInTargetPosition && typeof paramInTargetPosition === 'object' && !Array.isArray(paramInTargetPosition))\n    ) {\n        if (\n            // The config object already has a commitment set.\n            paramInTargetPosition &&\n            commitmentPropertyName in paramInTargetPosition\n        ) {\n            if (\n                !paramInTargetPosition[commitmentPropertyName as keyof typeof paramInTargetPosition] ||\n                paramInTargetPosition[commitmentPropertyName as keyof typeof paramInTargetPosition] === 'finalized'\n            ) {\n                // Delete the commitment property; `finalized` is already the server default.\n                const nextParams = [...params];\n                const {\n                    [commitmentPropertyName as keyof typeof paramInTargetPosition]: _, // eslint-disable-line @typescript-eslint/no-unused-vars\n                    ...rest\n                } = paramInTargetPosition;\n                if (Object.keys(rest).length > 0) {\n                    nextParams[optionsObjectPositionInParams] = rest;\n                } else {\n                    if (optionsObjectPositionInParams === nextParams.length - 1) {\n                        nextParams.length--;\n                    } else {\n                        nextParams[optionsObjectPositionInParams] = undefined;\n                    }\n                }\n                return nextParams;\n            }\n        } else if (overrideCommitment !== 'finalized') {\n            // Apply the default commitment.\n            const nextParams = [...params];\n            nextParams[optionsObjectPositionInParams] = {\n                ...paramInTargetPosition,\n                [commitmentPropertyName]: overrideCommitment,\n            };\n            return nextParams;\n        }\n    }\n    return params;\n}\n", "import type { RpcRequest, RpcRequestTransformer } from '@solana/rpc-spec-types';\nimport type { Commitment } from '@solana/rpc-types';\n\nimport { applyDefaultCommitment } from './request-transformer-default-commitment-internal';\n\n/**\n * Creates a transformer that adds the provided default commitment to the configuration object of the request when applicable.\n *\n * @param config\n *\n * @example\n * ```ts\n * import { getDefaultCommitmentRequestTransformer, OPTIONS_OBJECT_POSITION_BY_METHOD } from '@solana/rpc-transformers';\n *\n * const requestTransformer = getDefaultCommitmentRequestTransformer({\n *     defaultCommitment: 'confirmed',\n *     optionsObjectPositionByMethod: OPTIONS_OBJECT_POSITION_BY_METHOD,\n * });\n */\nexport function getDefaultCommitmentRequestTransformer({\n    defaultCommitment,\n    optionsObjectPositionByMethod,\n}: Readonly<{\n    defaultCommitment?: Commitment;\n    optionsObjectPositionByMethod: Record<string, number>;\n}>): RpcRequestTransformer {\n    return <TParams>(request: RpcRequest<TParams>): RpcRequest => {\n        const { params, methodName } = request;\n\n        // We only apply default commitment to array parameters.\n        if (!Array.isArray(params)) {\n            return request;\n        }\n\n        // Find the position of the options object in the parameters and abort if not found.\n        const optionsObjectPositionInParams = optionsObjectPositionByMethod[methodName];\n        if (optionsObjectPositionInParams == null) {\n            return request;\n        }\n\n        return Object.freeze({\n            methodName,\n            params: applyDefaultCommitment({\n                commitmentPropertyName: methodName === 'sendTransaction' ? 'preflightCommitment' : 'commitment',\n                optionsObjectPositionInParams,\n                overrideCommitment: defaultCommitment,\n                params,\n            }),\n        });\n    };\n}\n", "import { KeyPath, TraversalState } from './tree-traversal';\n\nexport function getIntegerOverflowNodeVisitor(onIntegerOverflow: (keyPath: KeyPath, value: bigint) => void) {\n    return <T>(value: T, { keyPath }: TraversalState): T => {\n        if (typeof value === 'bigint') {\n            if (onIntegerOverflow && (value > Number.MAX_SAFE_INTEGER || value < -Number.MAX_SAFE_INTEGER)) {\n                onIntegerOverflow(keyPath as (number | string)[], value);\n            }\n        }\n        return value;\n    };\n}\n", "import { RpcRequest } from '@solana/rpc-spec-types';\n\nimport { getIntegerOverflowNodeVisitor } from './request-transformer-integer-overflow-internal';\nimport { getTreeWalkerRequestTransformer, KeyPath } from './tree-traversal';\n\nexport type IntegerOverflowHandler = (request: RpcRequest, keyPath: KeyPath, value: bigint) => void;\n\n/**\n * Creates a transformer that traverses the request parameters and executes the provided handler\n * when an integer overflow is detected.\n *\n * @example\n * ```ts\n * import { getIntegerOverflowRequestTransformer } from '@solana/rpc-transformers';\n *\n * const requestTransformer = getIntegerOverflowRequestTransformer((request, keyPath, value) => {\n *     throw new Error(`Integer overflow at ${keyPath.join('.')}: ${value}`);\n * });\n * ```\n */\nexport function getIntegerOverflowRequestTransformer(onIntegerOverflow: IntegerOverflowHandler) {\n    return <TParams>(request: RpcRequest<TParams>): RpcRequest => {\n        const transformer = getTreeWalkerRequestTransformer(\n            [getIntegerOverflowNodeVisitor((...args) => onIntegerOverflow(request, ...args))],\n            { keyPath: [] },\n        );\n        return transformer(request);\n    };\n}\n", "export const OPTIONS_OBJECT_POSITION_BY_METHOD: Record<string, number> = {\n    accountNotifications: 1,\n    blockNotifications: 1,\n    getAccountInfo: 1,\n    getBalance: 1,\n    getBlock: 1,\n    getBlockHeight: 0,\n    getBlockProduction: 0,\n    getBlocks: 2,\n    getBlocksWithLimit: 2,\n    getEpochInfo: 0,\n    getFeeForMessage: 1,\n    getInflationGovernor: 0,\n    getInflationReward: 1,\n    getLargestAccounts: 0,\n    getLatestBlockhash: 0,\n    getLeaderSchedule: 1,\n    getMinimumBalanceForRentExemption: 1,\n    getMultipleAccounts: 1,\n    getProgramAccounts: 1,\n    getSignaturesForAddress: 1,\n    getSlot: 0,\n    getSlotLeader: 0,\n    getStakeMinimumDelegation: 0,\n    getSupply: 0,\n    getTokenAccountBalance: 1,\n    getTokenAccountsByDelegate: 2,\n    getTokenAccountsByOwner: 2,\n    getTokenLargestAccounts: 1,\n    getTokenSupply: 1,\n    getTransaction: 1,\n    getTransactionCount: 0,\n    getVoteAccounts: 0,\n    isBlockhashValid: 1,\n    logsNotifications: 1,\n    programNotifications: 1,\n    requestAirdrop: 2,\n    sendTransaction: 1,\n    signatureNotifications: 1,\n    simulateTransaction: 1,\n};\n", "import { pipe } from '@solana/functional';\nimport { RpcRequest, RpcRequestTransformer } from '@solana/rpc-spec-types';\nimport { Commitment } from '@solana/rpc-types';\n\nimport { getBigIntDowncastRequestTransformer } from './request-transformer-bigint-downcast';\nimport { getDefaultCommitmentRequestTransformer } from './request-transformer-default-commitment';\nimport { getIntegerOverflowRequestTransformer, IntegerOverflowHandler } from './request-transformer-integer-overflow';\nimport { OPTIONS_OBJECT_POSITION_BY_METHOD } from './request-transformer-options-object-position-config';\n\nexport type RequestTransformerConfig = Readonly<{\n    /**\n     * An optional {@link Commitment} value to use as the default when none is supplied by the\n     * caller.\n     */\n    defaultCommitment?: Commitment;\n    /**\n     * An optional function that will be called whenever a `bigint` input exceeds that which can be\n     * expressed using JavaScript numbers.\n     *\n     * This is used in the default {@link SolanaRpcSubscriptionsApi} to throw an exception rather\n     * than to allow truncated values to propagate through a program.\n     */\n    onIntegerOverflow?: IntegerOverflowHandler;\n}>;\n\n/**\n * Returns the default request transformer for the Solana RPC API.\n *\n * Under the hood, this function composes multiple\n * {@link RpcRequestTransformer | RpcRequestTransformers} together such as the\n * {@link getDefaultCommitmentTransformer}, the {@link getIntegerOverflowRequestTransformer} and the\n * {@link getBigIntDowncastRequestTransformer}.\n *\n * @example\n * ```ts\n * import { getDefaultRequestTransformerForSolanaRpc } from '@solana/rpc-transformers';\n *\n * const requestTransformer = getDefaultRequestTransformerForSolanaRpc({\n *     defaultCommitment: 'confirmed',\n *     onIntegerOverflow: (request, keyPath, value) => {\n *         throw new Error(`Integer overflow at ${keyPath.join('.')}: ${value}`);\n *     },\n * });\n * ```\n */\nexport function getDefaultRequestTransformerForSolanaRpc(config?: RequestTransformerConfig): RpcRequestTransformer {\n    const handleIntegerOverflow = config?.onIntegerOverflow;\n    return (request: RpcRequest): RpcRequest => {\n        return pipe(\n            request,\n            handleIntegerOverflow ? getIntegerOverflowRequestTransformer(handleIntegerOverflow) : r => r,\n            getBigIntDowncastRequestTransformer(),\n            getDefaultCommitmentRequestTransformer({\n                defaultCommitment: config?.defaultCommitment,\n                optionsObjectPositionByMethod: OPTIONS_OBJECT_POSITION_BY_METHOD,\n            }),\n        );\n    };\n}\n", "import { KeyPath, KEYPATH_WILDCARD, TraversalState } from './tree-traversal';\n\nexport function getBigIntUpcastVisitor(allowedNumericKeyPaths: readonly KeyPath[]) {\n    return function upcastNodeToBigIntIfNumber(value: unknown, { keyPath }: TraversalState) {\n        const isInteger = (typeof value === 'number' && Number.isInteger(value)) || typeof value === 'bigint';\n        if (!isInteger) return value;\n        if (keyPathIsAllowedToBeNumeric(keyPath, allowedNumericKeyPaths)) {\n            return Number(value);\n        } else {\n            return BigInt(value);\n        }\n    };\n}\n\nfunction keyPathIsAllowedToBeNumeric(keyPath: KeyPath, allowedNumericKeyPaths: readonly KeyPath[]) {\n    return allowedNumericKeyPaths.some(prohibitedKeyPath => {\n        if (prohibitedKeyPath.length !== keyPath.length) {\n            return false;\n        }\n        for (let ii = keyPath.length - 1; ii >= 0; ii--) {\n            const keyPathPart = keyPath[ii];\n            const prohibitedKeyPathPart = prohibitedKeyPath[ii];\n            if (\n                prohibitedKeyPathPart !== keyPathPart &&\n                (prohibitedKeyPathPart !== KEYPATH_WILDCARD || typeof keyPathPart !== 'number')\n            ) {\n                return false;\n            }\n        }\n        return true;\n    });\n}\n", "import { getBigIntUpcastVisitor } from './response-transformer-bigint-upcast-internal';\nimport { getTreeWalkerResponseTransformer, KeyPath } from './tree-traversal';\n\n/**\n * Returns a transformer that upcasts all `Number` values to `BigInts` unless they match within the\n * provided {@link KeyPath | KeyPaths}. In other words, the provided {@link KeyPath | KeyPaths} will\n * remain as `Number` values, any other numeric value will be upcasted to a `BigInt`.\n *\n * Note that you can use {@link KEYPATH_WILDCARD} to match any key within a {@link KeyPath}.\n *\n * @example\n * ```ts\n * import { getBigIntUpcastResponseTransformer } from '@solana/rpc-transformers';\n *\n * const responseTransformer = getBigIntUpcastResponseTransformer([\n *     ['index'],\n *     ['instructions', KEYPATH_WILDCARD, 'accounts', KEYPATH_WILDCARD],\n *     ['instructions', KEYPATH_WILDCARD, 'programIdIndex'],\n *     ['instructions', KEYPATH_WILDCARD, 'stackHeight'],\n * ]);\n * ```\n */\nexport function getBigIntUpcastResponseTransformer(allowedNumericKeyPaths: readonly KeyPath[]) {\n    return getTreeWalkerResponseTransformer([getBigIntUpcastVisitor(allowedNumericKeyPaths)], { keyPath: [] });\n}\n", "import { RpcResponseTransformer } from '@solana/rpc-spec-types';\n\ntype JsonRpcResponse = { result: unknown };\n\n/**\n * Returns a transformer that extracts the `result` field from the body of the RPC response.\n *\n * For instance, we go from `{ jsonrpc: '2.0', result: 'foo', id: 1 }` to `'foo'`.\n *\n * @example\n * ```ts\n * import { getResultResponseTransformer } from '@solana/rpc-transformers';\n *\n * const responseTransformer = getResultResponseTransformer();\n * ```\n */\nexport function getResultResponseTransformer(): RpcResponseTransformer {\n    return json => (json as JsonRpcResponse).result;\n}\n", "import { getSolanaErrorFromJsonRpcError } from '@solana/errors';\nimport { RpcResponseTransformer } from '@solana/rpc-spec-types';\n\ntype JsonRpcResponse = { error: Parameters<typeof getSolanaErrorFromJsonRpcError>[0] } | { result: unknown };\n\n/**\n * Returns a transformer that throws a {@link SolanaError} with the appropriate RPC error code if\n * the body of the RPC response contains an error.\n *\n * @example\n * ```ts\n * import { getThrowSolanaErrorResponseTransformer } from '@solana/rpc-transformers';\n *\n * const responseTransformer = getThrowSolanaErrorResponseTransformer();\n * ```\n */\nexport function getThrowSolanaErrorResponseTransformer(): RpcResponseTransformer {\n    return json => {\n        const jsonRpcResponse = json as JsonRpcResponse;\n        if ('error' in jsonRpcResponse) {\n            throw getSolanaErrorFromJsonRpcError(jsonRpcResponse.error);\n        }\n        return jsonRpcResponse;\n    };\n}\n", "import { pipe } from '@solana/functional';\nimport { RpcRequest, RpcResponse, RpcResponseTransformer } from '@solana/rpc-spec-types';\n\nimport { AllowedNumericKeypaths } from './response-transformer-allowed-numeric-values';\nimport { getBigIntUpcastResponseTransformer } from './response-transformer-bigint-upcast';\nimport { getResultResponseTransformer } from './response-transformer-result';\nimport { getThrowSolanaErrorResponseTransformer } from './response-transformer-throw-solana-error';\n\nexport type ResponseTransformerConfig<TApi> = Readonly<{\n    /**\n     * An optional map from the name of an API method to an array of {@link KeyPath | KeyPaths}\n     * pointing to values in the response that should materialize in the application as `Number`\n     * instead of `BigInt`.\n     */\n    allowedNumericKeyPaths?: AllowedNumericKeypaths<TApi>;\n}>;\n\n/**\n * Returns the default response transformer for the Solana RPC API.\n *\n * Under the hood, this function composes multiple\n * {@link RpcResponseTransformer | RpcResponseTransformers} together such as the\n * {@link getThrowSolanaErrorResponseTransformer}, the {@link getResultResponseTransformer} and the\n * {@link getBigIntUpcastResponseTransformer}.\n *\n * @example\n * ```ts\n * import { getDefaultResponseTransformerForSolanaRpc } from '@solana/rpc-transformers';\n *\n * const responseTransformer = getDefaultResponseTransformerForSolanaRpc({\n *     allowedNumericKeyPaths: getAllowedNumericKeypaths(),\n * });\n * ```\n */\nexport function getDefaultResponseTransformerForSolanaRpc<TApi>(\n    config?: ResponseTransformerConfig<TApi>,\n): RpcResponseTransformer {\n    return (response: RpcResponse, request: RpcRequest): RpcResponse => {\n        const methodName = request.methodName as keyof TApi;\n        const keyPaths =\n            config?.allowedNumericKeyPaths && methodName ? config.allowedNumericKeyPaths[methodName] : undefined;\n        return pipe(\n            response,\n            r => getThrowSolanaErrorResponseTransformer()(r, request),\n            r => getResultResponseTransformer()(r, request),\n            r => getBigIntUpcastResponseTransformer(keyPaths ?? [])(r, request),\n        );\n    };\n}\n\n/**\n * Returns the default response transformer for the Solana RPC Subscriptions API.\n *\n * Under the hood, this function composes the {@link getBigIntUpcastResponseTransformer}.\n *\n * @example\n * ```ts\n * import { getDefaultResponseTransformerForSolanaRpcSubscriptions } from '@solana/rpc-transformers';\n *\n * const responseTransformer = getDefaultResponseTransformerForSolanaRpcSubscriptions({\n *     allowedNumericKeyPaths: getAllowedNumericKeypaths(),\n * });\n * ```\n */\nexport function getDefaultResponseTransformerForSolanaRpcSubscriptions<TApi>(\n    config?: ResponseTransformerConfig<TApi>,\n): RpcResponseTransformer {\n    return (response: RpcResponse, request: RpcRequest): RpcResponse => {\n        const methodName = request.methodName as keyof TApi;\n        const keyPaths =\n            config?.allowedNumericKeyPaths && methodName ? config.allowedNumericKeyPaths[methodName] : undefined;\n        return pipe(response, r => getBigIntUpcastResponseTransformer(keyPaths ?? [])(r, request));\n    };\n}\n", "import { KeyPath, KEYPATH_WILDCARD } from './tree-traversal';\n\nexport type AllowedNumericKeypaths<TApi> = Partial<Record<keyof TApi, readonly KeyPath[]>>;\n\n// Numeric values nested in `jsonParsed` accounts\nexport const jsonParsedTokenAccountsConfigs = [\n    // parsed Token/Token22 token account\n    ['data', 'parsed', 'info', 'tokenAmount', 'decimals'],\n    ['data', 'parsed', 'info', 'tokenAmount', 'uiAmount'],\n    ['data', 'parsed', 'info', 'rentExemptReserve', 'decimals'],\n    ['data', 'parsed', 'info', 'rentExemptReserve', 'uiAmount'],\n    ['data', 'parsed', 'info', 'delegatedAmount', 'decimals'],\n    ['data', 'parsed', 'info', 'delegatedAmount', 'uiAmount'],\n    ['data', 'parsed', 'info', 'extensions', KEYPATH_WILDCARD, 'state', 'olderTransferFee', 'transferFeeBasisPoints'],\n    ['data', 'parsed', 'info', 'extensions', KEYPATH_WILDCARD, 'state', 'newerTransferFee', 'transferFeeBasisPoints'],\n    ['data', 'parsed', 'info', 'extensions', KEYPATH_WILDCARD, 'state', 'preUpdateAverageRate'],\n    ['data', 'parsed', 'info', 'extensions', KEYPATH_WILDCARD, 'state', 'currentRate'],\n];\nexport const jsonParsedAccountsConfigs = [\n    ...jsonParsedTokenAccountsConfigs,\n    // parsed AddressTableLookup account\n    ['data', 'parsed', 'info', 'lastExtendedSlotStartIndex'],\n    // parsed Config account\n    ['data', 'parsed', 'info', 'slashPenalty'],\n    ['data', 'parsed', 'info', 'warmupCooldownRate'],\n    // parsed Token/Token22 mint account\n    ['data', 'parsed', 'info', 'decimals'],\n    // parsed Token/Token22 multisig account\n    ['data', 'parsed', 'info', 'numRequiredSigners'],\n    ['data', 'parsed', 'info', 'numValidSigners'],\n    // parsed Stake account\n    ['data', 'parsed', 'info', 'stake', 'delegation', 'warmupCooldownRate'],\n    // parsed Sysvar rent account\n    ['data', 'parsed', 'info', 'exemptionThreshold'],\n    ['data', 'parsed', 'info', 'burnPercent'],\n    // parsed Vote account\n    ['data', 'parsed', 'info', 'commission'],\n    ['data', 'parsed', 'info', 'votes', KEYPATH_WILDCARD, 'confirmationCount'],\n];\nexport const innerInstructionsConfigs = [\n    ['index'],\n    ['instructions', KEYPATH_WILDCARD, 'accounts', KEYPATH_WILDCARD],\n    ['instructions', KEYPATH_WILDCARD, 'programIdIndex'],\n    ['instructions', KEYPATH_WILDCARD, 'stackHeight'],\n];\nexport const messageConfig = [\n    ['addressTableLookups', KEYPATH_WILDCARD, 'writableIndexes', KEYPATH_WILDCARD],\n    ['addressTableLookups', KEYPATH_WILDCARD, 'readonlyIndexes', KEYPATH_WILDCARD],\n    ['header', 'numReadonlySignedAccounts'],\n    ['header', 'numReadonlyUnsignedAccounts'],\n    ['header', 'numRequiredSignatures'],\n    ['instructions', KEYPATH_WILDCARD, 'accounts', KEYPATH_WILDCARD],\n    ['instructions', KEYPATH_WILDCARD, 'programIdIndex'],\n    ['instructions', KEYPATH_WILDCARD, 'stackHeight'],\n] as const;\n"]}