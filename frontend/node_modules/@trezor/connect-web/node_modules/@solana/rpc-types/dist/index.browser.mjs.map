{"version": 3, "sources": ["../src/blockhash.ts", "../src/cluster-url.ts", "../src/commitment.ts", "../src/lamports.ts", "../src/stringified-bigint.ts", "../src/stringified-number.ts", "../src/unix-timestamp.ts"], "names": ["SolanaError", "combineCodec"], "mappings": ";;;;;;AAqBA,IAAI,qBAAA;AACJ,IAAI,qBAAA;AAEJ,SAAS,wBAA4C,GAAA;AACjD,EAAI,IAAA,CAAC,qBAAuB,EAAA,qBAAA,GAAwB,gBAAiB,EAAA;AACrE,EAAO,OAAA,qBAAA;AACX;AAEA,SAAS,wBAA4C,GAAA;AACjD,EAAI,IAAA,CAAC,qBAAuB,EAAA,qBAAA,GAAwB,gBAAiB,EAAA;AACrE,EAAO,OAAA,qBAAA;AACX;AAoBO,SAAS,YAAY,iBAA2D,EAAA;AAEnF,EAAA;AAAA;AAAA,IAEI,kBAAkB,MAAS,GAAA,EAAA;AAAA,IAE3B,kBAAkB,MAAS,GAAA;AAAA,IAC7B;AACE,IAAO,OAAA,KAAA;AAAA;AAGX,EAAA,MAAM,gBAAgB,wBAAyB,EAAA;AAC/C,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,MAAA,CAAO,iBAAiB,CAAA;AACpD,EAAA,MAAM,WAAW,KAAM,CAAA,UAAA;AACvB,EAAA,IAAI,aAAa,EAAI,EAAA;AACjB,IAAO,OAAA,KAAA;AAAA;AAEX,EAAO,OAAA,IAAA;AACX;AA2BO,SAAS,kBAAkB,iBAAmE,EAAA;AAEjG,EAAA;AAAA;AAAA,IAEI,kBAAkB,MAAS,GAAA,EAAA;AAAA,IAE3B,kBAAkB,MAAS,GAAA;AAAA,IAC7B;AACE,IAAM,MAAA,IAAI,YAAY,kDAAoD,EAAA;AAAA,MACtE,cAAc,iBAAkB,CAAA;AAAA,KACnC,CAAA;AAAA;AAGL,EAAA,MAAM,gBAAgB,wBAAyB,EAAA;AAC/C,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,MAAA,CAAO,iBAAiB,CAAA;AACpD,EAAA,MAAM,WAAW,KAAM,CAAA,UAAA;AACvB,EAAA,IAAI,aAAa,EAAI,EAAA;AACjB,IAAM,MAAA,IAAI,YAAY,2CAA6C,EAAA;AAAA,MAC/D,YAAc,EAAA;AAAA,KACjB,CAAA;AAAA;AAET;AAwBO,SAAS,UAAU,iBAAsC,EAAA;AAC5D,EAAA,iBAAA,CAAkB,iBAAiB,CAAA;AACnC,EAAO,OAAA,iBAAA;AACX;AAoBO,SAAS,mBAAuD,GAAA;AACnE,EAAO,OAAA,gBAAA;AAAA,IAAiB,cAAA,CAAe,wBAAyB,EAAA,EAAG,EAAE,CAAA;AAAA,IAAG,CAAA,iBAAA,KACpE,UAAU,iBAAiB;AAAA,GAC/B;AACJ;AAoBO,SAAS,mBAAuD,GAAA;AACnE,EAAO,OAAA,cAAA,CAAe,wBAAyB,EAAA,EAAG,EAAE,CAAA;AACxD;AAQO,SAAS,iBAA8D,GAAA;AAC1E,EAAA,OAAO,YAAa,CAAA,mBAAA,EAAuB,EAAA,mBAAA,EAAqB,CAAA;AACpE;AAEO,SAAS,sBAA2D,GAAA;AACvE,EAAO,OAAA,IAAI,IAAK,CAAA,QAAA,CAAS,IAAM,EAAA;AAAA,IAC3B,SAAW,EAAA,OAAA;AAAA,IACX,iBAAmB,EAAA,KAAA;AAAA,IACnB,aAAe,EAAA,UAAA;AAAA,IACf,OAAS,EAAA,KAAA;AAAA,IACT,WAAa,EAAA,SAAA;AAAA,IACb,KAAO,EAAA;AAAA,GACV,CAAE,CAAA,OAAA;AACP;;;AC9MO,SAAS,QAAQ,cAAoC,EAAA;AACxD,EAAO,OAAA,cAAA;AACX;AAEO,SAAS,OAAO,cAAmC,EAAA;AACtD,EAAO,OAAA,cAAA;AACX;AAEO,SAAS,QAAQ,cAAoC,EAAA;AACxD,EAAO,OAAA,cAAA;AACX;ACNA,SAAS,mBAAmB,UAAgC,EAAA;AACxD,EAAA,QAAQ,UAAY;AAAA,IAChB,KAAK,WAAA;AACD,MAAO,OAAA,CAAA;AAAA,IACX,KAAK,WAAA;AACD,MAAO,OAAA,CAAA;AAAA,IACX,KAAK,WAAA;AACD,MAAO,OAAA,CAAA;AAAA,IACX;AACI,MAAM,MAAA,IAAIA,YAAY,4DAA8D,EAAA;AAAA,QAChF,eAAiB,EAAA;AAAA,OACpB,CAAA;AAAA;AAEb;AAEO,SAAS,oBAAA,CAAqB,GAAe,CAA2B,EAAA;AAC3E,EAAA,IAAI,MAAM,CAAG,EAAA;AACT,IAAO,OAAA,CAAA;AAAA;AAEX,EAAA,OAAO,mBAAmB,CAAC,CAAA,GAAI,kBAAmB,CAAA,CAAC,IAAI,EAAK,GAAA,CAAA;AAChE;ACRA,IAAM,WAAc,GAAA,qBAAA;AAEpB,IAAI,kBAAA;AACJ,IAAI,kBAAA;AAEJ,SAAS,qBAA8D,GAAA;AACnE,EAAI,IAAA,CAAC,kBAAoB,EAAA,kBAAA,GAAqB,aAAc,EAAA;AAC5D,EAAO,OAAA,kBAAA;AACX;AAEA,SAAS,qBAAqD,GAAA;AAC1D,EAAI,IAAA,CAAC,kBAAoB,EAAA,kBAAA,GAAqB,aAAc,EAAA;AAC5D,EAAO,OAAA,kBAAA;AACX;AAmBO,SAAS,WAAW,gBAAwD,EAAA;AAC/E,EAAO,OAAA,gBAAA,IAAoB,KAAK,gBAAoB,IAAA,WAAA;AACxD;AA8BO,SAAS,iBAAiB,gBAAgE,EAAA;AAC7F,EAAI,IAAA,gBAAA,GAAmB,CAAK,IAAA,gBAAA,GAAmB,WAAa,EAAA;AACxD,IAAM,MAAA,IAAIA,YAAY,mCAAmC,CAAA;AAAA;AAEjE;AAaO,SAAS,SAAS,gBAAoC,EAAA;AACzD,EAAA,gBAAA,CAAiB,gBAAgB,CAAA;AACjC,EAAO,OAAA,gBAAA;AACX;AAQO,SAAS,yBAA2D,GAAA;AACvE,EAAO,OAAA,kBAAA,CAAmB,uBAAuB,CAAA;AACrD;AAkBO,SAAS,mBACZ,YACmE,EAAA;AACnE,EAAO,OAAA,YAAA;AACX;AAMO,SAAS,yBAA2D,GAAA;AACvE,EAAO,OAAA,kBAAA,CAAmB,uBAAuB,CAAA;AACrD;AAmBO,SAAS,mBACZ,YACmE,EAAA;AACnE,EAAO,OAAA,gBAAA;AAAA,IAA4C,YAAA;AAAA,IAAc,CAAA,KAAA,KAC7D,SAAS,OAAO,KAAA,KAAU,WAAW,KAAQ,GAAA,MAAA,CAAO,KAAK,CAAC;AAAA,GAC9D;AACJ;AAQO,SAAS,uBAAiE,GAAA;AAC7E,EAAA,OAAOC,YAAa,CAAA,yBAAA,EAA6B,EAAA,yBAAA,EAA2B,CAAA;AAChF;AAQO,SAAS,iBACZ,UACuE,EAAA;AACvE,EAAA,OAAOA,aAAa,kBAAmB,CAAA,UAAU,CAAG,EAAA,kBAAA,CAAmB,UAAU,CAAC,CAAA;AAEtF;ACzKO,SAAS,oBAAoB,cAA6D,EAAA;AAC7F,EAAI,IAAA;AACA,IAAA,MAAA,CAAO,cAAc,CAAA;AACrB,IAAO,OAAA,IAAA;AAAA,GACH,CAAA,MAAA;AACJ,IAAO,OAAA,KAAA;AAAA;AAEf;AAwBO,SAAS,0BAA0B,cAAqE,EAAA;AAC3G,EAAI,IAAA;AACA,IAAA,MAAA,CAAO,cAAc,CAAA;AAAA,GACjB,CAAA,MAAA;AACJ,IAAM,MAAA,IAAID,YAAY,qCAAuC,EAAA;AAAA,MACzD,KAAO,EAAA;AAAA,KACV,CAAA;AAAA;AAET;AAaO,SAAS,kBAAkB,cAA2C,EAAA;AACzE,EAAA,yBAAA,CAA0B,cAAc,CAAA;AACxC,EAAO,OAAA,cAAA;AACX;ACtDO,SAAS,oBAAoB,cAA6D,EAAA;AAC7F,EAAA,OAAO,CAAC,MAAA,CAAO,KAAM,CAAA,MAAA,CAAO,cAAc,CAAC,CAAA;AAC/C;AAwBO,SAAS,0BAA0B,cAAqE,EAAA;AAC3G,EAAA,IAAI,MAAO,CAAA,KAAA,CAAM,MAAO,CAAA,cAAc,CAAC,CAAG,EAAA;AACtC,IAAM,MAAA,IAAIA,YAAY,qCAAuC,EAAA;AAAA,MACzD,KAAO,EAAA;AAAA,KACV,CAAA;AAAA;AAET;AAaO,SAAS,kBAAkB,cAA2C,EAAA;AACzE,EAAA,yBAAA,CAA0B,cAAc,CAAA;AACxC,EAAO,OAAA,cAAA;AACX;AC/DA,IAAM,WAAc,GAAA,oBAAA;AACpB,IAAM,cAAc,CAAC,oBAAA;AAoBd,SAAS,gBAAgB,iBAA+D,EAAA;AAC3F,EAAO,OAAA,iBAAA,IAAqB,eAAe,iBAAqB,IAAA,WAAA;AACpE;AA2BO,SAAS,sBAAsB,iBAAuE,EAAA;AACzG,EAAI,IAAA,iBAAA,GAAoB,WAAe,IAAA,iBAAA,GAAoB,WAAa,EAAA;AACpE,IAAM,MAAA,IAAIA,YAAY,oCAAsC,EAAA;AAAA,MACxD,KAAO,EAAA;AAAA,KACV,CAAA;AAAA;AAET;AAaO,SAAS,cAAc,iBAA0C,EAAA;AACpE,EAAA,qBAAA,CAAsB,iBAAiB,CAAA;AACvC,EAAO,OAAA,iBAAA;AACX", "file": "index.browser.mjs", "sourcesContent": ["import {\n    combineCodec,\n    Decoder,\n    Encoder,\n    fixDecoderSize,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoderSize,\n    transformEncoder,\n} from '@solana/codecs-core';\nimport { getBase58Decoder, getBase58Encoder } from '@solana/codecs-strings';\nimport {\n    SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH,\n    SolanaError,\n} from '@solana/errors';\nimport { Brand, EncodedString } from '@solana/nominal-types';\n\nexport type Blockhash = Brand<EncodedString<string, 'base58'>, 'Blockhash'>;\n\nlet memoizedBase58Encoder: Encoder<string> | undefined;\nlet memoizedBase58Decoder: Decoder<string> | undefined;\n\nfunction getMemoizedBase58Encoder(): Encoder<string> {\n    if (!memoizedBase58Encoder) memoizedBase58Encoder = getBase58Encoder();\n    return memoizedBase58Encoder;\n}\n\nfunction getMemoizedBase58Decoder(): Decoder<string> {\n    if (!memoizedBase58Decoder) memoizedBase58Decoder = getBase58Decoder();\n    return memoizedBase58Decoder;\n}\n\n/**\n * A type guard that returns `true` if the input string conforms to the {@link Blockhash} type, and\n * refines its type for use in your program.\n *\n * @example\n * ```ts\n * import { isBlockhash } from '@solana/rpc-types';\n *\n * if (isBlockhash(blockhash)) {\n *     // At this point, `blockhash` has been refined to a\n *     // `Blockhash` that can be used with the RPC.\n *     const { value: isValid } = await rpc.isBlockhashValid(blockhash).send();\n *     setBlockhashIsFresh(isValid);\n * } else {\n *     setError(`${blockhash} is not a blockhash`);\n * }\n * ```\n */\nexport function isBlockhash(putativeBlockhash: string): putativeBlockhash is Blockhash {\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest value (32 bytes of zeroes)\n        putativeBlockhash.length < 32 ||\n        // Highest value (32 bytes of 255)\n        putativeBlockhash.length > 44\n    ) {\n        return false;\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const base58Encoder = getMemoizedBase58Encoder();\n    const bytes = base58Encoder.encode(putativeBlockhash);\n    const numBytes = bytes.byteLength;\n    if (numBytes !== 32) {\n        return false;\n    }\n    return true;\n}\n\n/**\n * From time to time you might acquire a string, that you expect to validate as a blockhash, from an\n * untrusted network API or user input. Use this function to assert that such an arbitrary string is\n * a base58-encoded blockhash.\n *\n * @example\n * ```ts\n * import { assertIsBlockhash } from '@solana/rpc-types';\n *\n * // Imagine a function that determines whether a blockhash is fresh when a user submits a form.\n * function handleSubmit() {\n *     // We know only that what the user typed conforms to the `string` type.\n *     const blockhash: string = blockhashInput.value;\n *     try {\n *         // If this type assertion function doesn't throw, then\n *         // Typescript will upcast `blockhash` to `Blockhash`.\n *         assertIsBlockhash(blockhash);\n *         // At this point, `blockhash` is a `Blockhash` that can be used with the RPC.\n *         const { value: isValid } = await rpc.isBlockhashValid(blockhash).send();\n *     } catch (e) {\n *         // `blockhash` turned out not to be a base58-encoded blockhash\n *     }\n * }\n * ```\n */\nexport function assertIsBlockhash(putativeBlockhash: string): asserts putativeBlockhash is Blockhash {\n    // Fast-path; see if the input string is of an acceptable length.\n    if (\n        // Lowest value (32 bytes of zeroes)\n        putativeBlockhash.length < 32 ||\n        // Highest value (32 bytes of 255)\n        putativeBlockhash.length > 44\n    ) {\n        throw new SolanaError(SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE, {\n            actualLength: putativeBlockhash.length,\n        });\n    }\n    // Slow-path; actually attempt to decode the input string.\n    const base58Encoder = getMemoizedBase58Encoder();\n    const bytes = base58Encoder.encode(putativeBlockhash);\n    const numBytes = bytes.byteLength;\n    if (numBytes !== 32) {\n        throw new SolanaError(SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH, {\n            actualLength: numBytes,\n        });\n    }\n}\n\n/**\n * Combines _asserting_ that a string is a blockhash with _coercing_ it to the {@link Blockhash}\n * type. It's most useful with untrusted input.\n *\n * @example\n * ```ts\n * import { blockhash } from '@solana/rpc-types';\n *\n * const { value: isValid } = await rpc.isBlockhashValid(blockhash(blockhashFromUserInput)).send();\n * ```\n *\n * > [!TIP]\n * > When starting from a known-good blockhash as a string, it's more efficient to typecast it\n * rather than to use the {@link blockhash} helper, because the helper unconditionally performs\n * validation on its input.\n * >\n * > ```ts\n * > import { Blockhash } from '@solana/rpc-types';\n * >\n * > const blockhash = 'ABmPH5KDXX99u6woqFS5vfBGSNyKG42SzpvBMWWqAy48' as Blockhash;\n * > ```\n */\nexport function blockhash(putativeBlockhash: string): Blockhash {\n    assertIsBlockhash(putativeBlockhash);\n    return putativeBlockhash;\n}\n\n/**\n * Returns an encoder that you can use to encode a base58-encoded blockhash to a byte array.\n *\n * @example\n * ```ts\n * import { getBlockhashEncoder } from '@solana/rpc-types';\n *\n * const blockhash = 'ABmPH5KDXX99u6woqFS5vfBGSNyKG42SzpvBMWWqAy48' as Blockhash;\n * const blockhashEncoder = getBlockhashEncoder();\n * const blockhashBytes = blockhashEncoder.encode(blockhash);\n * // Uint8Array(32) [\n * //   136, 123,  44, 249,  43,  19,  60,  14,\n * //   144,  16, 168, 241, 121, 111,  70, 232,\n * //   186,  26, 140, 202, 213,  64, 231,  82,\n * //   179,  66, 103, 237,  52, 117, 217,  93\n * // ]\n * ```\n */\nexport function getBlockhashEncoder(): FixedSizeEncoder<Blockhash, 32> {\n    return transformEncoder(fixEncoderSize(getMemoizedBase58Encoder(), 32), putativeBlockhash =>\n        blockhash(putativeBlockhash),\n    );\n}\n\n/**\n * Returns a decoder that you can use to convert an array of 32 bytes representing a blockhash to\n * the base58-encoded representation of that blockhash.\n *\n * @example\n * ```ts\n * import { getBlockhashDecoder } from '@solana/rpc-types';\n *\n * const blockhashBytes = new Uint8Array([\n *     136, 123,  44, 249,  43,  19,  60,  14,\n *     144,  16, 168, 241, 121, 111,  70, 232,\n *     186,  26, 140, 202, 213,  64, 231,  82,\n *     179,  66, 103, 237,  52, 117, 217,  93\n * ]);\n * const blockhashDecoder = getBlockhashDecoder();\n * const blockhash = blockhashDecoder.decode(blockhashBytes); // ABmPH5KDXX99u6woqFS5vfBGSNyKG42SzpvBMWWqAy48\n * ```\n */\nexport function getBlockhashDecoder(): FixedSizeDecoder<Blockhash, 32> {\n    return fixDecoderSize(getMemoizedBase58Decoder(), 32) as FixedSizeDecoder<Blockhash, 32>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to a base-58 encoded blockhash.\n *\n * @see {@link getBlockhashDecoder}\n * @see {@link getBlockhashEncoder}\n */\nexport function getBlockhashCodec(): FixedSizeCodec<Blockhash, Blockhash, 32> {\n    return combineCodec(getBlockhashEncoder(), getBlockhashDecoder());\n}\n\nexport function getBlockhashComparator(): (x: string, y: string) => number {\n    return new Intl.Collator('en', {\n        caseFirst: 'lower',\n        ignorePunctuation: false,\n        localeMatcher: 'best fit',\n        numeric: false,\n        sensitivity: 'variant',\n        usage: 'sort',\n    }).compare;\n}\n", "export type MainnetUrl = string & { '~cluster': 'mainnet' };\nexport type DevnetUrl = string & { '~cluster': 'devnet' };\nexport type TestnetUrl = string & { '~cluster': 'testnet' };\nexport type ClusterUrl = DevnetUrl | MainnetUrl | TestnetUrl | string;\n\n/** Given a URL casts it to a type that is only accepted where mainnet URLs are expected. */\nexport function mainnet(putativeString: string): MainnetUrl {\n    return putativeString as MainnetUrl;\n}\n/** Given a URL casts it to a type that is only accepted where devnet URLs are expected. */\nexport function devnet(putativeString: string): DevnetUrl {\n    return putativeString as DevnetUrl;\n}\n/** Given a URL casts it to a type that is only accepted where testnet URLs are expected. */\nexport function testnet(putativeString: string): TestnetUrl {\n    return putativeString as TestnetUrl;\n}\n", "import { SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE, SolanaError } from '@solana/errors';\n\n/**\n * A union of all possible commitment statuses -- each a measure of the network confirmation and\n * stake levels on a particular block.\n *\n * Read more about the statuses themselves, [here](https://docs.solana.com/cluster/commitments).\n */\nexport type Commitment = 'confirmed' | 'finalized' | 'processed';\n\nfunction getCommitmentScore(commitment: Commitment): number {\n    switch (commitment) {\n        case 'finalized':\n            return 2;\n        case 'confirmed':\n            return 1;\n        case 'processed':\n            return 0;\n        default:\n            throw new SolanaError(SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE, {\n                unexpectedValue: commitment satisfies never,\n            });\n    }\n}\n\nexport function commitmentComparator(a: Commitment, b: Commitment): -1 | 0 | 1 {\n    if (a === b) {\n        return 0;\n    }\n    return getCommitmentScore(a) < getCommitmentScore(b) ? -1 : 1;\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n} from '@solana/codecs-core';\nimport { getU64Decoder, getU64Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\nimport { SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE, SolanaError } from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\n\n/**\n * Represents an integer value denominated in Lamports (ie. $1 \\times 10^{-9}$ ◎).\n *\n * It is represented as a `bigint` in client code and an `u64` in server code.\n */\nexport type Lamports = Brand<bigint, 'Lamports'>;\n\n// Largest possible value to be represented by a u64\nconst maxU64Value = 18446744073709551615n; // 2n ** 64n - 1n\n\nlet memoizedU64Encoder: FixedSizeEncoder<bigint | number, 8> | undefined;\nlet memoizedU64Decoder: FixedSizeDecoder<bigint, 8> | undefined;\n\nfunction getMemoizedU64Encoder(): FixedSizeEncoder<bigint | number, 8> {\n    if (!memoizedU64Encoder) memoizedU64Encoder = getU64Encoder();\n    return memoizedU64Encoder;\n}\n\nfunction getMemoizedU64Decoder(): FixedSizeDecoder<bigint, 8> {\n    if (!memoizedU64Decoder) memoizedU64Decoder = getU64Decoder();\n    return memoizedU64Decoder;\n}\n\n/**\n * This is a type guard that accepts a `bigint` as input. It will both return `true` if the integer\n * conforms to the {@link Lamports} type and will refine the type for use in your program.\n *\n * @example\n * ```ts\n * import { isLamports } from '@solana/rpc-types';\n *\n * if (isLamports(lamports)) {\n *     // At this point, `lamports` has been refined to a\n *     // `Lamports` that can be used anywhere Lamports are expected.\n *     await transfer(fromAddress, toAddress, lamports);\n * } else {\n *     setError(`${lamports} is not a quantity of Lamports`);\n * }\n * ```\n */\nexport function isLamports(putativeLamports: bigint): putativeLamports is Lamports {\n    return putativeLamports >= 0 && putativeLamports <= maxU64Value;\n}\n\n/**\n * Lamport values returned from the RPC API conform to the type {@link Lamports}. You can use a\n * value of that type wherever a quantity of Lamports is expected.\n *\n * @example\n * From time to time you might acquire a number that you expect to be a quantity of Lamports, from\n * an untrusted network API or user input. To assert that such an arbitrary number is usable as a\n * quantity of Lamports, use this function.\n *\n * ```ts\n * import { assertIsLamports } from '@solana/rpc-types';\n *\n * // Imagine a function that creates a transfer instruction when a user submits a form.\n * function handleSubmit() {\n *     // We know only that what the user typed conforms to the `number` type.\n *     const lamports: number = parseInt(quantityInput.value, 10);\n *     try {\n *         // If this type assertion function doesn't throw, then\n *         // Typescript will upcast `lamports` to `Lamports`.\n *         assertIsLamports(lamports);\n *         // At this point, `lamports` is a `Lamports` that can be used anywhere Lamports are expected.\n *         await transfer(fromAddress, toAddress, lamports);\n *     } catch (e) {\n *         // `lamports` turned out not to validate as a quantity of Lamports.\n *     }\n * }\n * ```\n */\nexport function assertIsLamports(putativeLamports: bigint): asserts putativeLamports is Lamports {\n    if (putativeLamports < 0 || putativeLamports > maxU64Value) {\n        throw new SolanaError(SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE);\n    }\n}\n\n/**\n * This helper combines _asserting_ that a number is a possible number of {@link Lamports} with\n * _coercing_ it to the {@link Lamports} type. It's best used with untrusted input.\n *\n * @example\n * ```ts\n * import { lamports } from '@solana/rpc-types';\n *\n * await transfer(address(fromAddress), address(toAddress), lamports(100000n));\n * ```\n */\nexport function lamports(putativeLamports: bigint): Lamports {\n    assertIsLamports(putativeLamports);\n    return putativeLamports;\n}\n\ntype ExtractAdditionalProps<T, U> = Omit<T, keyof U>;\n\n/**\n * Returns an encoder that you can use to encode a 64-bit {@link Lamports} value to 8 bytes in\n * little endian order.\n */\nexport function getDefaultLamportsEncoder(): FixedSizeEncoder<Lamports, 8> {\n    return getLamportsEncoder(getMemoizedU64Encoder());\n}\n\n/**\n * Returns an encoder that you can use to encode a {@link Lamports} value to a byte array.\n *\n * You must supply a number decoder that will determine how encode the numeric value.\n *\n * @example\n * ```ts\n * import { getLamportsEncoder } from '@solana/rpc-types';\n * import { getU16Encoder } from '@solana/codecs-numbers';\n *\n * const lamports = lamports(256n);\n * const lamportsEncoder = getLamportsEncoder(getU16Encoder());\n * const lamportsBytes = lamportsEncoder.encode(lamports);\n * // Uint8Array(2) [ 0, 1 ]\n * ```\n */\nexport function getLamportsEncoder<TEncoder extends NumberEncoder>(\n    innerEncoder: TEncoder,\n): Encoder<Lamports> & ExtractAdditionalProps<TEncoder, NumberEncoder> {\n    return innerEncoder;\n}\n\n/**\n * Returns a decoder that you can use to decode a byte array representing a 64-bit little endian\n * number to a {@link Lamports} value.\n */\nexport function getDefaultLamportsDecoder(): FixedSizeDecoder<Lamports, 8> {\n    return getLamportsDecoder(getMemoizedU64Decoder());\n}\n\n/**\n * Returns a decoder that you can use to convert an array of bytes representing a number to a\n * {@link Lamports} value.\n *\n * You must supply a number decoder that will determine how many bits to use to decode the numeric\n * value.\n *\n * @example\n * ```ts\n * import { getLamportsDecoder } from '@solana/rpc-types';\n * import { getU16Decoder } from '@solana/codecs-numbers';\n *\n * const lamportsBytes = new Uint8Array([ 0, 1 ]);\n * const lamportsDecoder = getLamportsDecoder(getU16Decoder());\n * const lamports = lamportsDecoder.decode(lamportsBytes); // lamports(256n)\n * ```\n */\nexport function getLamportsDecoder<TDecoder extends NumberDecoder>(\n    innerDecoder: TDecoder,\n): Decoder<Lamports> & ExtractAdditionalProps<TDecoder, NumberDecoder> {\n    return transformDecoder<bigint | number, Lamports>(innerDecoder, value =>\n        lamports(typeof value === 'bigint' ? value : BigInt(value)),\n    ) as Decoder<Lamports> & ExtractAdditionalProps<TDecoder, NumberDecoder>;\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to a 64-bit {@link Lamports} value.\n *\n * @see {@link getDefaultLamportsDecoder}\n * @see {@link getDefaultLamportsEncoder}\n */\nexport function getDefaultLamportsCodec(): FixedSizeCodec<Lamports, Lamports, 8> {\n    return combineCodec(getDefaultLamportsEncoder(), getDefaultLamportsDecoder());\n}\n\n/**\n * Returns a codec that you can use to encode from or decode to {@link Lamports} value.\n *\n * @see {@link getLamportsDecoder}\n * @see {@link getLamportsEncoder}\n */\nexport function getLamportsCodec<TCodec extends NumberCodec>(\n    innerCodec: TCodec,\n): Codec<Lamports, Lamports> & ExtractAdditionalProps<TCodec, NumberCodec> {\n    return combineCodec(getLamportsEncoder(innerCodec), getLamportsDecoder(innerCodec)) as Codec<Lamports, Lamports> &\n        ExtractAdditionalProps<TCodec, NumberCodec>;\n}\n", "import { SOLANA_ERROR__MALFORMED_BIGINT_STRING, SolanaError } from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\n\n/**\n * This type represents a `bigint` which has been encoded as a string for transit over a transport\n * that does not support `bigint` values natively. The JSON-RPC is such a transport.\n */\nexport type StringifiedBigInt = Brand<string, 'StringifiedBigInt'>;\n\n/**\n * A type guard that returns `true` if the input string parses as a `BigInt`, and refines its type\n * for use in your program.\n *\n * @example\n * ```ts\n * import { isStringifiedBigInt } from '@solana/rpc-types';\n *\n * if (isStringifiedBigInt(bigintString)) {\n *     // At this point, `bigintString` has been refined to a `StringifiedBigInt`\n *     bigintString satisfies StringifiedBigInt; // OK\n * } else {\n *     setError(`${bigintString} does not represent a BigInt`);\n * }\n * ```\n */\nexport function isStringifiedBigInt(putativeBigInt: string): putativeBigInt is StringifiedBigInt {\n    try {\n        BigInt(putativeBigInt);\n        return true;\n    } catch {\n        return false;\n    }\n}\n\n/**\n * From time to time you might acquire a string, that you expect to parse as a `BigInt`, from an\n * untrusted network API or user input. Use this function to assert that such an arbitrary string\n * will in fact parse as a `BigInt`.\n *\n * @example\n * ```ts\n * import { assertIsStringifiedBigInt } from '@solana/rpc-types';\n *\n * // Imagine having received a value that you presume represents the supply of some token.\n * // At this point we know only that it conforms to the `string` type.\n * try {\n *     // If this type assertion function doesn't throw, then\n *     // Typescript will upcast `supplyString` to `StringifiedBigInt`.\n *     assertIsStringifiedBigInt(supplyString);\n *     // At this point, `supplyString` is a `StringifiedBigInt`.\n *     supplyString satisfies StringifiedBigInt;\n * } catch (e) {\n *     // `supplyString` turned out not to parse as a `BigInt`\n * }\n * ```\n */\nexport function assertIsStringifiedBigInt(putativeBigInt: string): asserts putativeBigInt is StringifiedBigInt {\n    try {\n        BigInt(putativeBigInt);\n    } catch {\n        throw new SolanaError(SOLANA_ERROR__MALFORMED_BIGINT_STRING, {\n            value: putativeBigInt,\n        });\n    }\n}\n\n/**\n * This helper combines _asserting_ that a string will parse as a `BigInt` with _coercing_ it to the\n * {@link StringifiedBigInt} type. It's best used with untrusted input.\n *\n * @example\n * ```ts\n * import { stringifiedBigInt } from '@solana/rpc-types';\n *\n * const supplyString = stringifiedBigInt('1000000000');\n * ```\n */\nexport function stringifiedBigInt(putativeBigInt: string): StringifiedBigInt {\n    assertIsStringifiedBigInt(putativeBigInt);\n    return putativeBigInt;\n}\n", "import { SOLANA_ERROR__MALFORMED_NUMBER_STRING, SolanaError } from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\n\n/**\n * This type represents a number which has been encoded as a string for transit over a transport\n * where loss of precision when using the native number type is a concern. The JSON-RPC is such a\n * transport.\n */\nexport type StringifiedNumber = Brand<string, 'StringifiedNumber'>;\n\n/**\n * A type guard that returns `true` if the input string parses as a `Number`, and refines its type\n * for use in your program.\n *\n * @example\n * ```ts\n * import { isStringifiedNumber } from '@solana/rpc-types';\n *\n * if (isStringifiedNumber(numericString)) {\n *     // At this point, `numericString` has been refined to a `StringifiedNumber`\n *     numericString satisfies StringifiedNumber; // OK\n * } else {\n *     setError(`${numericString} does not represent a number`);\n * }\n * ```\n */\nexport function isStringifiedNumber(putativeNumber: string): putativeNumber is StringifiedNumber {\n    return !Number.isNaN(Number(putativeNumber));\n}\n\n/**\n * From time to time you might acquire a string, that you expect to parse as a `Number`, from an\n * untrusted network API or user input. Use this function to assert that such an arbitrary string\n * will in fact parse as a `Number`.\n *\n * @example\n * ```ts\n * import { assertIsStringifiedNumber } from '@solana/rpc-types';\n *\n * // Imagine having received a value that you presume represents some decimal number.\n * // At this point we know only that it conforms to the `string` type.\n * try {\n *     // If this type assertion function doesn't throw, then\n *     // Typescript will upcast `decimalNumberString` to `StringifiedNumber`.\n *     assertIsStringifiedNumber(decimalNumberString);\n *     // At this point, `decimalNumberString` is a `StringifiedNumber`.\n *     decimalNumberString satisfies StringifiedNumber;\n * } catch (e) {\n *     // `decimalNumberString` turned out not to parse as a number.\n * }\n * ```\n */\nexport function assertIsStringifiedNumber(putativeNumber: string): asserts putativeNumber is StringifiedNumber {\n    if (Number.isNaN(Number(putativeNumber))) {\n        throw new SolanaError(SOLANA_ERROR__MALFORMED_NUMBER_STRING, {\n            value: putativeNumber,\n        });\n    }\n}\n\n/**\n * This helper combines _asserting_ that a string will parse as a `Number` with _coercing_ it to the\n * {@link StringifiedNumber} type. It's best used with untrusted input.\n *\n * @example\n * ```ts\n * import { stringifiedNumber } from '@solana/rpc-types';\n *\n * const decimalNumberString = stringifiedNumber('-42.1');\n * ```\n */\nexport function stringifiedNumber(putativeNumber: string): StringifiedNumber {\n    assertIsStringifiedNumber(putativeNumber);\n    return putativeNumber;\n}\n", "import { SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE, SolanaError } from '@solana/errors';\nimport { Brand } from '@solana/nominal-types';\n\n/**\n * This type represents a Unix timestamp in _seconds_.\n *\n * It is represented as a `bigint` in client code and an `i64` in server code.\n */\nexport type UnixTimestamp = Brand<bigint, 'UnixTimestamp'>;\n\n// Largest possible value to be represented by an i64\nconst maxI64Value = 9223372036854775807n; // 2n ** 63n - 1n\nconst minI64Value = -9223372036854775808n; // -(2n ** 63n)\n\n/**\n * This is a type guard that accepts a `bigint` as input. It will both return `true` if the integer\n * conforms to the {@link UnixTimestamp} type and will refine the type for use in your program.\n *\n * @example\n * ```ts\n * import { isUnixTimestamp } from '@solana/rpc-types';\n *\n * if (isUnixTimestamp(timestamp)) {\n *     // At this point, `timestamp` has been refined to a\n *     // `UnixTimestamp` that can be used anywhere timestamps are expected.\n *     timestamp satisfies UnixTimestamp;\n * } else {\n *     setError(`${timestamp} is not a Unix timestamp`);\n * }\n * ```\n */\n\nexport function isUnixTimestamp(putativeTimestamp: bigint): putativeTimestamp is UnixTimestamp {\n    return putativeTimestamp >= minI64Value && putativeTimestamp <= maxI64Value;\n}\n\n/**\n * Timestamp values returned from the RPC API conform to the type {@link UnixTimestamp}. You can use\n * a value of that type wherever a timestamp is expected.\n *\n * @example\n * From time to time you might acquire a number that you expect to be a timestamp, from an untrusted\n * network API or user input. To assert that such an arbitrary number is usable as a Unix timestamp,\n * use this function.\n *\n * ```ts\n * import { assertIsUnixTimestamp } from '@solana/rpc-types';\n *\n * // Imagine having received a value that you presume represents a timestamp.\n * // At this point we know only that it conforms to the `bigint` type.\n * try {\n *     // If this type assertion function doesn't throw, then\n *     // Typescript will upcast `timestamp` to `UnixTimestamp`.\n *     assertIsUnixTimestamp(timestamp);\n *     // At this point, `timestamp` is a `UnixTimestamp`.\n *     timestamp satisfies UnixTimestamp;\n * } catch (e) {\n *     // `timestamp` turned out not to be a valid Unix timestamp\n * }\n * ```\n */\nexport function assertIsUnixTimestamp(putativeTimestamp: bigint): asserts putativeTimestamp is UnixTimestamp {\n    if (putativeTimestamp < minI64Value || putativeTimestamp > maxI64Value) {\n        throw new SolanaError(SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE, {\n            value: putativeTimestamp,\n        });\n    }\n}\n\n/**\n * This helper combines _asserting_ that a `bigint` represents a Unix timestamp with _coercing_ it\n * to the {@link UnixTimestamp} type. It's best used with untrusted input.\n *\n * @example\n * ```ts\n * import { unixTimestamp } from '@solana/rpc-types';\n *\n * const timestamp = unixTimestamp(-42n); // Wednesday, December 31, 1969 3:59:18 PM GMT-08:00\n * ```\n */\nexport function unixTimestamp(putativeTimestamp: bigint): UnixTimestamp {\n    assertIsUnixTimestamp(putativeTimestamp);\n    return putativeTimestamp;\n}\n"]}