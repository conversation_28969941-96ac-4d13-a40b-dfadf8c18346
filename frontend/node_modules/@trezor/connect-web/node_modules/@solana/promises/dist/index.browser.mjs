// src/race.ts
function isObject(value) {
  return value !== null && (typeof value === "object" || typeof value === "function");
}
function addRaceContender(contender) {
  const deferreds = /* @__PURE__ */ new Set();
  const record = { deferreds, settled: false };
  Promise.resolve(contender).then(
    (value) => {
      for (const { resolve } of deferreds) {
        resolve(value);
      }
      deferreds.clear();
      record.settled = true;
    },
    (err) => {
      for (const { reject } of deferreds) {
        reject(err);
      }
      deferreds.clear();
      record.settled = true;
    }
  );
  return record;
}
var wm = /* @__PURE__ */ new WeakMap();
async function safeRace(contenders) {
  let deferred;
  const result = new Promise((resolve, reject) => {
    deferred = { reject, resolve };
    for (const contender of contenders) {
      if (!isObject(contender)) {
        Promise.resolve(contender).then(resolve, reject);
        continue;
      }
      let record = wm.get(contender);
      if (record === void 0) {
        record = addRace<PERSON>ontender(contender);
        record.deferreds.add(deferred);
        wm.set(contender, record);
      } else if (record.settled) {
        Promise.resolve(contender).then(resolve, reject);
      } else {
        record.deferreds.add(deferred);
      }
    }
  });
  return await result.finally(() => {
    for (const contender of contenders) {
      if (isObject(contender)) {
        const record = wm.get(contender);
        record.deferreds.delete(deferred);
      }
    }
  });
}

// src/abortable.ts
function getAbortablePromise(promise, abortSignal) {
  if (!abortSignal) {
    return promise;
  } else {
    return safeRace([
      // This promise only ever rejects if the signal is aborted. Otherwise it idles forever.
      // It's important that this come before the input promise; in the event of an abort, we
      // want to throw even if the input promise's result is ready
      new Promise((_, reject) => {
        if (abortSignal.aborted) {
          reject(abortSignal.reason);
        } else {
          abortSignal.addEventListener("abort", function() {
            reject(this.reason);
          });
        }
      }),
      promise
    ]);
  }
}

export { getAbortablePromise, safeRace };
//# sourceMappingURL=index.browser.mjs.map
//# sourceMappingURL=index.browser.mjs.map