'use strict';

var kit = require('@solana/kit');
var sysvars = require('@solana/sysvars');

// src/generated/accounts/mint.ts
var AccountState = /* @__PURE__ */ ((AccountState2) => {
  AccountState2[AccountState2["Uninitialized"] = 0] = "Uninitialized";
  AccountState2[AccountState2["Initialized"] = 1] = "Initialized";
  AccountState2[AccountState2["Frozen"] = 2] = "Frozen";
  return AccountState2;
})(AccountState || {});
function getAccountStateEncoder() {
  return kit.getEnumEncoder(AccountState);
}
function getAccountStateDecoder() {
  return kit.getEnumDecoder(AccountState);
}
function getAccountStateCodec() {
  return kit.combineCodec(getAccountStateEncoder(), getAccountStateDecoder());
}
var AuthorityType = /* @__PURE__ */ ((AuthorityType2) => {
  AuthorityType2[AuthorityType2["MintTokens"] = 0] = "MintTokens";
  AuthorityType2[AuthorityType2["FreezeAccount"] = 1] = "FreezeAccount";
  AuthorityType2[AuthorityType2["AccountOwner"] = 2] = "AccountOwner";
  AuthorityType2[AuthorityType2["CloseAccount"] = 3] = "CloseAccount";
  AuthorityType2[AuthorityType2["TransferFeeConfig"] = 4] = "TransferFeeConfig";
  AuthorityType2[AuthorityType2["WithheldWithdraw"] = 5] = "WithheldWithdraw";
  AuthorityType2[AuthorityType2["CloseMint"] = 6] = "CloseMint";
  AuthorityType2[AuthorityType2["InterestRate"] = 7] = "InterestRate";
  AuthorityType2[AuthorityType2["PermanentDelegate"] = 8] = "PermanentDelegate";
  AuthorityType2[AuthorityType2["ConfidentialTransferMint"] = 9] = "ConfidentialTransferMint";
  AuthorityType2[AuthorityType2["TransferHookProgramId"] = 10] = "TransferHookProgramId";
  AuthorityType2[AuthorityType2["ConfidentialTransferFeeConfig"] = 11] = "ConfidentialTransferFeeConfig";
  AuthorityType2[AuthorityType2["MetadataPointer"] = 12] = "MetadataPointer";
  AuthorityType2[AuthorityType2["GroupPointer"] = 13] = "GroupPointer";
  AuthorityType2[AuthorityType2["GroupMemberPointer"] = 14] = "GroupMemberPointer";
  AuthorityType2[AuthorityType2["ScaledUiAmount"] = 15] = "ScaledUiAmount";
  AuthorityType2[AuthorityType2["Pause"] = 16] = "Pause";
  return AuthorityType2;
})(AuthorityType || {});
function getAuthorityTypeEncoder() {
  return kit.getEnumEncoder(AuthorityType);
}
function getAuthorityTypeDecoder() {
  return kit.getEnumDecoder(AuthorityType);
}
function getAuthorityTypeCodec() {
  return kit.combineCodec(getAuthorityTypeEncoder(), getAuthorityTypeDecoder());
}
function getDecryptableBalanceEncoder() {
  return kit.fixEncoderSize(kit.getBytesEncoder(), 36);
}
function getDecryptableBalanceDecoder() {
  return kit.fixDecoderSize(kit.getBytesDecoder(), 36);
}
function getDecryptableBalanceCodec() {
  return kit.combineCodec(
    getDecryptableBalanceEncoder(),
    getDecryptableBalanceDecoder()
  );
}
function getEncryptedBalanceEncoder() {
  return kit.fixEncoderSize(kit.getBytesEncoder(), 64);
}
function getEncryptedBalanceDecoder() {
  return kit.fixDecoderSize(kit.getBytesDecoder(), 64);
}
function getEncryptedBalanceCodec() {
  return kit.combineCodec(
    getEncryptedBalanceEncoder(),
    getEncryptedBalanceDecoder()
  );
}
function getExtensionEncoder() {
  return kit.getDiscriminatedUnionEncoder(
    [
      ["Uninitialized", kit.getUnitEncoder()],
      [
        "TransferFeeConfig",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["transferFeeConfigAuthority", kit.getAddressEncoder()],
            ["withdrawWithheldAuthority", kit.getAddressEncoder()],
            ["withheldAmount", kit.getU64Encoder()],
            ["olderTransferFee", getTransferFeeEncoder()],
            ["newerTransferFee", getTransferFeeEncoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "TransferFeeAmount",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["withheldAmount", kit.getU64Encoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "MintCloseAuthority",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["closeAuthority", kit.getAddressEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "ConfidentialTransferMint",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["autoApproveNewAccounts", kit.getBooleanEncoder()],
            [
              "auditorElgamalPubkey",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "ConfidentialTransferAccount",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["approved", kit.getBooleanEncoder()],
            ["elgamalPubkey", kit.getAddressEncoder()],
            ["pendingBalanceLow", getEncryptedBalanceEncoder()],
            ["pendingBalanceHigh", getEncryptedBalanceEncoder()],
            ["availableBalance", getEncryptedBalanceEncoder()],
            ["decryptableAvailableBalance", getDecryptableBalanceEncoder()],
            ["allowConfidentialCredits", kit.getBooleanEncoder()],
            ["allowNonConfidentialCredits", kit.getBooleanEncoder()],
            ["pendingBalanceCreditCounter", kit.getU64Encoder()],
            ["maximumPendingBalanceCreditCounter", kit.getU64Encoder()],
            ["expectedPendingBalanceCreditCounter", kit.getU64Encoder()],
            ["actualPendingBalanceCreditCounter", kit.getU64Encoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "DefaultAccountState",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["state", getAccountStateEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "ImmutableOwner",
        kit.addEncoderSizePrefix(kit.getStructEncoder([]), kit.getU16Encoder())
      ],
      [
        "MemoTransfer",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["requireIncomingTransferMemos", kit.getBooleanEncoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "NonTransferable",
        kit.addEncoderSizePrefix(kit.getStructEncoder([]), kit.getU16Encoder())
      ],
      [
        "InterestBearingConfig",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["rateAuthority", kit.getAddressEncoder()],
            ["initializationTimestamp", kit.getU64Encoder()],
            ["preUpdateAverageRate", kit.getI16Encoder()],
            ["lastUpdateTimestamp", kit.getU64Encoder()],
            ["currentRate", kit.getI16Encoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "CpiGuard",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["lockCpi", kit.getBooleanEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "PermanentDelegate",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["delegate", kit.getAddressEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "NonTransferableAccount",
        kit.addEncoderSizePrefix(kit.getStructEncoder([]), kit.getU16Encoder())
      ],
      [
        "TransferHook",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["authority", kit.getAddressEncoder()],
            ["programId", kit.getAddressEncoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "TransferHookAccount",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["transferring", kit.getBooleanEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "ConfidentialTransferFee",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["elgamalPubkey", kit.getAddressEncoder()],
            ["harvestToMintEnabled", kit.getBooleanEncoder()],
            ["withheldAmount", getEncryptedBalanceEncoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "ConfidentialTransferFeeAmount",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([["withheldAmount", getEncryptedBalanceEncoder()]]),
          kit.getU16Encoder()
        )
      ],
      [
        "MetadataPointer",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "metadataAddress",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "TokenMetadata",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "updateAuthority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["mint", kit.getAddressEncoder()],
            ["name", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())],
            ["symbol", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())],
            ["uri", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())],
            [
              "additionalMetadata",
              kit.getMapEncoder(
                kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder()),
                kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())
              )
            ]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "GroupPointer",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "groupAddress",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "TokenGroup",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "updateAuthority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["mint", kit.getAddressEncoder()],
            ["size", kit.getU64Encoder()],
            ["maxSize", kit.getU64Encoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "GroupMemberPointer",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "memberAddress",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "TokenGroupMember",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["mint", kit.getAddressEncoder()],
            ["group", kit.getAddressEncoder()],
            ["memberNumber", kit.getU64Encoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      ["ConfidentialMintBurn", kit.getUnitEncoder()],
      [
        "ScaledUiAmountConfig",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            ["authority", kit.getAddressEncoder()],
            ["multiplier", kit.getF64Encoder()],
            ["newMultiplierEffectiveTimestamp", kit.getU64Encoder()],
            ["newMultiplier", kit.getF64Encoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      [
        "PausableConfig",
        kit.addEncoderSizePrefix(
          kit.getStructEncoder([
            [
              "authority",
              kit.getOptionEncoder(kit.getAddressEncoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["paused", kit.getBooleanEncoder()]
          ]),
          kit.getU16Encoder()
        )
      ],
      ["PausableAccount", kit.getUnitEncoder()]
    ],
    { size: kit.getU16Encoder() }
  );
}
function getExtensionDecoder() {
  return kit.getDiscriminatedUnionDecoder(
    [
      ["Uninitialized", kit.getUnitDecoder()],
      [
        "TransferFeeConfig",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["transferFeeConfigAuthority", kit.getAddressDecoder()],
            ["withdrawWithheldAuthority", kit.getAddressDecoder()],
            ["withheldAmount", kit.getU64Decoder()],
            ["olderTransferFee", getTransferFeeDecoder()],
            ["newerTransferFee", getTransferFeeDecoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "TransferFeeAmount",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["withheldAmount", kit.getU64Decoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "MintCloseAuthority",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["closeAuthority", kit.getAddressDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "ConfidentialTransferMint",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["autoApproveNewAccounts", kit.getBooleanDecoder()],
            [
              "auditorElgamalPubkey",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "ConfidentialTransferAccount",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["approved", kit.getBooleanDecoder()],
            ["elgamalPubkey", kit.getAddressDecoder()],
            ["pendingBalanceLow", getEncryptedBalanceDecoder()],
            ["pendingBalanceHigh", getEncryptedBalanceDecoder()],
            ["availableBalance", getEncryptedBalanceDecoder()],
            ["decryptableAvailableBalance", getDecryptableBalanceDecoder()],
            ["allowConfidentialCredits", kit.getBooleanDecoder()],
            ["allowNonConfidentialCredits", kit.getBooleanDecoder()],
            ["pendingBalanceCreditCounter", kit.getU64Decoder()],
            ["maximumPendingBalanceCreditCounter", kit.getU64Decoder()],
            ["expectedPendingBalanceCreditCounter", kit.getU64Decoder()],
            ["actualPendingBalanceCreditCounter", kit.getU64Decoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "DefaultAccountState",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["state", getAccountStateDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "ImmutableOwner",
        kit.addDecoderSizePrefix(kit.getStructDecoder([]), kit.getU16Decoder())
      ],
      [
        "MemoTransfer",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["requireIncomingTransferMemos", kit.getBooleanDecoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "NonTransferable",
        kit.addDecoderSizePrefix(kit.getStructDecoder([]), kit.getU16Decoder())
      ],
      [
        "InterestBearingConfig",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["rateAuthority", kit.getAddressDecoder()],
            ["initializationTimestamp", kit.getU64Decoder()],
            ["preUpdateAverageRate", kit.getI16Decoder()],
            ["lastUpdateTimestamp", kit.getU64Decoder()],
            ["currentRate", kit.getI16Decoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "CpiGuard",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["lockCpi", kit.getBooleanDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "PermanentDelegate",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["delegate", kit.getAddressDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "NonTransferableAccount",
        kit.addDecoderSizePrefix(kit.getStructDecoder([]), kit.getU16Decoder())
      ],
      [
        "TransferHook",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["authority", kit.getAddressDecoder()],
            ["programId", kit.getAddressDecoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "TransferHookAccount",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["transferring", kit.getBooleanDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "ConfidentialTransferFee",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["elgamalPubkey", kit.getAddressDecoder()],
            ["harvestToMintEnabled", kit.getBooleanDecoder()],
            ["withheldAmount", getEncryptedBalanceDecoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "ConfidentialTransferFeeAmount",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([["withheldAmount", getEncryptedBalanceDecoder()]]),
          kit.getU16Decoder()
        )
      ],
      [
        "MetadataPointer",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "metadataAddress",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "TokenMetadata",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "updateAuthority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["mint", kit.getAddressDecoder()],
            ["name", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())],
            ["symbol", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())],
            ["uri", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())],
            [
              "additionalMetadata",
              kit.getMapDecoder(
                kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder()),
                kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())
              )
            ]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "GroupPointer",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "groupAddress",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "TokenGroup",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "updateAuthority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["mint", kit.getAddressDecoder()],
            ["size", kit.getU64Decoder()],
            ["maxSize", kit.getU64Decoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "GroupMemberPointer",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            [
              "memberAddress",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "TokenGroupMember",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["mint", kit.getAddressDecoder()],
            ["group", kit.getAddressDecoder()],
            ["memberNumber", kit.getU64Decoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      ["ConfidentialMintBurn", kit.getUnitDecoder()],
      [
        "ScaledUiAmountConfig",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            ["authority", kit.getAddressDecoder()],
            ["multiplier", kit.getF64Decoder()],
            ["newMultiplierEffectiveTimestamp", kit.getU64Decoder()],
            ["newMultiplier", kit.getF64Decoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      [
        "PausableConfig",
        kit.addDecoderSizePrefix(
          kit.getStructDecoder([
            [
              "authority",
              kit.getOptionDecoder(kit.getAddressDecoder(), {
                prefix: null,
                noneValue: "zeroes"
              })
            ],
            ["paused", kit.getBooleanDecoder()]
          ]),
          kit.getU16Decoder()
        )
      ],
      ["PausableAccount", kit.getUnitDecoder()]
    ],
    { size: kit.getU16Decoder() }
  );
}
function getExtensionCodec() {
  return kit.combineCodec(getExtensionEncoder(), getExtensionDecoder());
}
function extension(kind, data) {
  return Array.isArray(data) ? { __kind: kind, fields: data } : { __kind: kind, ...data ?? {} };
}
function isExtension(kind, value) {
  return value.__kind === kind;
}
var ExtensionType = /* @__PURE__ */ ((ExtensionType2) => {
  ExtensionType2[ExtensionType2["Uninitialized"] = 0] = "Uninitialized";
  ExtensionType2[ExtensionType2["TransferFeeConfig"] = 1] = "TransferFeeConfig";
  ExtensionType2[ExtensionType2["TransferFeeAmount"] = 2] = "TransferFeeAmount";
  ExtensionType2[ExtensionType2["MintCloseAuthority"] = 3] = "MintCloseAuthority";
  ExtensionType2[ExtensionType2["ConfidentialTransferMint"] = 4] = "ConfidentialTransferMint";
  ExtensionType2[ExtensionType2["ConfidentialTransferAccount"] = 5] = "ConfidentialTransferAccount";
  ExtensionType2[ExtensionType2["DefaultAccountState"] = 6] = "DefaultAccountState";
  ExtensionType2[ExtensionType2["ImmutableOwner"] = 7] = "ImmutableOwner";
  ExtensionType2[ExtensionType2["MemoTransfer"] = 8] = "MemoTransfer";
  ExtensionType2[ExtensionType2["NonTransferable"] = 9] = "NonTransferable";
  ExtensionType2[ExtensionType2["InterestBearingConfig"] = 10] = "InterestBearingConfig";
  ExtensionType2[ExtensionType2["CpiGuard"] = 11] = "CpiGuard";
  ExtensionType2[ExtensionType2["PermanentDelegate"] = 12] = "PermanentDelegate";
  ExtensionType2[ExtensionType2["NonTransferableAccount"] = 13] = "NonTransferableAccount";
  ExtensionType2[ExtensionType2["TransferHook"] = 14] = "TransferHook";
  ExtensionType2[ExtensionType2["TransferHookAccount"] = 15] = "TransferHookAccount";
  ExtensionType2[ExtensionType2["ConfidentialTransferFee"] = 16] = "ConfidentialTransferFee";
  ExtensionType2[ExtensionType2["ConfidentialTransferFeeAmount"] = 17] = "ConfidentialTransferFeeAmount";
  ExtensionType2[ExtensionType2["ScaledUiAmountConfig"] = 18] = "ScaledUiAmountConfig";
  ExtensionType2[ExtensionType2["PausableConfig"] = 19] = "PausableConfig";
  ExtensionType2[ExtensionType2["PausableAccount"] = 20] = "PausableAccount";
  ExtensionType2[ExtensionType2["MetadataPointer"] = 21] = "MetadataPointer";
  ExtensionType2[ExtensionType2["TokenMetadata"] = 22] = "TokenMetadata";
  ExtensionType2[ExtensionType2["GroupPointer"] = 23] = "GroupPointer";
  ExtensionType2[ExtensionType2["TokenGroup"] = 24] = "TokenGroup";
  ExtensionType2[ExtensionType2["GroupMemberPointer"] = 25] = "GroupMemberPointer";
  ExtensionType2[ExtensionType2["TokenGroupMember"] = 26] = "TokenGroupMember";
  return ExtensionType2;
})(ExtensionType || {});
function getExtensionTypeEncoder() {
  return kit.getEnumEncoder(ExtensionType, { size: kit.getU16Encoder() });
}
function getExtensionTypeDecoder() {
  return kit.getEnumDecoder(ExtensionType, { size: kit.getU16Decoder() });
}
function getExtensionTypeCodec() {
  return kit.combineCodec(getExtensionTypeEncoder(), getExtensionTypeDecoder());
}
function getTokenMetadataFieldEncoder() {
  return kit.getDiscriminatedUnionEncoder([
    ["Name", kit.getUnitEncoder()],
    ["Symbol", kit.getUnitEncoder()],
    ["Uri", kit.getUnitEncoder()],
    [
      "Key",
      kit.getStructEncoder([
        [
          "fields",
          kit.getTupleEncoder([
            kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())
          ])
        ]
      ])
    ]
  ]);
}
function getTokenMetadataFieldDecoder() {
  return kit.getDiscriminatedUnionDecoder([
    ["Name", kit.getUnitDecoder()],
    ["Symbol", kit.getUnitDecoder()],
    ["Uri", kit.getUnitDecoder()],
    [
      "Key",
      kit.getStructDecoder([
        [
          "fields",
          kit.getTupleDecoder([
            kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())
          ])
        ]
      ])
    ]
  ]);
}
function getTokenMetadataFieldCodec() {
  return kit.combineCodec(
    getTokenMetadataFieldEncoder(),
    getTokenMetadataFieldDecoder()
  );
}
function tokenMetadataField(kind, data) {
  return Array.isArray(data) ? { __kind: kind, fields: data } : { __kind: kind, ...data ?? {} };
}
function isTokenMetadataField(kind, value) {
  return value.__kind === kind;
}
function getTransferFeeEncoder() {
  return kit.getStructEncoder([
    ["epoch", kit.getU64Encoder()],
    ["maximumFee", kit.getU64Encoder()],
    ["transferFeeBasisPoints", kit.getU16Encoder()]
  ]);
}
function getTransferFeeDecoder() {
  return kit.getStructDecoder([
    ["epoch", kit.getU64Decoder()],
    ["maximumFee", kit.getU64Decoder()],
    ["transferFeeBasisPoints", kit.getU16Decoder()]
  ]);
}
function getTransferFeeCodec() {
  return kit.combineCodec(getTransferFeeEncoder(), getTransferFeeDecoder());
}

// src/generated/accounts/mint.ts
function getMintEncoder() {
  return kit.getStructEncoder([
    [
      "mintAuthority",
      kit.getOptionEncoder(kit.getAddressEncoder(), {
        prefix: kit.getU32Encoder(),
        noneValue: "zeroes"
      })
    ],
    ["supply", kit.getU64Encoder()],
    ["decimals", kit.getU8Encoder()],
    ["isInitialized", kit.getBooleanEncoder()],
    [
      "freezeAuthority",
      kit.getOptionEncoder(kit.getAddressEncoder(), {
        prefix: kit.getU32Encoder(),
        noneValue: "zeroes"
      })
    ],
    [
      "extensions",
      kit.getOptionEncoder(
        kit.getHiddenPrefixEncoder(
          kit.getArrayEncoder(getExtensionEncoder(), { size: "remainder" }),
          [kit.getConstantEncoder(kit.padLeftEncoder(kit.getU8Encoder(), 83).encode(1))]
        ),
        { prefix: null }
      )
    ]
  ]);
}
function getMintDecoder() {
  return kit.getStructDecoder([
    [
      "mintAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: kit.getU32Decoder(),
        noneValue: "zeroes"
      })
    ],
    ["supply", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()],
    ["isInitialized", kit.getBooleanDecoder()],
    [
      "freezeAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: kit.getU32Decoder(),
        noneValue: "zeroes"
      })
    ],
    [
      "extensions",
      kit.getOptionDecoder(
        kit.getHiddenPrefixDecoder(
          kit.getArrayDecoder(getExtensionDecoder(), { size: "remainder" }),
          [kit.getConstantDecoder(kit.padLeftEncoder(kit.getU8Encoder(), 83).encode(1))]
        ),
        { prefix: null }
      )
    ]
  ]);
}
function getMintCodec() {
  return kit.combineCodec(getMintEncoder(), getMintDecoder());
}
function decodeMint(encodedAccount) {
  return kit.decodeAccount(
    encodedAccount,
    getMintDecoder()
  );
}
async function fetchMint(rpc, address, config) {
  const maybeAccount = await fetchMaybeMint(rpc, address, config);
  kit.assertAccountExists(maybeAccount);
  return maybeAccount;
}
async function fetchMaybeMint(rpc, address, config) {
  const maybeAccount = await kit.fetchEncodedAccount(rpc, address, config);
  return decodeMint(maybeAccount);
}
async function fetchAllMint(rpc, addresses, config) {
  const maybeAccounts = await fetchAllMaybeMint(rpc, addresses, config);
  kit.assertAccountsExist(maybeAccounts);
  return maybeAccounts;
}
async function fetchAllMaybeMint(rpc, addresses, config) {
  const maybeAccounts = await kit.fetchEncodedAccounts(rpc, addresses, config);
  return maybeAccounts.map((maybeAccount) => decodeMint(maybeAccount));
}
function getMultisigEncoder() {
  return kit.getStructEncoder([
    ["m", kit.getU8Encoder()],
    ["n", kit.getU8Encoder()],
    ["isInitialized", kit.getBooleanEncoder()],
    ["signers", kit.getArrayEncoder(kit.getAddressEncoder(), { size: 11 })]
  ]);
}
function getMultisigDecoder() {
  return kit.getStructDecoder([
    ["m", kit.getU8Decoder()],
    ["n", kit.getU8Decoder()],
    ["isInitialized", kit.getBooleanDecoder()],
    ["signers", kit.getArrayDecoder(kit.getAddressDecoder(), { size: 11 })]
  ]);
}
function getMultisigCodec() {
  return kit.combineCodec(getMultisigEncoder(), getMultisigDecoder());
}
function decodeMultisig(encodedAccount) {
  return kit.decodeAccount(
    encodedAccount,
    getMultisigDecoder()
  );
}
async function fetchMultisig(rpc, address, config) {
  const maybeAccount = await fetchMaybeMultisig(rpc, address, config);
  kit.assertAccountExists(maybeAccount);
  return maybeAccount;
}
async function fetchMaybeMultisig(rpc, address, config) {
  const maybeAccount = await kit.fetchEncodedAccount(rpc, address, config);
  return decodeMultisig(maybeAccount);
}
async function fetchAllMultisig(rpc, addresses, config) {
  const maybeAccounts = await fetchAllMaybeMultisig(rpc, addresses, config);
  kit.assertAccountsExist(maybeAccounts);
  return maybeAccounts;
}
async function fetchAllMaybeMultisig(rpc, addresses, config) {
  const maybeAccounts = await kit.fetchEncodedAccounts(rpc, addresses, config);
  return maybeAccounts.map((maybeAccount) => decodeMultisig(maybeAccount));
}
function getMultisigSize() {
  return 355;
}
function getTokenEncoder() {
  return kit.getStructEncoder([
    ["mint", kit.getAddressEncoder()],
    ["owner", kit.getAddressEncoder()],
    ["amount", kit.getU64Encoder()],
    [
      "delegate",
      kit.getOptionEncoder(kit.getAddressEncoder(), {
        prefix: kit.getU32Encoder(),
        noneValue: "zeroes"
      })
    ],
    ["state", getAccountStateEncoder()],
    [
      "isNative",
      kit.getOptionEncoder(kit.getU64Encoder(), {
        prefix: kit.getU32Encoder(),
        noneValue: "zeroes"
      })
    ],
    ["delegatedAmount", kit.getU64Encoder()],
    [
      "closeAuthority",
      kit.getOptionEncoder(kit.getAddressEncoder(), {
        prefix: kit.getU32Encoder(),
        noneValue: "zeroes"
      })
    ],
    [
      "extensions",
      kit.getOptionEncoder(
        kit.getHiddenPrefixEncoder(
          kit.getArrayEncoder(getExtensionEncoder(), { size: "remainder" }),
          [kit.getConstantEncoder(kit.getU8Encoder().encode(2))]
        ),
        { prefix: null }
      )
    ]
  ]);
}
function getTokenDecoder() {
  return kit.getStructDecoder([
    ["mint", kit.getAddressDecoder()],
    ["owner", kit.getAddressDecoder()],
    ["amount", kit.getU64Decoder()],
    [
      "delegate",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: kit.getU32Decoder(),
        noneValue: "zeroes"
      })
    ],
    ["state", getAccountStateDecoder()],
    [
      "isNative",
      kit.getOptionDecoder(kit.getU64Decoder(), {
        prefix: kit.getU32Decoder(),
        noneValue: "zeroes"
      })
    ],
    ["delegatedAmount", kit.getU64Decoder()],
    [
      "closeAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: kit.getU32Decoder(),
        noneValue: "zeroes"
      })
    ],
    [
      "extensions",
      kit.getOptionDecoder(
        kit.getHiddenPrefixDecoder(
          kit.getArrayDecoder(getExtensionDecoder(), { size: "remainder" }),
          [kit.getConstantDecoder(kit.getU8Encoder().encode(2))]
        ),
        { prefix: null }
      )
    ]
  ]);
}
function getTokenCodec() {
  return kit.combineCodec(getTokenEncoder(), getTokenDecoder());
}
function decodeToken(encodedAccount) {
  return kit.decodeAccount(
    encodedAccount,
    getTokenDecoder()
  );
}
async function fetchToken(rpc, address, config) {
  const maybeAccount = await fetchMaybeToken(rpc, address, config);
  kit.assertAccountExists(maybeAccount);
  return maybeAccount;
}
async function fetchMaybeToken(rpc, address, config) {
  const maybeAccount = await kit.fetchEncodedAccount(rpc, address, config);
  return decodeToken(maybeAccount);
}
async function fetchAllToken(rpc, addresses, config) {
  const maybeAccounts = await fetchAllMaybeToken(rpc, addresses, config);
  kit.assertAccountsExist(maybeAccounts);
  return maybeAccounts;
}
async function fetchAllMaybeToken(rpc, addresses, config) {
  const maybeAccounts = await kit.fetchEncodedAccounts(rpc, addresses, config);
  return maybeAccounts.map((maybeAccount) => decodeToken(maybeAccount));
}
var ASSOCIATED_TOKEN_PROGRAM_ADDRESS = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL";
var AssociatedTokenInstruction = /* @__PURE__ */ ((AssociatedTokenInstruction2) => {
  AssociatedTokenInstruction2[AssociatedTokenInstruction2["CreateAssociatedToken"] = 0] = "CreateAssociatedToken";
  AssociatedTokenInstruction2[AssociatedTokenInstruction2["CreateAssociatedTokenIdempotent"] = 1] = "CreateAssociatedTokenIdempotent";
  AssociatedTokenInstruction2[AssociatedTokenInstruction2["RecoverNestedAssociatedToken"] = 2] = "RecoverNestedAssociatedToken";
  return AssociatedTokenInstruction2;
})(AssociatedTokenInstruction || {});
function identifyAssociatedTokenInstruction(instruction) {
  const data = "data" in instruction ? instruction.data : instruction;
  if (kit.containsBytes(data, kit.getU8Encoder().encode(0), 0)) {
    return 0 /* CreateAssociatedToken */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(1), 0)) {
    return 1 /* CreateAssociatedTokenIdempotent */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(2), 0)) {
    return 2 /* RecoverNestedAssociatedToken */;
  }
  throw new Error(
    "The provided instruction could not be identified as a associatedToken instruction."
  );
}
var TOKEN_2022_PROGRAM_ADDRESS = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
var Token2022Account = /* @__PURE__ */ ((Token2022Account2) => {
  Token2022Account2[Token2022Account2["Mint"] = 0] = "Mint";
  Token2022Account2[Token2022Account2["Token"] = 1] = "Token";
  Token2022Account2[Token2022Account2["Multisig"] = 2] = "Multisig";
  return Token2022Account2;
})(Token2022Account || {});
function identifyToken2022Account(account) {
  const data = "data" in account ? account.data : account;
  if (data.length === 82) {
    return 0 /* Mint */;
  }
  if (data.length === 165) {
    return 1 /* Token */;
  }
  if (data.length === 355) {
    return 2 /* Multisig */;
  }
  throw new Error(
    "The provided account could not be identified as a token-2022 account."
  );
}
var Token2022Instruction = /* @__PURE__ */ ((Token2022Instruction2) => {
  Token2022Instruction2[Token2022Instruction2["InitializeMint"] = 0] = "InitializeMint";
  Token2022Instruction2[Token2022Instruction2["InitializeAccount"] = 1] = "InitializeAccount";
  Token2022Instruction2[Token2022Instruction2["InitializeMultisig"] = 2] = "InitializeMultisig";
  Token2022Instruction2[Token2022Instruction2["Transfer"] = 3] = "Transfer";
  Token2022Instruction2[Token2022Instruction2["Approve"] = 4] = "Approve";
  Token2022Instruction2[Token2022Instruction2["Revoke"] = 5] = "Revoke";
  Token2022Instruction2[Token2022Instruction2["SetAuthority"] = 6] = "SetAuthority";
  Token2022Instruction2[Token2022Instruction2["MintTo"] = 7] = "MintTo";
  Token2022Instruction2[Token2022Instruction2["Burn"] = 8] = "Burn";
  Token2022Instruction2[Token2022Instruction2["CloseAccount"] = 9] = "CloseAccount";
  Token2022Instruction2[Token2022Instruction2["FreezeAccount"] = 10] = "FreezeAccount";
  Token2022Instruction2[Token2022Instruction2["ThawAccount"] = 11] = "ThawAccount";
  Token2022Instruction2[Token2022Instruction2["TransferChecked"] = 12] = "TransferChecked";
  Token2022Instruction2[Token2022Instruction2["ApproveChecked"] = 13] = "ApproveChecked";
  Token2022Instruction2[Token2022Instruction2["MintToChecked"] = 14] = "MintToChecked";
  Token2022Instruction2[Token2022Instruction2["BurnChecked"] = 15] = "BurnChecked";
  Token2022Instruction2[Token2022Instruction2["InitializeAccount2"] = 16] = "InitializeAccount2";
  Token2022Instruction2[Token2022Instruction2["SyncNative"] = 17] = "SyncNative";
  Token2022Instruction2[Token2022Instruction2["InitializeAccount3"] = 18] = "InitializeAccount3";
  Token2022Instruction2[Token2022Instruction2["InitializeMultisig2"] = 19] = "InitializeMultisig2";
  Token2022Instruction2[Token2022Instruction2["InitializeMint2"] = 20] = "InitializeMint2";
  Token2022Instruction2[Token2022Instruction2["GetAccountDataSize"] = 21] = "GetAccountDataSize";
  Token2022Instruction2[Token2022Instruction2["InitializeImmutableOwner"] = 22] = "InitializeImmutableOwner";
  Token2022Instruction2[Token2022Instruction2["AmountToUiAmount"] = 23] = "AmountToUiAmount";
  Token2022Instruction2[Token2022Instruction2["UiAmountToAmount"] = 24] = "UiAmountToAmount";
  Token2022Instruction2[Token2022Instruction2["InitializeMintCloseAuthority"] = 25] = "InitializeMintCloseAuthority";
  Token2022Instruction2[Token2022Instruction2["InitializeTransferFeeConfig"] = 26] = "InitializeTransferFeeConfig";
  Token2022Instruction2[Token2022Instruction2["TransferCheckedWithFee"] = 27] = "TransferCheckedWithFee";
  Token2022Instruction2[Token2022Instruction2["WithdrawWithheldTokensFromMint"] = 28] = "WithdrawWithheldTokensFromMint";
  Token2022Instruction2[Token2022Instruction2["WithdrawWithheldTokensFromAccounts"] = 29] = "WithdrawWithheldTokensFromAccounts";
  Token2022Instruction2[Token2022Instruction2["HarvestWithheldTokensToMint"] = 30] = "HarvestWithheldTokensToMint";
  Token2022Instruction2[Token2022Instruction2["SetTransferFee"] = 31] = "SetTransferFee";
  Token2022Instruction2[Token2022Instruction2["InitializeConfidentialTransferMint"] = 32] = "InitializeConfidentialTransferMint";
  Token2022Instruction2[Token2022Instruction2["UpdateConfidentialTransferMint"] = 33] = "UpdateConfidentialTransferMint";
  Token2022Instruction2[Token2022Instruction2["ConfigureConfidentialTransferAccount"] = 34] = "ConfigureConfidentialTransferAccount";
  Token2022Instruction2[Token2022Instruction2["ApproveConfidentialTransferAccount"] = 35] = "ApproveConfidentialTransferAccount";
  Token2022Instruction2[Token2022Instruction2["EmptyConfidentialTransferAccount"] = 36] = "EmptyConfidentialTransferAccount";
  Token2022Instruction2[Token2022Instruction2["ConfidentialDeposit"] = 37] = "ConfidentialDeposit";
  Token2022Instruction2[Token2022Instruction2["ConfidentialWithdraw"] = 38] = "ConfidentialWithdraw";
  Token2022Instruction2[Token2022Instruction2["ConfidentialTransfer"] = 39] = "ConfidentialTransfer";
  Token2022Instruction2[Token2022Instruction2["ApplyConfidentialPendingBalance"] = 40] = "ApplyConfidentialPendingBalance";
  Token2022Instruction2[Token2022Instruction2["EnableConfidentialCredits"] = 41] = "EnableConfidentialCredits";
  Token2022Instruction2[Token2022Instruction2["DisableConfidentialCredits"] = 42] = "DisableConfidentialCredits";
  Token2022Instruction2[Token2022Instruction2["EnableNonConfidentialCredits"] = 43] = "EnableNonConfidentialCredits";
  Token2022Instruction2[Token2022Instruction2["DisableNonConfidentialCredits"] = 44] = "DisableNonConfidentialCredits";
  Token2022Instruction2[Token2022Instruction2["ConfidentialTransferWithFee"] = 45] = "ConfidentialTransferWithFee";
  Token2022Instruction2[Token2022Instruction2["InitializeDefaultAccountState"] = 46] = "InitializeDefaultAccountState";
  Token2022Instruction2[Token2022Instruction2["UpdateDefaultAccountState"] = 47] = "UpdateDefaultAccountState";
  Token2022Instruction2[Token2022Instruction2["Reallocate"] = 48] = "Reallocate";
  Token2022Instruction2[Token2022Instruction2["EnableMemoTransfers"] = 49] = "EnableMemoTransfers";
  Token2022Instruction2[Token2022Instruction2["DisableMemoTransfers"] = 50] = "DisableMemoTransfers";
  Token2022Instruction2[Token2022Instruction2["CreateNativeMint"] = 51] = "CreateNativeMint";
  Token2022Instruction2[Token2022Instruction2["InitializeNonTransferableMint"] = 52] = "InitializeNonTransferableMint";
  Token2022Instruction2[Token2022Instruction2["InitializeInterestBearingMint"] = 53] = "InitializeInterestBearingMint";
  Token2022Instruction2[Token2022Instruction2["UpdateRateInterestBearingMint"] = 54] = "UpdateRateInterestBearingMint";
  Token2022Instruction2[Token2022Instruction2["EnableCpiGuard"] = 55] = "EnableCpiGuard";
  Token2022Instruction2[Token2022Instruction2["DisableCpiGuard"] = 56] = "DisableCpiGuard";
  Token2022Instruction2[Token2022Instruction2["InitializePermanentDelegate"] = 57] = "InitializePermanentDelegate";
  Token2022Instruction2[Token2022Instruction2["InitializeTransferHook"] = 58] = "InitializeTransferHook";
  Token2022Instruction2[Token2022Instruction2["UpdateTransferHook"] = 59] = "UpdateTransferHook";
  Token2022Instruction2[Token2022Instruction2["InitializeConfidentialTransferFee"] = 60] = "InitializeConfidentialTransferFee";
  Token2022Instruction2[Token2022Instruction2["WithdrawWithheldTokensFromMintForConfidentialTransferFee"] = 61] = "WithdrawWithheldTokensFromMintForConfidentialTransferFee";
  Token2022Instruction2[Token2022Instruction2["WithdrawWithheldTokensFromAccountsForConfidentialTransferFee"] = 62] = "WithdrawWithheldTokensFromAccountsForConfidentialTransferFee";
  Token2022Instruction2[Token2022Instruction2["HarvestWithheldTokensToMintForConfidentialTransferFee"] = 63] = "HarvestWithheldTokensToMintForConfidentialTransferFee";
  Token2022Instruction2[Token2022Instruction2["EnableHarvestToMint"] = 64] = "EnableHarvestToMint";
  Token2022Instruction2[Token2022Instruction2["DisableHarvestToMint"] = 65] = "DisableHarvestToMint";
  Token2022Instruction2[Token2022Instruction2["WithdrawExcessLamports"] = 66] = "WithdrawExcessLamports";
  Token2022Instruction2[Token2022Instruction2["InitializeMetadataPointer"] = 67] = "InitializeMetadataPointer";
  Token2022Instruction2[Token2022Instruction2["UpdateMetadataPointer"] = 68] = "UpdateMetadataPointer";
  Token2022Instruction2[Token2022Instruction2["InitializeGroupPointer"] = 69] = "InitializeGroupPointer";
  Token2022Instruction2[Token2022Instruction2["UpdateGroupPointer"] = 70] = "UpdateGroupPointer";
  Token2022Instruction2[Token2022Instruction2["InitializeGroupMemberPointer"] = 71] = "InitializeGroupMemberPointer";
  Token2022Instruction2[Token2022Instruction2["UpdateGroupMemberPointer"] = 72] = "UpdateGroupMemberPointer";
  Token2022Instruction2[Token2022Instruction2["InitializeScaledUiAmountMint"] = 73] = "InitializeScaledUiAmountMint";
  Token2022Instruction2[Token2022Instruction2["UpdateMultiplierScaledUiMint"] = 74] = "UpdateMultiplierScaledUiMint";
  Token2022Instruction2[Token2022Instruction2["InitializePausableConfig"] = 75] = "InitializePausableConfig";
  Token2022Instruction2[Token2022Instruction2["Pause"] = 76] = "Pause";
  Token2022Instruction2[Token2022Instruction2["Resume"] = 77] = "Resume";
  Token2022Instruction2[Token2022Instruction2["InitializeTokenMetadata"] = 78] = "InitializeTokenMetadata";
  Token2022Instruction2[Token2022Instruction2["UpdateTokenMetadataField"] = 79] = "UpdateTokenMetadataField";
  Token2022Instruction2[Token2022Instruction2["RemoveTokenMetadataKey"] = 80] = "RemoveTokenMetadataKey";
  Token2022Instruction2[Token2022Instruction2["UpdateTokenMetadataUpdateAuthority"] = 81] = "UpdateTokenMetadataUpdateAuthority";
  Token2022Instruction2[Token2022Instruction2["EmitTokenMetadata"] = 82] = "EmitTokenMetadata";
  Token2022Instruction2[Token2022Instruction2["InitializeTokenGroup"] = 83] = "InitializeTokenGroup";
  Token2022Instruction2[Token2022Instruction2["UpdateTokenGroupMaxSize"] = 84] = "UpdateTokenGroupMaxSize";
  Token2022Instruction2[Token2022Instruction2["UpdateTokenGroupUpdateAuthority"] = 85] = "UpdateTokenGroupUpdateAuthority";
  Token2022Instruction2[Token2022Instruction2["InitializeTokenGroupMember"] = 86] = "InitializeTokenGroupMember";
  return Token2022Instruction2;
})(Token2022Instruction || {});
function identifyToken2022Instruction(instruction) {
  const data = "data" in instruction ? instruction.data : instruction;
  if (kit.containsBytes(data, kit.getU8Encoder().encode(0), 0)) {
    return 0 /* InitializeMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(1), 0)) {
    return 1 /* InitializeAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(2), 0)) {
    return 2 /* InitializeMultisig */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(3), 0)) {
    return 3 /* Transfer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(4), 0)) {
    return 4 /* Approve */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(5), 0)) {
    return 5 /* Revoke */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(6), 0)) {
    return 6 /* SetAuthority */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(7), 0)) {
    return 7 /* MintTo */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(8), 0)) {
    return 8 /* Burn */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(9), 0)) {
    return 9 /* CloseAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(10), 0)) {
    return 10 /* FreezeAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(11), 0)) {
    return 11 /* ThawAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(12), 0)) {
    return 12 /* TransferChecked */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(13), 0)) {
    return 13 /* ApproveChecked */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(14), 0)) {
    return 14 /* MintToChecked */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(15), 0)) {
    return 15 /* BurnChecked */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(16), 0)) {
    return 16 /* InitializeAccount2 */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(17), 0)) {
    return 17 /* SyncNative */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(18), 0)) {
    return 18 /* InitializeAccount3 */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(19), 0)) {
    return 19 /* InitializeMultisig2 */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(20), 0)) {
    return 20 /* InitializeMint2 */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(21), 0)) {
    return 21 /* GetAccountDataSize */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(22), 0)) {
    return 22 /* InitializeImmutableOwner */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(23), 0)) {
    return 23 /* AmountToUiAmount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(24), 0)) {
    return 24 /* UiAmountToAmount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(25), 0)) {
    return 25 /* InitializeMintCloseAuthority */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 26 /* InitializeTransferFeeConfig */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 27 /* TransferCheckedWithFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(2), 1)) {
    return 28 /* WithdrawWithheldTokensFromMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(3), 1)) {
    return 29 /* WithdrawWithheldTokensFromAccounts */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(4), 1)) {
    return 30 /* HarvestWithheldTokensToMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(26), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(5), 1)) {
    return 31 /* SetTransferFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 32 /* InitializeConfidentialTransferMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 33 /* UpdateConfidentialTransferMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(2), 1)) {
    return 34 /* ConfigureConfidentialTransferAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(3), 1)) {
    return 35 /* ApproveConfidentialTransferAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(4), 1)) {
    return 36 /* EmptyConfidentialTransferAccount */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(5), 1)) {
    return 37 /* ConfidentialDeposit */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(6), 1)) {
    return 38 /* ConfidentialWithdraw */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(7), 1)) {
    return 39 /* ConfidentialTransfer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(8), 1)) {
    return 40 /* ApplyConfidentialPendingBalance */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(9), 1)) {
    return 41 /* EnableConfidentialCredits */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(10), 1)) {
    return 42 /* DisableConfidentialCredits */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(11), 1)) {
    return 43 /* EnableNonConfidentialCredits */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(12), 1)) {
    return 44 /* DisableNonConfidentialCredits */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(27), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(13), 1)) {
    return 45 /* ConfidentialTransferWithFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(28), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 46 /* InitializeDefaultAccountState */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(28), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 47 /* UpdateDefaultAccountState */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(29), 0)) {
    return 48 /* Reallocate */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(30), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 49 /* EnableMemoTransfers */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(30), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 50 /* DisableMemoTransfers */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(31), 0)) {
    return 51 /* CreateNativeMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(32), 0)) {
    return 52 /* InitializeNonTransferableMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(33), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 53 /* InitializeInterestBearingMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(33), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 54 /* UpdateRateInterestBearingMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(34), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 55 /* EnableCpiGuard */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(34), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 56 /* DisableCpiGuard */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(35), 0)) {
    return 57 /* InitializePermanentDelegate */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(36), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 58 /* InitializeTransferHook */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(36), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 59 /* UpdateTransferHook */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 60 /* InitializeConfidentialTransferFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 61 /* WithdrawWithheldTokensFromMintForConfidentialTransferFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(2), 1)) {
    return 62 /* WithdrawWithheldTokensFromAccountsForConfidentialTransferFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(3), 1)) {
    return 63 /* HarvestWithheldTokensToMintForConfidentialTransferFee */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(4), 1)) {
    return 64 /* EnableHarvestToMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(37), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(5), 1)) {
    return 65 /* DisableHarvestToMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(38), 0)) {
    return 66 /* WithdrawExcessLamports */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(39), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 67 /* InitializeMetadataPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(39), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 68 /* UpdateMetadataPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(40), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 69 /* InitializeGroupPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(40), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 70 /* UpdateGroupPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(41), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 71 /* InitializeGroupMemberPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(41), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 72 /* UpdateGroupMemberPointer */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(43), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 73 /* InitializeScaledUiAmountMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(43), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 74 /* UpdateMultiplierScaledUiMint */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(44), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(0), 1)) {
    return 75 /* InitializePausableConfig */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(44), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(1), 1)) {
    return 76 /* Pause */;
  }
  if (kit.containsBytes(data, kit.getU8Encoder().encode(44), 0) && kit.containsBytes(data, kit.getU8Encoder().encode(2), 1)) {
    return 77 /* Resume */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([210, 225, 30, 162, 88, 184, 77, 141]),
    0
  )) {
    return 78 /* InitializeTokenMetadata */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([221, 233, 49, 45, 181, 202, 220, 200]),
    0
  )) {
    return 79 /* UpdateTokenMetadataField */;
  }
  if (kit.containsBytes(data, new Uint8Array([234, 18, 32, 56, 89, 141, 37, 181]), 0)) {
    return 80 /* RemoveTokenMetadataKey */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([215, 228, 166, 228, 84, 100, 86, 123]),
    0
  )) {
    return 81 /* UpdateTokenMetadataUpdateAuthority */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([250, 166, 180, 250, 13, 12, 184, 70]),
    0
  )) {
    return 82 /* EmitTokenMetadata */;
  }
  if (kit.containsBytes(data, new Uint8Array([121, 113, 108, 39, 54, 51, 0, 4]), 0)) {
    return 83 /* InitializeTokenGroup */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([108, 37, 171, 143, 248, 30, 18, 110]),
    0
  )) {
    return 84 /* UpdateTokenGroupMaxSize */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([161, 105, 88, 1, 237, 221, 216, 203]),
    0
  )) {
    return 85 /* UpdateTokenGroupUpdateAuthority */;
  }
  if (kit.containsBytes(
    data,
    new Uint8Array([152, 32, 222, 176, 223, 237, 116, 134]),
    0
  )) {
    return 86 /* InitializeTokenGroupMember */;
  }
  throw new Error(
    "The provided instruction could not be identified as a token-2022 instruction."
  );
}

// src/generated/errors/associatedToken.ts
var ASSOCIATED_TOKEN_ERROR__INVALID_OWNER = 0;
var associatedTokenErrorMessages;
if (process.env.NODE_ENV !== "production") {
  associatedTokenErrorMessages = {
    [ASSOCIATED_TOKEN_ERROR__INVALID_OWNER]: `Associated token account owner does not match address derivation`
  };
}
function getAssociatedTokenErrorMessage(code) {
  if (process.env.NODE_ENV !== "production") {
    return associatedTokenErrorMessages[code];
  }
  return "Error message not available in production bundles.";
}
function isAssociatedTokenError(error, transactionMessage, code) {
  return kit.isProgramError(
    error,
    transactionMessage,
    ASSOCIATED_TOKEN_PROGRAM_ADDRESS,
    code
  );
}
var TOKEN_2022_ERROR__NOT_RENT_EXEMPT = 0;
var TOKEN_2022_ERROR__INSUFFICIENT_FUNDS = 1;
var TOKEN_2022_ERROR__INVALID_MINT = 2;
var TOKEN_2022_ERROR__MINT_MISMATCH = 3;
var TOKEN_2022_ERROR__OWNER_MISMATCH = 4;
var TOKEN_2022_ERROR__FIXED_SUPPLY = 5;
var TOKEN_2022_ERROR__ALREADY_IN_USE = 6;
var TOKEN_2022_ERROR__INVALID_NUMBER_OF_PROVIDED_SIGNERS = 7;
var TOKEN_2022_ERROR__INVALID_NUMBER_OF_REQUIRED_SIGNERS = 8;
var TOKEN_2022_ERROR__UNINITIALIZED_STATE = 9;
var TOKEN_2022_ERROR__NATIVE_NOT_SUPPORTED = 10;
var TOKEN_2022_ERROR__NON_NATIVE_HAS_BALANCE = 11;
var TOKEN_2022_ERROR__INVALID_INSTRUCTION = 12;
var TOKEN_2022_ERROR__INVALID_STATE = 13;
var TOKEN_2022_ERROR__OVERFLOW = 14;
var TOKEN_2022_ERROR__AUTHORITY_TYPE_NOT_SUPPORTED = 15;
var TOKEN_2022_ERROR__MINT_CANNOT_FREEZE = 16;
var TOKEN_2022_ERROR__ACCOUNT_FROZEN = 17;
var TOKEN_2022_ERROR__MINT_DECIMALS_MISMATCH = 18;
var TOKEN_2022_ERROR__NON_NATIVE_NOT_SUPPORTED = 19;
var token2022ErrorMessages;
if (process.env.NODE_ENV !== "production") {
  token2022ErrorMessages = {
    [TOKEN_2022_ERROR__ACCOUNT_FROZEN]: `Account is frozen`,
    [TOKEN_2022_ERROR__ALREADY_IN_USE]: `Already in use`,
    [TOKEN_2022_ERROR__AUTHORITY_TYPE_NOT_SUPPORTED]: `Account does not support specified authority type`,
    [TOKEN_2022_ERROR__FIXED_SUPPLY]: `Fixed supply`,
    [TOKEN_2022_ERROR__INSUFFICIENT_FUNDS]: `Insufficient funds`,
    [TOKEN_2022_ERROR__INVALID_INSTRUCTION]: `Invalid instruction`,
    [TOKEN_2022_ERROR__INVALID_MINT]: `Invalid Mint`,
    [TOKEN_2022_ERROR__INVALID_NUMBER_OF_PROVIDED_SIGNERS]: `Invalid number of provided signers`,
    [TOKEN_2022_ERROR__INVALID_NUMBER_OF_REQUIRED_SIGNERS]: `Invalid number of required signers`,
    [TOKEN_2022_ERROR__INVALID_STATE]: `State is invalid for requested operation`,
    [TOKEN_2022_ERROR__MINT_CANNOT_FREEZE]: `This token mint cannot freeze accounts`,
    [TOKEN_2022_ERROR__MINT_DECIMALS_MISMATCH]: `The provided decimals value different from the Mint decimals`,
    [TOKEN_2022_ERROR__MINT_MISMATCH]: `Account not associated with this Mint`,
    [TOKEN_2022_ERROR__NATIVE_NOT_SUPPORTED]: `Instruction does not support native tokens`,
    [TOKEN_2022_ERROR__NON_NATIVE_HAS_BALANCE]: `Non-native account can only be closed if its balance is zero`,
    [TOKEN_2022_ERROR__NON_NATIVE_NOT_SUPPORTED]: `Instruction does not support non-native tokens`,
    [TOKEN_2022_ERROR__NOT_RENT_EXEMPT]: `Lamport balance below rent-exempt threshold`,
    [TOKEN_2022_ERROR__OVERFLOW]: `Operation overflowed`,
    [TOKEN_2022_ERROR__OWNER_MISMATCH]: `Owner does not match`,
    [TOKEN_2022_ERROR__UNINITIALIZED_STATE]: `State is unititialized`
  };
}
function getToken2022ErrorMessage(code) {
  if (process.env.NODE_ENV !== "production") {
    return token2022ErrorMessages[code];
  }
  return "Error message not available in production bundles.";
}
function isToken2022Error(error, transactionMessage, code) {
  return kit.isProgramError(
    error,
    transactionMessage,
    TOKEN_2022_PROGRAM_ADDRESS,
    code
  );
}
function expectAddress(value) {
  if (!value) {
    throw new Error("Expected a Address.");
  }
  if (typeof value === "object" && "address" in value) {
    return value.address;
  }
  if (Array.isArray(value)) {
    return value[0];
  }
  return value;
}
function getAccountMetaFactory(programAddress, optionalAccountStrategy) {
  return (account) => {
    if (!account.value) {
      return Object.freeze({
        address: programAddress,
        role: kit.AccountRole.READONLY
      });
    }
    const writableRole = account.isWritable ? kit.AccountRole.WRITABLE : kit.AccountRole.READONLY;
    return Object.freeze({
      address: expectAddress(account.value),
      role: isTransactionSigner(account.value) ? kit.upgradeRoleToSigner(writableRole) : writableRole,
      ...isTransactionSigner(account.value) ? { signer: account.value } : {}
    });
  };
}
function isTransactionSigner(value) {
  return !!value && typeof value === "object" && "address" in value && kit.isTransactionSigner(value);
}

// src/generated/instructions/amountToUiAmount.ts
var AMOUNT_TO_UI_AMOUNT_DISCRIMINATOR = 23;
function getAmountToUiAmountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(AMOUNT_TO_UI_AMOUNT_DISCRIMINATOR);
}
function getAmountToUiAmountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()]
    ]),
    (value) => ({ ...value, discriminator: AMOUNT_TO_UI_AMOUNT_DISCRIMINATOR })
  );
}
function getAmountToUiAmountInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()]
  ]);
}
function getAmountToUiAmountInstructionDataCodec() {
  return kit.combineCodec(
    getAmountToUiAmountInstructionDataEncoder(),
    getAmountToUiAmountInstructionDataDecoder()
  );
}
function getAmountToUiAmountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getAmountToUiAmountInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseAmountToUiAmountInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getAmountToUiAmountInstructionDataDecoder().decode(instruction.data)
  };
}
var APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR = 27;
function getApplyConfidentialPendingBalanceDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR
  );
}
var APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 8;
function getApplyConfidentialPendingBalanceConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getApplyConfidentialPendingBalanceInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["expectedPendingBalanceCreditCounter", kit.getU64Encoder()],
      ["newDecryptableAvailableBalance", getDecryptableBalanceEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR,
      confidentialTransferDiscriminator: APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getApplyConfidentialPendingBalanceInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["expectedPendingBalanceCreditCounter", kit.getU64Decoder()],
    ["newDecryptableAvailableBalance", getDecryptableBalanceDecoder()]
  ]);
}
function getApplyConfidentialPendingBalanceInstructionDataCodec() {
  return kit.combineCodec(
    getApplyConfidentialPendingBalanceInstructionDataEncoder(),
    getApplyConfidentialPendingBalanceInstructionDataDecoder()
  );
}
function getApplyConfidentialPendingBalanceInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getApplyConfidentialPendingBalanceInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseApplyConfidentialPendingBalanceInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount()
    },
    data: getApplyConfidentialPendingBalanceInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var APPROVE_DISCRIMINATOR = 4;
function getApproveDiscriminatorBytes() {
  return kit.getU8Encoder().encode(APPROVE_DISCRIMINATOR);
}
function getApproveInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()]
    ]),
    (value) => ({ ...value, discriminator: APPROVE_DISCRIMINATOR })
  );
}
function getApproveInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()]
  ]);
}
function getApproveInstructionDataCodec() {
  return kit.combineCodec(
    getApproveInstructionDataEncoder(),
    getApproveInstructionDataDecoder()
  );
}
function getApproveInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    delegate: { value: input.delegate ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.delegate),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getApproveInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseApproveInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      delegate: getNextAccount(),
      owner: getNextAccount()
    },
    data: getApproveInstructionDataDecoder().decode(instruction.data)
  };
}
var APPROVE_CHECKED_DISCRIMINATOR = 13;
function getApproveCheckedDiscriminatorBytes() {
  return kit.getU8Encoder().encode(APPROVE_CHECKED_DISCRIMINATOR);
}
function getApproveCheckedInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: APPROVE_CHECKED_DISCRIMINATOR })
  );
}
function getApproveCheckedInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()]
  ]);
}
function getApproveCheckedInstructionDataCodec() {
  return kit.combineCodec(
    getApproveCheckedInstructionDataEncoder(),
    getApproveCheckedInstructionDataDecoder()
  );
}
function getApproveCheckedInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    delegate: { value: input.delegate ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.delegate),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getApproveCheckedInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseApproveCheckedInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      mint: getNextAccount(),
      delegate: getNextAccount(),
      owner: getNextAccount()
    },
    data: getApproveCheckedInstructionDataDecoder().decode(instruction.data)
  };
}
var APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = 27;
function getApproveConfidentialTransferAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR
  );
}
var APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 3;
function getApproveConfidentialTransferAccountConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getApproveConfidentialTransferAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR,
      confidentialTransferDiscriminator: APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getApproveConfidentialTransferAccountInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()]
  ]);
}
function getApproveConfidentialTransferAccountInstructionDataCodec() {
  return kit.combineCodec(
    getApproveConfidentialTransferAccountInstructionDataEncoder(),
    getApproveConfidentialTransferAccountInstructionDataDecoder()
  );
}
function getApproveConfidentialTransferAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority)
    ],
    programAddress,
    data: getApproveConfidentialTransferAccountInstructionDataEncoder().encode(
      {}
    )
  };
  return instruction;
}
function parseApproveConfidentialTransferAccountInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getApproveConfidentialTransferAccountInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var BURN_DISCRIMINATOR = 8;
function getBurnDiscriminatorBytes() {
  return kit.getU8Encoder().encode(BURN_DISCRIMINATOR);
}
function getBurnInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()]
    ]),
    (value) => ({ ...value, discriminator: BURN_DISCRIMINATOR })
  );
}
function getBurnInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()]
  ]);
}
function getBurnInstructionDataCodec() {
  return kit.combineCodec(
    getBurnInstructionDataEncoder(),
    getBurnInstructionDataDecoder()
  );
}
function getBurnInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getBurnInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseBurnInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getBurnInstructionDataDecoder().decode(instruction.data)
  };
}
var BURN_CHECKED_DISCRIMINATOR = 15;
function getBurnCheckedDiscriminatorBytes() {
  return kit.getU8Encoder().encode(BURN_CHECKED_DISCRIMINATOR);
}
function getBurnCheckedInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: BURN_CHECKED_DISCRIMINATOR })
  );
}
function getBurnCheckedInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()]
  ]);
}
function getBurnCheckedInstructionDataCodec() {
  return kit.combineCodec(
    getBurnCheckedInstructionDataEncoder(),
    getBurnCheckedInstructionDataDecoder()
  );
}
function getBurnCheckedInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getBurnCheckedInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseBurnCheckedInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getBurnCheckedInstructionDataDecoder().decode(instruction.data)
  };
}
var CLOSE_ACCOUNT_DISCRIMINATOR = 9;
function getCloseAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CLOSE_ACCOUNT_DISCRIMINATOR);
}
function getCloseAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: CLOSE_ACCOUNT_DISCRIMINATOR })
  );
}
function getCloseAccountInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getCloseAccountInstructionDataCodec() {
  return kit.combineCodec(
    getCloseAccountInstructionDataEncoder(),
    getCloseAccountInstructionDataDecoder()
  );
}
function getCloseAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    destination: { value: input.destination ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getCloseAccountInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseCloseAccountInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      destination: getNextAccount(),
      owner: getNextAccount()
    },
    data: getCloseAccountInstructionDataDecoder().decode(instruction.data)
  };
}
var CONFIDENTIAL_DEPOSIT_DISCRIMINATOR = 27;
function getConfidentialDepositDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CONFIDENTIAL_DEPOSIT_DISCRIMINATOR);
}
var CONFIDENTIAL_DEPOSIT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 5;
function getConfidentialDepositConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIDENTIAL_DEPOSIT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getConfidentialDepositInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIDENTIAL_DEPOSIT_DISCRIMINATOR,
      confidentialTransferDiscriminator: CONFIDENTIAL_DEPOSIT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getConfidentialDepositInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()]
  ]);
}
function getConfidentialDepositInstructionDataCodec() {
  return kit.combineCodec(
    getConfidentialDepositInstructionDataEncoder(),
    getConfidentialDepositInstructionDataDecoder()
  );
}
function getConfidentialDepositInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getConfidentialDepositInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseConfidentialDepositInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getConfidentialDepositInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 27;
function getConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CONFIDENTIAL_TRANSFER_DISCRIMINATOR);
}
var CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 7;
function getConfidentialTransferConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getConfidentialTransferInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["newSourceDecryptableAvailableBalance", getDecryptableBalanceEncoder()],
      ["equalityProofInstructionOffset", kit.getI8Encoder()],
      ["ciphertextValidityProofInstructionOffset", kit.getI8Encoder()],
      ["rangeProofInstructionOffset", kit.getI8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIDENTIAL_TRANSFER_DISCRIMINATOR,
      confidentialTransferDiscriminator: CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getConfidentialTransferInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["newSourceDecryptableAvailableBalance", getDecryptableBalanceDecoder()],
    ["equalityProofInstructionOffset", kit.getI8Decoder()],
    ["ciphertextValidityProofInstructionOffset", kit.getI8Decoder()],
    ["rangeProofInstructionOffset", kit.getI8Decoder()]
  ]);
}
function getConfidentialTransferInstructionDataCodec() {
  return kit.combineCodec(
    getConfidentialTransferInstructionDataEncoder(),
    getConfidentialTransferInstructionDataDecoder()
  );
}
function getConfidentialTransferInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    sourceToken: { value: input.sourceToken ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destinationToken: {
      value: input.destinationToken ?? null,
      isWritable: true
    },
    instructionsSysvar: {
      value: input.instructionsSysvar ?? null,
      isWritable: false
    },
    equalityRecord: { value: input.equalityRecord ?? null, isWritable: false },
    ciphertextValidityRecord: {
      value: input.ciphertextValidityRecord ?? null,
      isWritable: false
    },
    rangeRecord: { value: input.rangeRecord ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sourceToken),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destinationToken),
      getAccountMeta(accounts.instructionsSysvar),
      getAccountMeta(accounts.equalityRecord),
      getAccountMeta(accounts.ciphertextValidityRecord),
      getAccountMeta(accounts.rangeRecord),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getConfidentialTransferInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseConfidentialTransferInstruction(instruction) {
  if (instruction.accounts.length < 8) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sourceToken: getNextAccount(),
      mint: getNextAccount(),
      destinationToken: getNextAccount(),
      instructionsSysvar: getNextOptionalAccount(),
      equalityRecord: getNextOptionalAccount(),
      ciphertextValidityRecord: getNextOptionalAccount(),
      rangeRecord: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getConfidentialTransferInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CONFIDENTIAL_TRANSFER_WITH_FEE_DISCRIMINATOR = 27;
function getConfidentialTransferWithFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CONFIDENTIAL_TRANSFER_WITH_FEE_DISCRIMINATOR);
}
var CONFIDENTIAL_TRANSFER_WITH_FEE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 13;
function getConfidentialTransferWithFeeConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIDENTIAL_TRANSFER_WITH_FEE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getConfidentialTransferWithFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["newSourceDecryptableAvailableBalance", getDecryptableBalanceEncoder()],
      ["equalityProofInstructionOffset", kit.getI8Encoder()],
      [
        "transferAmountCiphertextValidityProofInstructionOffset",
        kit.getI8Encoder()
      ],
      ["feeSigmaProofInstructionOffset", kit.getI8Encoder()],
      ["feeCiphertextValidityProofInstructionOffset", kit.getI8Encoder()],
      ["rangeProofInstructionOffset", kit.getI8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIDENTIAL_TRANSFER_WITH_FEE_DISCRIMINATOR,
      confidentialTransferDiscriminator: CONFIDENTIAL_TRANSFER_WITH_FEE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getConfidentialTransferWithFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["newSourceDecryptableAvailableBalance", getDecryptableBalanceDecoder()],
    ["equalityProofInstructionOffset", kit.getI8Decoder()],
    ["transferAmountCiphertextValidityProofInstructionOffset", kit.getI8Decoder()],
    ["feeSigmaProofInstructionOffset", kit.getI8Decoder()],
    ["feeCiphertextValidityProofInstructionOffset", kit.getI8Decoder()],
    ["rangeProofInstructionOffset", kit.getI8Decoder()]
  ]);
}
function getConfidentialTransferWithFeeInstructionDataCodec() {
  return kit.combineCodec(
    getConfidentialTransferWithFeeInstructionDataEncoder(),
    getConfidentialTransferWithFeeInstructionDataDecoder()
  );
}
function getConfidentialTransferWithFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    sourceToken: { value: input.sourceToken ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destinationToken: {
      value: input.destinationToken ?? null,
      isWritable: true
    },
    instructionsSysvar: {
      value: input.instructionsSysvar ?? null,
      isWritable: false
    },
    equalityRecord: { value: input.equalityRecord ?? null, isWritable: false },
    transferAmountCiphertextValidityRecord: {
      value: input.transferAmountCiphertextValidityRecord ?? null,
      isWritable: false
    },
    feeSigmaRecord: { value: input.feeSigmaRecord ?? null, isWritable: false },
    feeCiphertextValidityRecord: {
      value: input.feeCiphertextValidityRecord ?? null,
      isWritable: false
    },
    rangeRecord: { value: input.rangeRecord ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sourceToken),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destinationToken),
      getAccountMeta(accounts.instructionsSysvar),
      getAccountMeta(accounts.equalityRecord),
      getAccountMeta(accounts.transferAmountCiphertextValidityRecord),
      getAccountMeta(accounts.feeSigmaRecord),
      getAccountMeta(accounts.feeCiphertextValidityRecord),
      getAccountMeta(accounts.rangeRecord),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getConfidentialTransferWithFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseConfidentialTransferWithFeeInstruction(instruction) {
  if (instruction.accounts.length < 10) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sourceToken: getNextAccount(),
      mint: getNextAccount(),
      destinationToken: getNextAccount(),
      instructionsSysvar: getNextOptionalAccount(),
      equalityRecord: getNextOptionalAccount(),
      transferAmountCiphertextValidityRecord: getNextOptionalAccount(),
      feeSigmaRecord: getNextOptionalAccount(),
      feeCiphertextValidityRecord: getNextOptionalAccount(),
      rangeRecord: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getConfidentialTransferWithFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CONFIDENTIAL_WITHDRAW_DISCRIMINATOR = 27;
function getConfidentialWithdrawDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CONFIDENTIAL_WITHDRAW_DISCRIMINATOR);
}
var CONFIDENTIAL_WITHDRAW_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 6;
function getConfidentialWithdrawConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIDENTIAL_WITHDRAW_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getConfidentialWithdrawInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()],
      ["newDecryptableAvailableBalance", getDecryptableBalanceEncoder()],
      ["equalityProofInstructionOffset", kit.getI8Encoder()],
      ["rangeProofInstructionOffset", kit.getI8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIDENTIAL_WITHDRAW_DISCRIMINATOR,
      confidentialTransferDiscriminator: CONFIDENTIAL_WITHDRAW_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getConfidentialWithdrawInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()],
    ["newDecryptableAvailableBalance", getDecryptableBalanceDecoder()],
    ["equalityProofInstructionOffset", kit.getI8Decoder()],
    ["rangeProofInstructionOffset", kit.getI8Decoder()]
  ]);
}
function getConfidentialWithdrawInstructionDataCodec() {
  return kit.combineCodec(
    getConfidentialWithdrawInstructionDataEncoder(),
    getConfidentialWithdrawInstructionDataDecoder()
  );
}
function getConfidentialWithdrawInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    instructionsSysvar: {
      value: input.instructionsSysvar ?? null,
      isWritable: false
    },
    equalityRecord: { value: input.equalityRecord ?? null, isWritable: false },
    rangeRecord: { value: input.rangeRecord ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.instructionsSysvar),
      getAccountMeta(accounts.equalityRecord),
      getAccountMeta(accounts.rangeRecord),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getConfidentialWithdrawInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseConfidentialWithdrawInstruction(instruction) {
  if (instruction.accounts.length < 6) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      mint: getNextAccount(),
      instructionsSysvar: getNextOptionalAccount(),
      equalityRecord: getNextOptionalAccount(),
      rangeRecord: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getConfidentialWithdrawInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = 27;
function getConfigureConfidentialTransferAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR
  );
}
var CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 2;
function getConfigureConfidentialTransferAccountConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getConfigureConfidentialTransferAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["decryptableZeroBalance", getDecryptableBalanceEncoder()],
      ["maximumPendingBalanceCreditCounter", kit.getU64Encoder()],
      ["proofInstructionOffset", kit.getI8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR,
      confidentialTransferDiscriminator: CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getConfigureConfidentialTransferAccountInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["decryptableZeroBalance", getDecryptableBalanceDecoder()],
    ["maximumPendingBalanceCreditCounter", kit.getU64Decoder()],
    ["proofInstructionOffset", kit.getI8Decoder()]
  ]);
}
function getConfigureConfidentialTransferAccountInstructionDataCodec() {
  return kit.combineCodec(
    getConfigureConfidentialTransferAccountInstructionDataEncoder(),
    getConfigureConfidentialTransferAccountInstructionDataDecoder()
  );
}
function getConfigureConfidentialTransferAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    instructionsSysvarOrContextState: {
      value: input.instructionsSysvarOrContextState ?? null,
      isWritable: false
    },
    record: { value: input.record ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.instructionsSysvarOrContextState.value) {
    accounts.instructionsSysvarOrContextState.value = "Sysvar1nstructions1111111111111111111111111";
  }
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.instructionsSysvarOrContextState),
      getAccountMeta(accounts.record),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getConfigureConfidentialTransferAccountInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseConfigureConfidentialTransferAccountInstruction(instruction) {
  if (instruction.accounts.length < 5) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      mint: getNextAccount(),
      instructionsSysvarOrContextState: getNextAccount(),
      record: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getConfigureConfidentialTransferAccountInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
async function findAssociatedTokenPda(seeds, config = {}) {
  const {
    programAddress = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
  } = config;
  return await kit.getProgramDerivedAddress({
    programAddress,
    seeds: [
      kit.getAddressEncoder().encode(seeds.owner),
      kit.getAddressEncoder().encode(seeds.tokenProgram),
      kit.getAddressEncoder().encode(seeds.mint)
    ]
  });
}

// src/generated/instructions/createAssociatedToken.ts
var CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR = 0;
function getCreateAssociatedTokenDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR);
}
function getCreateAssociatedTokenInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR
    })
  );
}
function getCreateAssociatedTokenInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getCreateAssociatedTokenInstructionDataCodec() {
  return kit.combineCodec(
    getCreateAssociatedTokenInstructionDataEncoder(),
    getCreateAssociatedTokenInstructionDataDecoder()
  );
}
async function getCreateAssociatedTokenInstructionAsync(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  if (!accounts.ata.value) {
    accounts.ata.value = await findAssociatedTokenPda({
      owner: expectAddress(accounts.owner.value),
      tokenProgram: expectAddress(accounts.tokenProgram.value),
      mint: expectAddress(accounts.mint.value)
    });
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getCreateAssociatedTokenInstructionDataEncoder().encode({})
  };
  return instruction;
}
function getCreateAssociatedTokenInstruction(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getCreateAssociatedTokenInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseCreateAssociatedTokenInstruction(instruction) {
  if (instruction.accounts.length < 6) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      payer: getNextAccount(),
      ata: getNextAccount(),
      owner: getNextAccount(),
      mint: getNextAccount(),
      systemProgram: getNextAccount(),
      tokenProgram: getNextAccount()
    },
    data: getCreateAssociatedTokenInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CREATE_ASSOCIATED_TOKEN_IDEMPOTENT_DISCRIMINATOR = 1;
function getCreateAssociatedTokenIdempotentDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    CREATE_ASSOCIATED_TOKEN_IDEMPOTENT_DISCRIMINATOR
  );
}
function getCreateAssociatedTokenIdempotentInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: CREATE_ASSOCIATED_TOKEN_IDEMPOTENT_DISCRIMINATOR
    })
  );
}
function getCreateAssociatedTokenIdempotentInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getCreateAssociatedTokenIdempotentInstructionDataCodec() {
  return kit.combineCodec(
    getCreateAssociatedTokenIdempotentInstructionDataEncoder(),
    getCreateAssociatedTokenIdempotentInstructionDataDecoder()
  );
}
async function getCreateAssociatedTokenIdempotentInstructionAsync(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  if (!accounts.ata.value) {
    accounts.ata.value = await findAssociatedTokenPda({
      owner: expectAddress(accounts.owner.value),
      tokenProgram: expectAddress(accounts.tokenProgram.value),
      mint: expectAddress(accounts.mint.value)
    });
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getCreateAssociatedTokenIdempotentInstructionDataEncoder().encode({})
  };
  return instruction;
}
function getCreateAssociatedTokenIdempotentInstruction(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    ata: { value: input.ata ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.ata),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getCreateAssociatedTokenIdempotentInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseCreateAssociatedTokenIdempotentInstruction(instruction) {
  if (instruction.accounts.length < 6) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      payer: getNextAccount(),
      ata: getNextAccount(),
      owner: getNextAccount(),
      mint: getNextAccount(),
      systemProgram: getNextAccount(),
      tokenProgram: getNextAccount()
    },
    data: getCreateAssociatedTokenIdempotentInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var CREATE_NATIVE_MINT_DISCRIMINATOR = 31;
function getCreateNativeMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(CREATE_NATIVE_MINT_DISCRIMINATOR);
}
function getCreateNativeMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: CREATE_NATIVE_MINT_DISCRIMINATOR })
  );
}
function getCreateNativeMintInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getCreateNativeMintInstructionDataCodec() {
  return kit.combineCodec(
    getCreateNativeMintInstructionDataEncoder(),
    getCreateNativeMintInstructionDataDecoder()
  );
}
function getCreateNativeMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    payer: { value: input.payer ?? null, isWritable: true },
    nativeMint: { value: input.nativeMint ?? null, isWritable: true },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.nativeMint),
      getAccountMeta(accounts.systemProgram)
    ],
    programAddress,
    data: getCreateNativeMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseCreateNativeMintInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      payer: getNextAccount(),
      nativeMint: getNextAccount(),
      systemProgram: getNextAccount()
    },
    data: getCreateNativeMintInstructionDataDecoder().decode(instruction.data)
  };
}
var DISABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR = 27;
function getDisableConfidentialCreditsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR);
}
var DISABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 10;
function getDisableConfidentialCreditsConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    DISABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getDisableConfidentialCreditsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: DISABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR,
      confidentialTransferDiscriminator: DISABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getDisableConfidentialCreditsInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()]
  ]);
}
function getDisableConfidentialCreditsInstructionDataCodec() {
  return kit.combineCodec(
    getDisableConfidentialCreditsInstructionDataEncoder(),
    getDisableConfidentialCreditsInstructionDataDecoder()
  );
}
function getDisableConfidentialCreditsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getDisableConfidentialCreditsInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseDisableConfidentialCreditsInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount()
    },
    data: getDisableConfidentialCreditsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var DISABLE_CPI_GUARD_DISCRIMINATOR = 34;
function getDisableCpiGuardDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_CPI_GUARD_DISCRIMINATOR);
}
var DISABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR = 1;
function getDisableCpiGuardCpiGuardDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR);
}
function getDisableCpiGuardInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["cpiGuardDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: DISABLE_CPI_GUARD_DISCRIMINATOR,
      cpiGuardDiscriminator: DISABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR
    })
  );
}
function getDisableCpiGuardInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["cpiGuardDiscriminator", kit.getU8Decoder()]
  ]);
}
function getDisableCpiGuardInstructionDataCodec() {
  return kit.combineCodec(
    getDisableCpiGuardInstructionDataEncoder(),
    getDisableCpiGuardInstructionDataDecoder()
  );
}
function getDisableCpiGuardInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getDisableCpiGuardInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseDisableCpiGuardInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      owner: getNextAccount()
    },
    data: getDisableCpiGuardInstructionDataDecoder().decode(instruction.data)
  };
}
var DISABLE_HARVEST_TO_MINT_DISCRIMINATOR = 37;
function getDisableHarvestToMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_HARVEST_TO_MINT_DISCRIMINATOR);
}
var DISABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 5;
function getDisableHarvestToMintConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    DISABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getDisableHarvestToMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: DISABLE_HARVEST_TO_MINT_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: DISABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getDisableHarvestToMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()]
  ]);
}
function getDisableHarvestToMintInstructionDataCodec() {
  return kit.combineCodec(
    getDisableHarvestToMintInstructionDataEncoder(),
    getDisableHarvestToMintInstructionDataDecoder()
  );
}
function getDisableHarvestToMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getDisableHarvestToMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseDisableHarvestToMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getDisableHarvestToMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var DISABLE_MEMO_TRANSFERS_DISCRIMINATOR = 30;
function getDisableMemoTransfersDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_MEMO_TRANSFERS_DISCRIMINATOR);
}
var DISABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR = 1;
function getDisableMemoTransfersMemoTransfersDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    DISABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR
  );
}
function getDisableMemoTransfersInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["memoTransfersDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: DISABLE_MEMO_TRANSFERS_DISCRIMINATOR,
      memoTransfersDiscriminator: DISABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR
    })
  );
}
function getDisableMemoTransfersInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["memoTransfersDiscriminator", kit.getU8Decoder()]
  ]);
}
function getDisableMemoTransfersInstructionDataCodec() {
  return kit.combineCodec(
    getDisableMemoTransfersInstructionDataEncoder(),
    getDisableMemoTransfersInstructionDataDecoder()
  );
}
function getDisableMemoTransfersInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getDisableMemoTransfersInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseDisableMemoTransfersInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      owner: getNextAccount()
    },
    data: getDisableMemoTransfersInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var DISABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR = 27;
function getDisableNonConfidentialCreditsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(DISABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR);
}
var DISABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 12;
function getDisableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    DISABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getDisableNonConfidentialCreditsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: DISABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR,
      confidentialTransferDiscriminator: DISABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getDisableNonConfidentialCreditsInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()]
  ]);
}
function getDisableNonConfidentialCreditsInstructionDataCodec() {
  return kit.combineCodec(
    getDisableNonConfidentialCreditsInstructionDataEncoder(),
    getDisableNonConfidentialCreditsInstructionDataDecoder()
  );
}
function getDisableNonConfidentialCreditsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getDisableNonConfidentialCreditsInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseDisableNonConfidentialCreditsInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount()
    },
    data: getDisableNonConfidentialCreditsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var EMIT_TOKEN_METADATA_DISCRIMINATOR = new Uint8Array([
  250,
  166,
  180,
  250,
  13,
  12,
  184,
  70
]);
function getEmitTokenMetadataDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(EMIT_TOKEN_METADATA_DISCRIMINATOR);
}
function getEmitTokenMetadataInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      ["start", kit.getOptionEncoder(kit.getU64Encoder())],
      ["end", kit.getOptionEncoder(kit.getU64Encoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: EMIT_TOKEN_METADATA_DISCRIMINATOR,
      start: value.start ?? kit.none(),
      end: value.end ?? kit.none()
    })
  );
}
function getEmitTokenMetadataInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    ["start", kit.getOptionDecoder(kit.getU64Decoder())],
    ["end", kit.getOptionDecoder(kit.getU64Decoder())]
  ]);
}
function getEmitTokenMetadataInstructionDataCodec() {
  return kit.combineCodec(
    getEmitTokenMetadataInstructionDataEncoder(),
    getEmitTokenMetadataInstructionDataDecoder()
  );
}
function getEmitTokenMetadataInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.metadata)],
    programAddress,
    data: getEmitTokenMetadataInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseEmitTokenMetadataInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount()
    },
    data: getEmitTokenMetadataInstructionDataDecoder().decode(instruction.data)
  };
}
var EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = 27;
function getEmptyConfidentialTransferAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR
  );
}
var EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 4;
function getEmptyConfidentialTransferAccountConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getEmptyConfidentialTransferAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["proofInstructionOffset", kit.getI8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR,
      confidentialTransferDiscriminator: EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getEmptyConfidentialTransferAccountInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["proofInstructionOffset", kit.getI8Decoder()]
  ]);
}
function getEmptyConfidentialTransferAccountInstructionDataCodec() {
  return kit.combineCodec(
    getEmptyConfidentialTransferAccountInstructionDataEncoder(),
    getEmptyConfidentialTransferAccountInstructionDataDecoder()
  );
}
function getEmptyConfidentialTransferAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    instructionsSysvarOrContextState: {
      value: input.instructionsSysvarOrContextState ?? null,
      isWritable: false
    },
    record: { value: input.record ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.instructionsSysvarOrContextState.value) {
    accounts.instructionsSysvarOrContextState.value = "Sysvar1nstructions1111111111111111111111111";
  }
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.instructionsSysvarOrContextState),
      getAccountMeta(accounts.record),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getEmptyConfidentialTransferAccountInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseEmptyConfidentialTransferAccountInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      instructionsSysvarOrContextState: getNextAccount(),
      record: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getEmptyConfidentialTransferAccountInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var ENABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR = 27;
function getEnableConfidentialCreditsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR);
}
var ENABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 9;
function getEnableConfidentialCreditsConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    ENABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getEnableConfidentialCreditsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: ENABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR,
      confidentialTransferDiscriminator: ENABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getEnableConfidentialCreditsInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()]
  ]);
}
function getEnableConfidentialCreditsInstructionDataCodec() {
  return kit.combineCodec(
    getEnableConfidentialCreditsInstructionDataEncoder(),
    getEnableConfidentialCreditsInstructionDataDecoder()
  );
}
function getEnableConfidentialCreditsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getEnableConfidentialCreditsInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseEnableConfidentialCreditsInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount()
    },
    data: getEnableConfidentialCreditsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var ENABLE_CPI_GUARD_DISCRIMINATOR = 34;
function getEnableCpiGuardDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_CPI_GUARD_DISCRIMINATOR);
}
var ENABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR = 0;
function getEnableCpiGuardCpiGuardDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR);
}
function getEnableCpiGuardInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["cpiGuardDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: ENABLE_CPI_GUARD_DISCRIMINATOR,
      cpiGuardDiscriminator: ENABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR
    })
  );
}
function getEnableCpiGuardInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["cpiGuardDiscriminator", kit.getU8Decoder()]
  ]);
}
function getEnableCpiGuardInstructionDataCodec() {
  return kit.combineCodec(
    getEnableCpiGuardInstructionDataEncoder(),
    getEnableCpiGuardInstructionDataDecoder()
  );
}
function getEnableCpiGuardInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getEnableCpiGuardInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseEnableCpiGuardInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      owner: getNextAccount()
    },
    data: getEnableCpiGuardInstructionDataDecoder().decode(instruction.data)
  };
}
var ENABLE_HARVEST_TO_MINT_DISCRIMINATOR = 37;
function getEnableHarvestToMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_HARVEST_TO_MINT_DISCRIMINATOR);
}
var ENABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 4;
function getEnableHarvestToMintConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    ENABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getEnableHarvestToMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: ENABLE_HARVEST_TO_MINT_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: ENABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getEnableHarvestToMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()]
  ]);
}
function getEnableHarvestToMintInstructionDataCodec() {
  return kit.combineCodec(
    getEnableHarvestToMintInstructionDataEncoder(),
    getEnableHarvestToMintInstructionDataDecoder()
  );
}
function getEnableHarvestToMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getEnableHarvestToMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseEnableHarvestToMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getEnableHarvestToMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var ENABLE_MEMO_TRANSFERS_DISCRIMINATOR = 30;
function getEnableMemoTransfersDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_MEMO_TRANSFERS_DISCRIMINATOR);
}
var ENABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR = 0;
function getEnableMemoTransfersMemoTransfersDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    ENABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR
  );
}
function getEnableMemoTransfersInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["memoTransfersDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: ENABLE_MEMO_TRANSFERS_DISCRIMINATOR,
      memoTransfersDiscriminator: ENABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR
    })
  );
}
function getEnableMemoTransfersInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["memoTransfersDiscriminator", kit.getU8Decoder()]
  ]);
}
function getEnableMemoTransfersInstructionDataCodec() {
  return kit.combineCodec(
    getEnableMemoTransfersInstructionDataEncoder(),
    getEnableMemoTransfersInstructionDataDecoder()
  );
}
function getEnableMemoTransfersInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getEnableMemoTransfersInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseEnableMemoTransfersInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      owner: getNextAccount()
    },
    data: getEnableMemoTransfersInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var ENABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR = 27;
function getEnableNonConfidentialCreditsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(ENABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR);
}
var ENABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 11;
function getEnableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    ENABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getEnableNonConfidentialCreditsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: ENABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR,
      confidentialTransferDiscriminator: ENABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getEnableNonConfidentialCreditsInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()]
  ]);
}
function getEnableNonConfidentialCreditsInstructionDataCodec() {
  return kit.combineCodec(
    getEnableNonConfidentialCreditsInstructionDataEncoder(),
    getEnableNonConfidentialCreditsInstructionDataDecoder()
  );
}
function getEnableNonConfidentialCreditsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getEnableNonConfidentialCreditsInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseEnableNonConfidentialCreditsInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      authority: getNextAccount()
    },
    data: getEnableNonConfidentialCreditsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var FREEZE_ACCOUNT_DISCRIMINATOR = 10;
function getFreezeAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(FREEZE_ACCOUNT_DISCRIMINATOR);
}
function getFreezeAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: FREEZE_ACCOUNT_DISCRIMINATOR })
  );
}
function getFreezeAccountInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getFreezeAccountInstructionDataCodec() {
  return kit.combineCodec(
    getFreezeAccountInstructionDataEncoder(),
    getFreezeAccountInstructionDataDecoder()
  );
}
function getFreezeAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getFreezeAccountInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseFreezeAccountInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      owner: getNextAccount()
    },
    data: getFreezeAccountInstructionDataDecoder().decode(instruction.data)
  };
}
var GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR = 21;
function getGetAccountDataSizeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR);
}
function getGetAccountDataSizeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR
    })
  );
}
function getGetAccountDataSizeInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getGetAccountDataSizeInstructionDataCodec() {
  return kit.combineCodec(
    getGetAccountDataSizeInstructionDataEncoder(),
    getGetAccountDataSizeInstructionDataDecoder()
  );
}
function getGetAccountDataSizeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getGetAccountDataSizeInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseGetAccountDataSizeInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getGetAccountDataSizeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR = 26;
function getHarvestWithheldTokensToMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR);
}
var HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR = 4;
function getHarvestWithheldTokensToMintTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getHarvestWithheldTokensToMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR,
      transferFeeDiscriminator: HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getHarvestWithheldTokensToMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()]
  ]);
}
function getHarvestWithheldTokensToMintInstructionDataCodec() {
  return kit.combineCodec(
    getHarvestWithheldTokensToMintInstructionDataEncoder(),
    getHarvestWithheldTokensToMintInstructionDataDecoder()
  );
}
function getHarvestWithheldTokensToMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = args.sources.map((address) => ({
    address,
    role: kit.AccountRole.WRITABLE
  }));
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint), ...remainingAccounts],
    programAddress,
    data: getHarvestWithheldTokensToMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseHarvestWithheldTokensToMintInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getHarvestWithheldTokensToMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 37;
function getHarvestWithheldTokensToMintForConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
var HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 3;
function getHarvestWithheldTokensToMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()]
  ]);
}
function getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataCodec() {
  return kit.combineCodec(
    getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataEncoder(),
    getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataDecoder()
  );
}
function getHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.sources ?? []).map(
    (address) => ({ address, role: kit.AccountRole.WRITABLE })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint), ...remainingAccounts],
    programAddress,
    data: getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataEncoder().encode(
      {}
    )
  };
  return instruction;
}
function parseHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_ACCOUNT_DISCRIMINATOR = 1;
function getInitializeAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_ACCOUNT_DISCRIMINATOR);
}
function getInitializeAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: INITIALIZE_ACCOUNT_DISCRIMINATOR })
  );
}
function getInitializeAccountInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getInitializeAccountInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeAccountInstructionDataEncoder(),
    getInitializeAccountInstructionDataDecoder()
  );
}
function getInitializeAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false },
    rent: { value: input.rent ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.rent.value) {
    accounts.rent.value = "SysvarRent111111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.owner),
      getAccountMeta(accounts.rent)
    ],
    programAddress,
    data: getInitializeAccountInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseInitializeAccountInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      owner: getNextAccount(),
      rent: getNextAccount()
    },
    data: getInitializeAccountInstructionDataDecoder().decode(instruction.data)
  };
}
var INITIALIZE_ACCOUNT2_DISCRIMINATOR = 16;
function getInitializeAccount2DiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_ACCOUNT2_DISCRIMINATOR);
}
function getInitializeAccount2InstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["owner", kit.getAddressEncoder()]
    ]),
    (value) => ({ ...value, discriminator: INITIALIZE_ACCOUNT2_DISCRIMINATOR })
  );
}
function getInitializeAccount2InstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["owner", kit.getAddressDecoder()]
  ]);
}
function getInitializeAccount2InstructionDataCodec() {
  return kit.combineCodec(
    getInitializeAccount2InstructionDataEncoder(),
    getInitializeAccount2InstructionDataDecoder()
  );
}
function getInitializeAccount2Instruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    rent: { value: input.rent ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.rent.value) {
    accounts.rent.value = "SysvarRent111111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.rent)
    ],
    programAddress,
    data: getInitializeAccount2InstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeAccount2Instruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      rent: getNextAccount()
    },
    data: getInitializeAccount2InstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_ACCOUNT3_DISCRIMINATOR = 18;
function getInitializeAccount3DiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_ACCOUNT3_DISCRIMINATOR);
}
function getInitializeAccount3InstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["owner", kit.getAddressEncoder()]
    ]),
    (value) => ({ ...value, discriminator: INITIALIZE_ACCOUNT3_DISCRIMINATOR })
  );
}
function getInitializeAccount3InstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["owner", kit.getAddressDecoder()]
  ]);
}
function getInitializeAccount3InstructionDataCodec() {
  return kit.combineCodec(
    getInitializeAccount3InstructionDataEncoder(),
    getInitializeAccount3InstructionDataDecoder()
  );
}
function getInitializeAccount3Instruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.account), getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeAccount3InstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeAccount3Instruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount()
    },
    data: getInitializeAccount3InstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 37;
function getInitializeConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
var INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 0;
function getInitializeConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getInitializeConfidentialTransferFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      [
        "withdrawWithheldAuthorityElGamalPubkey",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getInitializeConfidentialTransferFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    [
      "withdrawWithheldAuthorityElGamalPubkey",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeConfidentialTransferFeeInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeConfidentialTransferFeeInstructionDataEncoder(),
    getInitializeConfidentialTransferFeeInstructionDataDecoder()
  );
}
function getInitializeConfidentialTransferFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeConfidentialTransferFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeConfidentialTransferFeeInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeConfidentialTransferFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR = 27;
function getInitializeConfidentialTransferMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR
  );
}
var INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 0;
function getInitializeConfidentialTransferMintConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getInitializeConfidentialTransferMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      ["autoApproveNewAccounts", kit.getBooleanEncoder()],
      [
        "auditorElgamalPubkey",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR,
      confidentialTransferDiscriminator: INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getInitializeConfidentialTransferMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    ["autoApproveNewAccounts", kit.getBooleanDecoder()],
    [
      "auditorElgamalPubkey",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeConfidentialTransferMintInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeConfidentialTransferMintInstructionDataEncoder(),
    getInitializeConfidentialTransferMintInstructionDataDecoder()
  );
}
function getInitializeConfidentialTransferMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeConfidentialTransferMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeConfidentialTransferMintInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeConfidentialTransferMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 28;
function getInitializeDefaultAccountStateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR);
}
var INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 0;
function getInitializeDefaultAccountStateDefaultAccountStateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR
  );
}
function getInitializeDefaultAccountStateInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["defaultAccountStateDiscriminator", kit.getU8Encoder()],
      ["state", getAccountStateEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR,
      defaultAccountStateDiscriminator: INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR
    })
  );
}
function getInitializeDefaultAccountStateInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["defaultAccountStateDiscriminator", kit.getU8Decoder()],
    ["state", getAccountStateDecoder()]
  ]);
}
function getInitializeDefaultAccountStateInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeDefaultAccountStateInstructionDataEncoder(),
    getInitializeDefaultAccountStateInstructionDataDecoder()
  );
}
function getInitializeDefaultAccountStateInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeDefaultAccountStateInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeDefaultAccountStateInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeDefaultAccountStateInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR = 41;
function getInitializeGroupMemberPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR);
}
var INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = 0;
function getInitializeGroupMemberPointerGroupMemberPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
  );
}
function getInitializeGroupMemberPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["groupMemberPointerDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      [
        "memberAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR,
      groupMemberPointerDiscriminator: INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
    })
  );
}
function getInitializeGroupMemberPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["groupMemberPointerDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    [
      "memberAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeGroupMemberPointerInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeGroupMemberPointerInstructionDataEncoder(),
    getInitializeGroupMemberPointerInstructionDataDecoder()
  );
}
function getInitializeGroupMemberPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeGroupMemberPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeGroupMemberPointerInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeGroupMemberPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_GROUP_POINTER_DISCRIMINATOR = 40;
function getInitializeGroupPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_GROUP_POINTER_DISCRIMINATOR);
}
var INITIALIZE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR = 0;
function getInitializeGroupPointerGroupPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR
  );
}
function getInitializeGroupPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["groupPointerDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      [
        "groupAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_GROUP_POINTER_DISCRIMINATOR,
      groupPointerDiscriminator: INITIALIZE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR
    })
  );
}
function getInitializeGroupPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["groupPointerDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    [
      "groupAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeGroupPointerInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeGroupPointerInstructionDataEncoder(),
    getInitializeGroupPointerInstructionDataDecoder()
  );
}
function getInitializeGroupPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeGroupPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeGroupPointerInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeGroupPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR = 22;
function getInitializeImmutableOwnerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR);
}
function getInitializeImmutableOwnerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR
    })
  );
}
function getInitializeImmutableOwnerInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getInitializeImmutableOwnerInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeImmutableOwnerInstructionDataEncoder(),
    getInitializeImmutableOwnerInstructionDataDecoder()
  );
}
function getInitializeImmutableOwnerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.account)],
    programAddress,
    data: getInitializeImmutableOwnerInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseInitializeImmutableOwnerInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount()
    },
    data: getInitializeImmutableOwnerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_INTEREST_BEARING_MINT_DISCRIMINATOR = 33;
function getInitializeInterestBearingMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_INTEREST_BEARING_MINT_DISCRIMINATOR);
}
var INITIALIZE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR = 0;
function getInitializeInterestBearingMintInterestBearingMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR
  );
}
function getInitializeInterestBearingMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["interestBearingMintDiscriminator", kit.getU8Encoder()],
      [
        "rateAuthority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      ["rate", kit.getI16Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_INTEREST_BEARING_MINT_DISCRIMINATOR,
      interestBearingMintDiscriminator: INITIALIZE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR
    })
  );
}
function getInitializeInterestBearingMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["interestBearingMintDiscriminator", kit.getU8Decoder()],
    [
      "rateAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    ["rate", kit.getI16Decoder()]
  ]);
}
function getInitializeInterestBearingMintInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeInterestBearingMintInstructionDataEncoder(),
    getInitializeInterestBearingMintInstructionDataDecoder()
  );
}
function getInitializeInterestBearingMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeInterestBearingMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeInterestBearingMintInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeInterestBearingMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_METADATA_POINTER_DISCRIMINATOR = 39;
function getInitializeMetadataPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_METADATA_POINTER_DISCRIMINATOR);
}
var INITIALIZE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR = 0;
function getInitializeMetadataPointerMetadataPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR
  );
}
function getInitializeMetadataPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["metadataPointerDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      [
        "metadataAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_METADATA_POINTER_DISCRIMINATOR,
      metadataPointerDiscriminator: INITIALIZE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR
    })
  );
}
function getInitializeMetadataPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["metadataPointerDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    [
      "metadataAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeMetadataPointerInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMetadataPointerInstructionDataEncoder(),
    getInitializeMetadataPointerInstructionDataDecoder()
  );
}
function getInitializeMetadataPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeMetadataPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMetadataPointerInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeMetadataPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_MINT_DISCRIMINATOR = 0;
function getInitializeMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_MINT_DISCRIMINATOR);
}
function getInitializeMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["decimals", kit.getU8Encoder()],
      ["mintAuthority", kit.getAddressEncoder()],
      ["freezeAuthority", kit.getOptionEncoder(kit.getAddressEncoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_MINT_DISCRIMINATOR,
      freezeAuthority: value.freezeAuthority ?? kit.none()
    })
  );
}
function getInitializeMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["decimals", kit.getU8Decoder()],
    ["mintAuthority", kit.getAddressDecoder()],
    ["freezeAuthority", kit.getOptionDecoder(kit.getAddressDecoder())]
  ]);
}
function getInitializeMintInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMintInstructionDataEncoder(),
    getInitializeMintInstructionDataDecoder()
  );
}
function getInitializeMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    rent: { value: input.rent ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.rent.value) {
    accounts.rent.value = "SysvarRent111111111111111111111111111111111";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint), getAccountMeta(accounts.rent)],
    programAddress,
    data: getInitializeMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      rent: getNextAccount()
    },
    data: getInitializeMintInstructionDataDecoder().decode(instruction.data)
  };
}
var INITIALIZE_MINT2_DISCRIMINATOR = 20;
function getInitializeMint2DiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_MINT2_DISCRIMINATOR);
}
function getInitializeMint2InstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["decimals", kit.getU8Encoder()],
      ["mintAuthority", kit.getAddressEncoder()],
      ["freezeAuthority", kit.getOptionEncoder(kit.getAddressEncoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_MINT2_DISCRIMINATOR,
      freezeAuthority: value.freezeAuthority ?? kit.none()
    })
  );
}
function getInitializeMint2InstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["decimals", kit.getU8Decoder()],
    ["mintAuthority", kit.getAddressDecoder()],
    ["freezeAuthority", kit.getOptionDecoder(kit.getAddressDecoder())]
  ]);
}
function getInitializeMint2InstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMint2InstructionDataEncoder(),
    getInitializeMint2InstructionDataDecoder()
  );
}
function getInitializeMint2Instruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeMint2InstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMint2Instruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeMint2InstructionDataDecoder().decode(instruction.data)
  };
}
var INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR = 25;
function getInitializeMintCloseAuthorityDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR);
}
function getInitializeMintCloseAuthorityInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["closeAuthority", kit.getOptionEncoder(kit.getAddressEncoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR
    })
  );
}
function getInitializeMintCloseAuthorityInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["closeAuthority", kit.getOptionDecoder(kit.getAddressDecoder())]
  ]);
}
function getInitializeMintCloseAuthorityInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMintCloseAuthorityInstructionDataEncoder(),
    getInitializeMintCloseAuthorityInstructionDataDecoder()
  );
}
function getInitializeMintCloseAuthorityInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeMintCloseAuthorityInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMintCloseAuthorityInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeMintCloseAuthorityInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_MULTISIG_DISCRIMINATOR = 2;
function getInitializeMultisigDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_MULTISIG_DISCRIMINATOR);
}
function getInitializeMultisigInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["m", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: INITIALIZE_MULTISIG_DISCRIMINATOR })
  );
}
function getInitializeMultisigInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["m", kit.getU8Decoder()]
  ]);
}
function getInitializeMultisigInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMultisigInstructionDataEncoder(),
    getInitializeMultisigInstructionDataDecoder()
  );
}
function getInitializeMultisigInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    multisig: { value: input.multisig ?? null, isWritable: true },
    rent: { value: input.rent ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.rent.value) {
    accounts.rent.value = "SysvarRent111111111111111111111111111111111";
  }
  const remainingAccounts = args.signers.map((address) => ({
    address,
    role: kit.AccountRole.READONLY
  }));
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.multisig),
      getAccountMeta(accounts.rent),
      ...remainingAccounts
    ],
    programAddress,
    data: getInitializeMultisigInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMultisigInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      multisig: getNextAccount(),
      rent: getNextAccount()
    },
    data: getInitializeMultisigInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_MULTISIG2_DISCRIMINATOR = 19;
function getInitializeMultisig2DiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_MULTISIG2_DISCRIMINATOR);
}
function getInitializeMultisig2InstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["m", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: INITIALIZE_MULTISIG2_DISCRIMINATOR })
  );
}
function getInitializeMultisig2InstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["m", kit.getU8Decoder()]
  ]);
}
function getInitializeMultisig2InstructionDataCodec() {
  return kit.combineCodec(
    getInitializeMultisig2InstructionDataEncoder(),
    getInitializeMultisig2InstructionDataDecoder()
  );
}
function getInitializeMultisig2Instruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    multisig: { value: input.multisig ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = args.signers.map((address) => ({
    address,
    role: kit.AccountRole.READONLY
  }));
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.multisig), ...remainingAccounts],
    programAddress,
    data: getInitializeMultisig2InstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeMultisig2Instruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      multisig: getNextAccount()
    },
    data: getInitializeMultisig2InstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_NON_TRANSFERABLE_MINT_DISCRIMINATOR = 32;
function getInitializeNonTransferableMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_NON_TRANSFERABLE_MINT_DISCRIMINATOR);
}
function getInitializeNonTransferableMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_NON_TRANSFERABLE_MINT_DISCRIMINATOR
    })
  );
}
function getInitializeNonTransferableMintInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getInitializeNonTransferableMintInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeNonTransferableMintInstructionDataEncoder(),
    getInitializeNonTransferableMintInstructionDataDecoder()
  );
}
function getInitializeNonTransferableMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeNonTransferableMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseInitializeNonTransferableMintInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeNonTransferableMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR = 44;
function getInitializePausableConfigDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR);
}
var INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR = 0;
function getInitializePausableConfigPausableDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR
  );
}
function getInitializePausableConfigInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["pausableDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR,
      pausableDiscriminator: INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR
    })
  );
}
function getInitializePausableConfigInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["pausableDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializePausableConfigInstructionDataCodec() {
  return kit.combineCodec(
    getInitializePausableConfigInstructionDataEncoder(),
    getInitializePausableConfigInstructionDataDecoder()
  );
}
function getInitializePausableConfigInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializePausableConfigInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializePausableConfigInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializePausableConfigInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_PERMANENT_DELEGATE_DISCRIMINATOR = 35;
function getInitializePermanentDelegateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_PERMANENT_DELEGATE_DISCRIMINATOR);
}
function getInitializePermanentDelegateInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["delegate", kit.getAddressEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_PERMANENT_DELEGATE_DISCRIMINATOR
    })
  );
}
function getInitializePermanentDelegateInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["delegate", kit.getAddressDecoder()]
  ]);
}
function getInitializePermanentDelegateInstructionDataCodec() {
  return kit.combineCodec(
    getInitializePermanentDelegateInstructionDataEncoder(),
    getInitializePermanentDelegateInstructionDataDecoder()
  );
}
function getInitializePermanentDelegateInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializePermanentDelegateInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializePermanentDelegateInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializePermanentDelegateInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = 43;
function getInitializeScaledUiAmountMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR);
}
var INITIALIZE_SCALED_UI_AMOUNT_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = 0;
function getInitializeScaledUiAmountMintScaledUiAmountMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_SCALED_UI_AMOUNT_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR
  );
}
function getInitializeScaledUiAmountMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["scaledUiAmountMintDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      ["multiplier", kit.getF64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR,
      scaledUiAmountMintDiscriminator: INITIALIZE_SCALED_UI_AMOUNT_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR
    })
  );
}
function getInitializeScaledUiAmountMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["scaledUiAmountMintDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    ["multiplier", kit.getF64Decoder()]
  ]);
}
function getInitializeScaledUiAmountMintInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeScaledUiAmountMintInstructionDataEncoder(),
    getInitializeScaledUiAmountMintInstructionDataDecoder()
  );
}
function getInitializeScaledUiAmountMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeScaledUiAmountMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeScaledUiAmountMintInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeScaledUiAmountMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_TOKEN_GROUP_DISCRIMINATOR = new Uint8Array([
  121,
  113,
  108,
  39,
  54,
  51,
  0,
  4
]);
function getInitializeTokenGroupDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(INITIALIZE_TOKEN_GROUP_DISCRIMINATOR);
}
function getInitializeTokenGroupInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      [
        "updateAuthority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      ["maxSize", kit.getU64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TOKEN_GROUP_DISCRIMINATOR
    })
  );
}
function getInitializeTokenGroupInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    [
      "updateAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    ["maxSize", kit.getU64Decoder()]
  ]);
}
function getInitializeTokenGroupInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeTokenGroupInstructionDataEncoder(),
    getInitializeTokenGroupInstructionDataDecoder()
  );
}
function getInitializeTokenGroupInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    group: { value: input.group ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.group),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.mintAuthority)
    ],
    programAddress,
    data: getInitializeTokenGroupInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeTokenGroupInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      group: getNextAccount(),
      mint: getNextAccount(),
      mintAuthority: getNextAccount()
    },
    data: getInitializeTokenGroupInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_TOKEN_GROUP_MEMBER_DISCRIMINATOR = new Uint8Array([
  152,
  32,
  222,
  176,
  223,
  237,
  116,
  134
]);
function getInitializeTokenGroupMemberDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(INITIALIZE_TOKEN_GROUP_MEMBER_DISCRIMINATOR);
}
function getInitializeTokenGroupMemberInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getBytesEncoder()]]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TOKEN_GROUP_MEMBER_DISCRIMINATOR
    })
  );
}
function getInitializeTokenGroupMemberInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getBytesDecoder()]]);
}
function getInitializeTokenGroupMemberInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeTokenGroupMemberInstructionDataEncoder(),
    getInitializeTokenGroupMemberInstructionDataDecoder()
  );
}
function getInitializeTokenGroupMemberInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    member: { value: input.member ?? null, isWritable: true },
    memberMint: { value: input.memberMint ?? null, isWritable: false },
    memberMintAuthority: {
      value: input.memberMintAuthority ?? null,
      isWritable: false
    },
    group: { value: input.group ?? null, isWritable: true },
    groupUpdateAuthority: {
      value: input.groupUpdateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.member),
      getAccountMeta(accounts.memberMint),
      getAccountMeta(accounts.memberMintAuthority),
      getAccountMeta(accounts.group),
      getAccountMeta(accounts.groupUpdateAuthority)
    ],
    programAddress,
    data: getInitializeTokenGroupMemberInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseInitializeTokenGroupMemberInstruction(instruction) {
  if (instruction.accounts.length < 5) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      member: getNextAccount(),
      memberMint: getNextAccount(),
      memberMintAuthority: getNextAccount(),
      group: getNextAccount(),
      groupUpdateAuthority: getNextAccount()
    },
    data: getInitializeTokenGroupMemberInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_TOKEN_METADATA_DISCRIMINATOR = new Uint8Array([
  210,
  225,
  30,
  162,
  88,
  184,
  77,
  141
]);
function getInitializeTokenMetadataDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(INITIALIZE_TOKEN_METADATA_DISCRIMINATOR);
}
function getInitializeTokenMetadataInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      ["name", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())],
      ["symbol", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())],
      ["uri", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TOKEN_METADATA_DISCRIMINATOR
    })
  );
}
function getInitializeTokenMetadataInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    ["name", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())],
    ["symbol", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())],
    ["uri", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())]
  ]);
}
function getInitializeTokenMetadataInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeTokenMetadataInstructionDataEncoder(),
    getInitializeTokenMetadataInstructionDataDecoder()
  );
}
function getInitializeTokenMetadataInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    },
    mint: { value: input.mint ?? null, isWritable: false },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.mintAuthority)
    ],
    programAddress,
    data: getInitializeTokenMetadataInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeTokenMetadataInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount(),
      mint: getNextAccount(),
      mintAuthority: getNextAccount()
    },
    data: getInitializeTokenMetadataInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR = 26;
function getInitializeTransferFeeConfigDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR);
}
var INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR = 0;
function getInitializeTransferFeeConfigTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getInitializeTransferFeeConfigInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()],
      ["transferFeeConfigAuthority", kit.getOptionEncoder(kit.getAddressEncoder())],
      ["withdrawWithheldAuthority", kit.getOptionEncoder(kit.getAddressEncoder())],
      ["transferFeeBasisPoints", kit.getU16Encoder()],
      ["maximumFee", kit.getU64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR,
      transferFeeDiscriminator: INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getInitializeTransferFeeConfigInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()],
    ["transferFeeConfigAuthority", kit.getOptionDecoder(kit.getAddressDecoder())],
    ["withdrawWithheldAuthority", kit.getOptionDecoder(kit.getAddressDecoder())],
    ["transferFeeBasisPoints", kit.getU16Decoder()],
    ["maximumFee", kit.getU64Decoder()]
  ]);
}
function getInitializeTransferFeeConfigInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeTransferFeeConfigInstructionDataEncoder(),
    getInitializeTransferFeeConfigInstructionDataDecoder()
  );
}
function getInitializeTransferFeeConfigInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeTransferFeeConfigInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeTransferFeeConfigInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeTransferFeeConfigInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR = 36;
function getInitializeTransferHookDiscriminatorBytes() {
  return kit.getU8Encoder().encode(INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR);
}
var INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR = 0;
function getInitializeTransferHookTransferHookDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR
  );
}
function getInitializeTransferHookInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferHookDiscriminator", kit.getU8Encoder()],
      [
        "authority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ],
      [
        "programId",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR,
      transferHookDiscriminator: INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR
    })
  );
}
function getInitializeTransferHookInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferHookDiscriminator", kit.getU8Decoder()],
    [
      "authority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ],
    [
      "programId",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getInitializeTransferHookInstructionDataCodec() {
  return kit.combineCodec(
    getInitializeTransferHookInstructionDataEncoder(),
    getInitializeTransferHookInstructionDataDecoder()
  );
}
function getInitializeTransferHookInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getInitializeTransferHookInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseInitializeTransferHookInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getInitializeTransferHookInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var MINT_TO_DISCRIMINATOR = 7;
function getMintToDiscriminatorBytes() {
  return kit.getU8Encoder().encode(MINT_TO_DISCRIMINATOR);
}
function getMintToInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()]
    ]),
    (value) => ({ ...value, discriminator: MINT_TO_DISCRIMINATOR })
  );
}
function getMintToInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()]
  ]);
}
function getMintToInstructionDataCodec() {
  return kit.combineCodec(
    getMintToInstructionDataEncoder(),
    getMintToInstructionDataDecoder()
  );
}
function getMintToInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    token: { value: input.token ?? null, isWritable: true },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mintAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getMintToInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseMintToInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      token: getNextAccount(),
      mintAuthority: getNextAccount()
    },
    data: getMintToInstructionDataDecoder().decode(instruction.data)
  };
}
var MINT_TO_CHECKED_DISCRIMINATOR = 14;
function getMintToCheckedDiscriminatorBytes() {
  return kit.getU8Encoder().encode(MINT_TO_CHECKED_DISCRIMINATOR);
}
function getMintToCheckedInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: MINT_TO_CHECKED_DISCRIMINATOR })
  );
}
function getMintToCheckedInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()]
  ]);
}
function getMintToCheckedInstructionDataCodec() {
  return kit.combineCodec(
    getMintToCheckedInstructionDataEncoder(),
    getMintToCheckedInstructionDataDecoder()
  );
}
function getMintToCheckedInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    token: { value: input.token ?? null, isWritable: true },
    mintAuthority: { value: input.mintAuthority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.mintAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getMintToCheckedInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseMintToCheckedInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      token: getNextAccount(),
      mintAuthority: getNextAccount()
    },
    data: getMintToCheckedInstructionDataDecoder().decode(instruction.data)
  };
}
var PAUSE_DISCRIMINATOR = 44;
function getPauseDiscriminatorBytes() {
  return kit.getU8Encoder().encode(PAUSE_DISCRIMINATOR);
}
var PAUSE_PAUSABLE_DISCRIMINATOR = 1;
function getPausePausableDiscriminatorBytes() {
  return kit.getU8Encoder().encode(PAUSE_PAUSABLE_DISCRIMINATOR);
}
function getPauseInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["pausableDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: PAUSE_DISCRIMINATOR,
      pausableDiscriminator: PAUSE_PAUSABLE_DISCRIMINATOR
    })
  );
}
function getPauseInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["pausableDiscriminator", kit.getU8Decoder()]
  ]);
}
function getPauseInstructionDataCodec() {
  return kit.combineCodec(
    getPauseInstructionDataEncoder(),
    getPauseInstructionDataDecoder()
  );
}
function getPauseInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority)
    ],
    programAddress,
    data: getPauseInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parsePauseInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getPauseInstructionDataDecoder().decode(instruction.data)
  };
}
var REALLOCATE_DISCRIMINATOR = 29;
function getReallocateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(REALLOCATE_DISCRIMINATOR);
}
function getReallocateInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      [
        "newExtensionTypes",
        kit.getArrayEncoder(getExtensionTypeEncoder(), { size: "remainder" })
      ]
    ]),
    (value) => ({ ...value, discriminator: REALLOCATE_DISCRIMINATOR })
  );
}
function getReallocateInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    [
      "newExtensionTypes",
      kit.getArrayDecoder(getExtensionTypeDecoder(), { size: "remainder" })
    ]
  ]);
}
function getReallocateInstructionDataCodec() {
  return kit.combineCodec(
    getReallocateInstructionDataEncoder(),
    getReallocateInstructionDataDecoder()
  );
}
function getReallocateInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    token: { value: input.token ?? null, isWritable: true },
    payer: { value: input.payer ?? null, isWritable: true },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value = "11111111111111111111111111111111";
  }
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.token),
      getAccountMeta(accounts.payer),
      getAccountMeta(accounts.systemProgram),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getReallocateInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseReallocateInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      token: getNextAccount(),
      payer: getNextAccount(),
      systemProgram: getNextAccount(),
      owner: getNextAccount()
    },
    data: getReallocateInstructionDataDecoder().decode(instruction.data)
  };
}
var RECOVER_NESTED_ASSOCIATED_TOKEN_DISCRIMINATOR = 2;
function getRecoverNestedAssociatedTokenDiscriminatorBytes() {
  return kit.getU8Encoder().encode(RECOVER_NESTED_ASSOCIATED_TOKEN_DISCRIMINATOR);
}
function getRecoverNestedAssociatedTokenInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: RECOVER_NESTED_ASSOCIATED_TOKEN_DISCRIMINATOR
    })
  );
}
function getRecoverNestedAssociatedTokenInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getRecoverNestedAssociatedTokenInstructionDataCodec() {
  return kit.combineCodec(
    getRecoverNestedAssociatedTokenInstructionDataEncoder(),
    getRecoverNestedAssociatedTokenInstructionDataDecoder()
  );
}
async function getRecoverNestedAssociatedTokenInstructionAsync(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    nestedAssociatedAccountAddress: {
      value: input.nestedAssociatedAccountAddress ?? null,
      isWritable: true
    },
    nestedTokenMintAddress: {
      value: input.nestedTokenMintAddress ?? null,
      isWritable: false
    },
    destinationAssociatedAccountAddress: {
      value: input.destinationAssociatedAccountAddress ?? null,
      isWritable: true
    },
    ownerAssociatedAccountAddress: {
      value: input.ownerAssociatedAccountAddress ?? null,
      isWritable: false
    },
    ownerTokenMintAddress: {
      value: input.ownerTokenMintAddress ?? null,
      isWritable: false
    },
    walletAddress: { value: input.walletAddress ?? null, isWritable: true },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  if (!accounts.ownerAssociatedAccountAddress.value) {
    accounts.ownerAssociatedAccountAddress.value = await findAssociatedTokenPda(
      {
        owner: expectAddress(accounts.walletAddress.value),
        tokenProgram: expectAddress(accounts.tokenProgram.value),
        mint: expectAddress(accounts.ownerTokenMintAddress.value)
      }
    );
  }
  if (!accounts.nestedAssociatedAccountAddress.value) {
    accounts.nestedAssociatedAccountAddress.value = await findAssociatedTokenPda({
      owner: expectAddress(accounts.ownerAssociatedAccountAddress.value),
      tokenProgram: expectAddress(accounts.tokenProgram.value),
      mint: expectAddress(accounts.nestedTokenMintAddress.value)
    });
  }
  if (!accounts.destinationAssociatedAccountAddress.value) {
    accounts.destinationAssociatedAccountAddress.value = await findAssociatedTokenPda({
      owner: expectAddress(accounts.walletAddress.value),
      tokenProgram: expectAddress(accounts.tokenProgram.value),
      mint: expectAddress(accounts.nestedTokenMintAddress.value)
    });
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.nestedAssociatedAccountAddress),
      getAccountMeta(accounts.nestedTokenMintAddress),
      getAccountMeta(accounts.destinationAssociatedAccountAddress),
      getAccountMeta(accounts.ownerAssociatedAccountAddress),
      getAccountMeta(accounts.ownerTokenMintAddress),
      getAccountMeta(accounts.walletAddress),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getRecoverNestedAssociatedTokenInstructionDataEncoder().encode({})
  };
  return instruction;
}
function getRecoverNestedAssociatedTokenInstruction(input, config) {
  const programAddress = config?.programAddress ?? ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
  const originalAccounts = {
    nestedAssociatedAccountAddress: {
      value: input.nestedAssociatedAccountAddress ?? null,
      isWritable: true
    },
    nestedTokenMintAddress: {
      value: input.nestedTokenMintAddress ?? null,
      isWritable: false
    },
    destinationAssociatedAccountAddress: {
      value: input.destinationAssociatedAccountAddress ?? null,
      isWritable: true
    },
    ownerAssociatedAccountAddress: {
      value: input.ownerAssociatedAccountAddress ?? null,
      isWritable: false
    },
    ownerTokenMintAddress: {
      value: input.ownerTokenMintAddress ?? null,
      isWritable: false
    },
    walletAddress: { value: input.walletAddress ?? null, isWritable: true },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";
  }
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.nestedAssociatedAccountAddress),
      getAccountMeta(accounts.nestedTokenMintAddress),
      getAccountMeta(accounts.destinationAssociatedAccountAddress),
      getAccountMeta(accounts.ownerAssociatedAccountAddress),
      getAccountMeta(accounts.ownerTokenMintAddress),
      getAccountMeta(accounts.walletAddress),
      getAccountMeta(accounts.tokenProgram)
    ],
    programAddress,
    data: getRecoverNestedAssociatedTokenInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseRecoverNestedAssociatedTokenInstruction(instruction) {
  if (instruction.accounts.length < 7) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      nestedAssociatedAccountAddress: getNextAccount(),
      nestedTokenMintAddress: getNextAccount(),
      destinationAssociatedAccountAddress: getNextAccount(),
      ownerAssociatedAccountAddress: getNextAccount(),
      ownerTokenMintAddress: getNextAccount(),
      walletAddress: getNextAccount(),
      tokenProgram: getNextAccount()
    },
    data: getRecoverNestedAssociatedTokenInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR = new Uint8Array([
  234,
  18,
  32,
  56,
  89,
  141,
  37,
  181
]);
function getRemoveTokenMetadataKeyDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR);
}
function getRemoveTokenMetadataKeyInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      ["idempotent", kit.getBooleanEncoder()],
      ["key", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR,
      idempotent: value.idempotent ?? false
    })
  );
}
function getRemoveTokenMetadataKeyInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    ["idempotent", kit.getBooleanDecoder()],
    ["key", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())]
  ]);
}
function getRemoveTokenMetadataKeyInstructionDataCodec() {
  return kit.combineCodec(
    getRemoveTokenMetadataKeyInstructionDataEncoder(),
    getRemoveTokenMetadataKeyInstructionDataDecoder()
  );
}
function getRemoveTokenMetadataKeyInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority)
    ],
    programAddress,
    data: getRemoveTokenMetadataKeyInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseRemoveTokenMetadataKeyInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount()
    },
    data: getRemoveTokenMetadataKeyInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var RESUME_DISCRIMINATOR = 44;
function getResumeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(RESUME_DISCRIMINATOR);
}
var RESUME_PAUSABLE_DISCRIMINATOR = 2;
function getResumePausableDiscriminatorBytes() {
  return kit.getU8Encoder().encode(RESUME_PAUSABLE_DISCRIMINATOR);
}
function getResumeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["pausableDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: RESUME_DISCRIMINATOR,
      pausableDiscriminator: RESUME_PAUSABLE_DISCRIMINATOR
    })
  );
}
function getResumeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["pausableDiscriminator", kit.getU8Decoder()]
  ]);
}
function getResumeInstructionDataCodec() {
  return kit.combineCodec(
    getResumeInstructionDataEncoder(),
    getResumeInstructionDataDecoder()
  );
}
function getResumeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority)
    ],
    programAddress,
    data: getResumeInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseResumeInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getResumeInstructionDataDecoder().decode(instruction.data)
  };
}
var REVOKE_DISCRIMINATOR = 5;
function getRevokeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(REVOKE_DISCRIMINATOR);
}
function getRevokeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: REVOKE_DISCRIMINATOR })
  );
}
function getRevokeInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getRevokeInstructionDataCodec() {
  return kit.combineCodec(
    getRevokeInstructionDataEncoder(),
    getRevokeInstructionDataDecoder()
  );
}
function getRevokeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getRevokeInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseRevokeInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      owner: getNextAccount()
    },
    data: getRevokeInstructionDataDecoder().decode(instruction.data)
  };
}
var SET_AUTHORITY_DISCRIMINATOR = 6;
function getSetAuthorityDiscriminatorBytes() {
  return kit.getU8Encoder().encode(SET_AUTHORITY_DISCRIMINATOR);
}
function getSetAuthorityInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["authorityType", getAuthorityTypeEncoder()],
      ["newAuthority", kit.getOptionEncoder(kit.getAddressEncoder())]
    ]),
    (value) => ({ ...value, discriminator: SET_AUTHORITY_DISCRIMINATOR })
  );
}
function getSetAuthorityInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["authorityType", getAuthorityTypeDecoder()],
    ["newAuthority", kit.getOptionDecoder(kit.getAddressDecoder())]
  ]);
}
function getSetAuthorityInstructionDataCodec() {
  return kit.combineCodec(
    getSetAuthorityInstructionDataEncoder(),
    getSetAuthorityInstructionDataDecoder()
  );
}
function getSetAuthorityInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    owned: { value: input.owned ?? null, isWritable: true },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.owned),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getSetAuthorityInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseSetAuthorityInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      owned: getNextAccount(),
      owner: getNextAccount()
    },
    data: getSetAuthorityInstructionDataDecoder().decode(instruction.data)
  };
}
var SET_TRANSFER_FEE_DISCRIMINATOR = 26;
function getSetTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(SET_TRANSFER_FEE_DISCRIMINATOR);
}
var SET_TRANSFER_FEE_TRANSFER_FEE_DISCRIMINATOR = 5;
function getSetTransferFeeTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(SET_TRANSFER_FEE_TRANSFER_FEE_DISCRIMINATOR);
}
function getSetTransferFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()],
      ["transferFeeBasisPoints", kit.getU16Encoder()],
      ["maximumFee", kit.getU64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: SET_TRANSFER_FEE_DISCRIMINATOR,
      transferFeeDiscriminator: SET_TRANSFER_FEE_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getSetTransferFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()],
    ["transferFeeBasisPoints", kit.getU16Decoder()],
    ["maximumFee", kit.getU64Decoder()]
  ]);
}
function getSetTransferFeeInstructionDataCodec() {
  return kit.combineCodec(
    getSetTransferFeeInstructionDataEncoder(),
    getSetTransferFeeInstructionDataDecoder()
  );
}
function getSetTransferFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    transferFeeConfigAuthority: {
      value: input.transferFeeConfigAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.transferFeeConfigAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getSetTransferFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseSetTransferFeeInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      transferFeeConfigAuthority: getNextAccount()
    },
    data: getSetTransferFeeInstructionDataDecoder().decode(instruction.data)
  };
}
var SYNC_NATIVE_DISCRIMINATOR = 17;
function getSyncNativeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(SYNC_NATIVE_DISCRIMINATOR);
}
function getSyncNativeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: SYNC_NATIVE_DISCRIMINATOR })
  );
}
function getSyncNativeInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getSyncNativeInstructionDataCodec() {
  return kit.combineCodec(
    getSyncNativeInstructionDataEncoder(),
    getSyncNativeInstructionDataDecoder()
  );
}
function getSyncNativeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.account)],
    programAddress,
    data: getSyncNativeInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseSyncNativeInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount()
    },
    data: getSyncNativeInstructionDataDecoder().decode(instruction.data)
  };
}
var THAW_ACCOUNT_DISCRIMINATOR = 11;
function getThawAccountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(THAW_ACCOUNT_DISCRIMINATOR);
}
function getThawAccountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({ ...value, discriminator: THAW_ACCOUNT_DISCRIMINATOR })
  );
}
function getThawAccountInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getThawAccountInstructionDataCodec() {
  return kit.combineCodec(
    getThawAccountInstructionDataEncoder(),
    getThawAccountInstructionDataDecoder()
  );
}
function getThawAccountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    account: { value: input.account ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    owner: { value: input.owner ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.account),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.owner),
      ...remainingAccounts
    ],
    programAddress,
    data: getThawAccountInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseThawAccountInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      account: getNextAccount(),
      mint: getNextAccount(),
      owner: getNextAccount()
    },
    data: getThawAccountInstructionDataDecoder().decode(instruction.data)
  };
}
var TRANSFER_DISCRIMINATOR = 3;
function getTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(TRANSFER_DISCRIMINATOR);
}
function getTransferInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()]
    ]),
    (value) => ({ ...value, discriminator: TRANSFER_DISCRIMINATOR })
  );
}
function getTransferInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()]
  ]);
}
function getTransferInstructionDataCodec() {
  return kit.combineCodec(
    getTransferInstructionDataEncoder(),
    getTransferInstructionDataDecoder()
  );
}
function getTransferInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    destination: { value: input.destination ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getTransferInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseTransferInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      destination: getNextAccount(),
      authority: getNextAccount()
    },
    data: getTransferInstructionDataDecoder().decode(instruction.data)
  };
}
var TRANSFER_CHECKED_DISCRIMINATOR = 12;
function getTransferCheckedDiscriminatorBytes() {
  return kit.getU8Encoder().encode(TRANSFER_CHECKED_DISCRIMINATOR);
}
function getTransferCheckedInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: TRANSFER_CHECKED_DISCRIMINATOR })
  );
}
function getTransferCheckedInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()]
  ]);
}
function getTransferCheckedInstructionDataCodec() {
  return kit.combineCodec(
    getTransferCheckedInstructionDataEncoder(),
    getTransferCheckedInstructionDataDecoder()
  );
}
function getTransferCheckedInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destination: { value: input.destination ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getTransferCheckedInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseTransferCheckedInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      mint: getNextAccount(),
      destination: getNextAccount(),
      authority: getNextAccount()
    },
    data: getTransferCheckedInstructionDataDecoder().decode(instruction.data)
  };
}
var TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR = 26;
function getTransferCheckedWithFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR);
}
var TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR = 1;
function getTransferCheckedWithFeeTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getTransferCheckedWithFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()],
      ["amount", kit.getU64Encoder()],
      ["decimals", kit.getU8Encoder()],
      ["fee", kit.getU64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR,
      transferFeeDiscriminator: TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getTransferCheckedWithFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()],
    ["amount", kit.getU64Decoder()],
    ["decimals", kit.getU8Decoder()],
    ["fee", kit.getU64Decoder()]
  ]);
}
function getTransferCheckedWithFeeInstructionDataCodec() {
  return kit.combineCodec(
    getTransferCheckedWithFeeInstructionDataEncoder(),
    getTransferCheckedWithFeeInstructionDataDecoder()
  );
}
function getTransferCheckedWithFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    source: { value: input.source ?? null, isWritable: true },
    mint: { value: input.mint ?? null, isWritable: false },
    destination: { value: input.destination ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.source),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getTransferCheckedWithFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseTransferCheckedWithFeeInstruction(instruction) {
  if (instruction.accounts.length < 4) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      source: getNextAccount(),
      mint: getNextAccount(),
      destination: getNextAccount(),
      authority: getNextAccount()
    },
    data: getTransferCheckedWithFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UI_AMOUNT_TO_AMOUNT_DISCRIMINATOR = 24;
function getUiAmountToAmountDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UI_AMOUNT_TO_AMOUNT_DISCRIMINATOR);
}
function getUiAmountToAmountInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["uiAmount", kit.getUtf8Encoder()]
    ]),
    (value) => ({ ...value, discriminator: UI_AMOUNT_TO_AMOUNT_DISCRIMINATOR })
  );
}
function getUiAmountToAmountInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["uiAmount", kit.getUtf8Decoder()]
  ]);
}
function getUiAmountToAmountInstructionDataCodec() {
  return kit.combineCodec(
    getUiAmountToAmountInstructionDataEncoder(),
    getUiAmountToAmountInstructionDataDecoder()
  );
}
function getUiAmountToAmountInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [getAccountMeta(accounts.mint)],
    programAddress,
    data: getUiAmountToAmountInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUiAmountToAmountInstruction(instruction) {
  if (instruction.accounts.length < 1) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount()
    },
    data: getUiAmountToAmountInstructionDataDecoder().decode(instruction.data)
  };
}
var UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR = 27;
function getUpdateConfidentialTransferMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR);
}
var UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = 1;
function getUpdateConfidentialTransferMintConfidentialTransferDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
  );
}
function getUpdateConfidentialTransferMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferDiscriminator", kit.getU8Encoder()],
      ["autoApproveNewAccounts", kit.getBooleanEncoder()],
      [
        "auditorElgamalPubkey",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR,
      confidentialTransferDiscriminator: UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR
    })
  );
}
function getUpdateConfidentialTransferMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferDiscriminator", kit.getU8Decoder()],
    ["autoApproveNewAccounts", kit.getBooleanDecoder()],
    [
      "auditorElgamalPubkey",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateConfidentialTransferMintInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateConfidentialTransferMintInstructionDataEncoder(),
    getUpdateConfidentialTransferMintInstructionDataDecoder()
  );
}
function getUpdateConfidentialTransferMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority)
    ],
    programAddress,
    data: getUpdateConfidentialTransferMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateConfidentialTransferMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getUpdateConfidentialTransferMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 28;
function getUpdateDefaultAccountStateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR);
}
var UPDATE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = 1;
function getUpdateDefaultAccountStateDefaultAccountStateDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR
  );
}
function getUpdateDefaultAccountStateInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["defaultAccountStateDiscriminator", kit.getU8Encoder()],
      ["state", getAccountStateEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR,
      defaultAccountStateDiscriminator: UPDATE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR
    })
  );
}
function getUpdateDefaultAccountStateInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["defaultAccountStateDiscriminator", kit.getU8Decoder()],
    ["state", getAccountStateDecoder()]
  ]);
}
function getUpdateDefaultAccountStateInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateDefaultAccountStateInstructionDataEncoder(),
    getUpdateDefaultAccountStateInstructionDataDecoder()
  );
}
function getUpdateDefaultAccountStateInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    freezeAuthority: {
      value: input.freezeAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.freezeAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateDefaultAccountStateInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateDefaultAccountStateInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      freezeAuthority: getNextAccount()
    },
    data: getUpdateDefaultAccountStateInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR = 41;
function getUpdateGroupMemberPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR);
}
var UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = 1;
function getUpdateGroupMemberPointerGroupMemberPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
  );
}
function getUpdateGroupMemberPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["groupMemberPointerDiscriminator", kit.getU8Encoder()],
      [
        "memberAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR,
      groupMemberPointerDiscriminator: UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR
    })
  );
}
function getUpdateGroupMemberPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["groupMemberPointerDiscriminator", kit.getU8Decoder()],
    [
      "memberAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateGroupMemberPointerInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateGroupMemberPointerInstructionDataEncoder(),
    getUpdateGroupMemberPointerInstructionDataDecoder()
  );
}
function getUpdateGroupMemberPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    groupMemberPointerAuthority: {
      value: input.groupMemberPointerAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.groupMemberPointerAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateGroupMemberPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateGroupMemberPointerInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      groupMemberPointerAuthority: getNextAccount()
    },
    data: getUpdateGroupMemberPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_GROUP_POINTER_DISCRIMINATOR = 40;
function getUpdateGroupPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_GROUP_POINTER_DISCRIMINATOR);
}
var UPDATE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR = 1;
function getUpdateGroupPointerGroupPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR
  );
}
function getUpdateGroupPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["groupPointerDiscriminator", kit.getU8Encoder()],
      [
        "groupAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_GROUP_POINTER_DISCRIMINATOR,
      groupPointerDiscriminator: UPDATE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR
    })
  );
}
function getUpdateGroupPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["groupPointerDiscriminator", kit.getU8Decoder()],
    [
      "groupAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateGroupPointerInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateGroupPointerInstructionDataEncoder(),
    getUpdateGroupPointerInstructionDataDecoder()
  );
}
function getUpdateGroupPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    groupPointerAuthority: {
      value: input.groupPointerAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.groupPointerAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateGroupPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateGroupPointerInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      groupPointerAuthority: getNextAccount()
    },
    data: getUpdateGroupPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_METADATA_POINTER_DISCRIMINATOR = 39;
function getUpdateMetadataPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_METADATA_POINTER_DISCRIMINATOR);
}
var UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR = 1;
function getUpdateMetadataPointerMetadataPointerDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR
  );
}
function getUpdateMetadataPointerInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["metadataPointerDiscriminator", kit.getU8Encoder()],
      [
        "metadataAddress",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_METADATA_POINTER_DISCRIMINATOR,
      metadataPointerDiscriminator: UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR
    })
  );
}
function getUpdateMetadataPointerInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["metadataPointerDiscriminator", kit.getU8Decoder()],
    [
      "metadataAddress",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateMetadataPointerInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateMetadataPointerInstructionDataEncoder(),
    getUpdateMetadataPointerInstructionDataDecoder()
  );
}
function getUpdateMetadataPointerInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    metadataPointerAuthority: {
      value: input.metadataPointerAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.metadataPointerAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateMetadataPointerInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateMetadataPointerInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      metadataPointerAuthority: getNextAccount()
    },
    data: getUpdateMetadataPointerInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR = 43;
function getUpdateMultiplierScaledUiMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR);
}
var UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = 1;
function getUpdateMultiplierScaledUiMintScaledUiAmountMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR
  );
}
function getUpdateMultiplierScaledUiMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["scaledUiAmountMintDiscriminator", kit.getU8Encoder()],
      ["multiplier", kit.getF64Encoder()],
      ["effectiveTimestamp", kit.getI64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR,
      scaledUiAmountMintDiscriminator: UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR
    })
  );
}
function getUpdateMultiplierScaledUiMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["scaledUiAmountMintDiscriminator", kit.getU8Decoder()],
    ["multiplier", kit.getF64Decoder()],
    ["effectiveTimestamp", kit.getI64Decoder()]
  ]);
}
function getUpdateMultiplierScaledUiMintInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateMultiplierScaledUiMintInstructionDataEncoder(),
    getUpdateMultiplierScaledUiMintInstructionDataDecoder()
  );
}
function getUpdateMultiplierScaledUiMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateMultiplierScaledUiMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateMultiplierScaledUiMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getUpdateMultiplierScaledUiMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR = 33;
function getUpdateRateInterestBearingMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR);
}
var UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR = 1;
function getUpdateRateInterestBearingMintInterestBearingMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR
  );
}
function getUpdateRateInterestBearingMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["interestBearingMintDiscriminator", kit.getU8Encoder()],
      ["rate", kit.getI16Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR,
      interestBearingMintDiscriminator: UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR
    })
  );
}
function getUpdateRateInterestBearingMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["interestBearingMintDiscriminator", kit.getU8Decoder()],
    ["rate", kit.getI16Decoder()]
  ]);
}
function getUpdateRateInterestBearingMintInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateRateInterestBearingMintInstructionDataEncoder(),
    getUpdateRateInterestBearingMintInstructionDataDecoder()
  );
}
function getUpdateRateInterestBearingMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    rateAuthority: { value: input.rateAuthority ?? null, isWritable: true }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.rateAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateRateInterestBearingMintInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateRateInterestBearingMintInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      rateAuthority: getNextAccount()
    },
    data: getUpdateRateInterestBearingMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_TOKEN_GROUP_MAX_SIZE_DISCRIMINATOR = new Uint8Array([
  108,
  37,
  171,
  143,
  248,
  30,
  18,
  110
]);
function getUpdateTokenGroupMaxSizeDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(UPDATE_TOKEN_GROUP_MAX_SIZE_DISCRIMINATOR);
}
function getUpdateTokenGroupMaxSizeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      ["maxSize", kit.getU64Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_TOKEN_GROUP_MAX_SIZE_DISCRIMINATOR
    })
  );
}
function getUpdateTokenGroupMaxSizeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    ["maxSize", kit.getU64Decoder()]
  ]);
}
function getUpdateTokenGroupMaxSizeInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateTokenGroupMaxSizeInstructionDataEncoder(),
    getUpdateTokenGroupMaxSizeInstructionDataDecoder()
  );
}
function getUpdateTokenGroupMaxSizeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    group: { value: input.group ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.group),
      getAccountMeta(accounts.updateAuthority)
    ],
    programAddress,
    data: getUpdateTokenGroupMaxSizeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateTokenGroupMaxSizeInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      group: getNextAccount(),
      updateAuthority: getNextAccount()
    },
    data: getUpdateTokenGroupMaxSizeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_TOKEN_GROUP_UPDATE_AUTHORITY_DISCRIMINATOR = new Uint8Array(
  [161, 105, 88, 1, 237, 221, 216, 203]
);
function getUpdateTokenGroupUpdateAuthorityDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(
    UPDATE_TOKEN_GROUP_UPDATE_AUTHORITY_DISCRIMINATOR
  );
}
function getUpdateTokenGroupUpdateAuthorityInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      [
        "newUpdateAuthority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_TOKEN_GROUP_UPDATE_AUTHORITY_DISCRIMINATOR
    })
  );
}
function getUpdateTokenGroupUpdateAuthorityInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    [
      "newUpdateAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateTokenGroupUpdateAuthorityInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateTokenGroupUpdateAuthorityInstructionDataEncoder(),
    getUpdateTokenGroupUpdateAuthorityInstructionDataDecoder()
  );
}
function getUpdateTokenGroupUpdateAuthorityInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    group: { value: input.group ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.group),
      getAccountMeta(accounts.updateAuthority)
    ],
    programAddress,
    data: getUpdateTokenGroupUpdateAuthorityInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateTokenGroupUpdateAuthorityInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      group: getNextAccount(),
      updateAuthority: getNextAccount()
    },
    data: getUpdateTokenGroupUpdateAuthorityInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_TOKEN_METADATA_FIELD_DISCRIMINATOR = new Uint8Array([
  221,
  233,
  49,
  45,
  181,
  202,
  220,
  200
]);
function getUpdateTokenMetadataFieldDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(UPDATE_TOKEN_METADATA_FIELD_DISCRIMINATOR);
}
function getUpdateTokenMetadataFieldInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      ["field", getTokenMetadataFieldEncoder()],
      ["value", kit.addEncoderSizePrefix(kit.getUtf8Encoder(), kit.getU32Encoder())]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_TOKEN_METADATA_FIELD_DISCRIMINATOR
    })
  );
}
function getUpdateTokenMetadataFieldInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    ["field", getTokenMetadataFieldDecoder()],
    ["value", kit.addDecoderSizePrefix(kit.getUtf8Decoder(), kit.getU32Decoder())]
  ]);
}
function getUpdateTokenMetadataFieldInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateTokenMetadataFieldInstructionDataEncoder(),
    getUpdateTokenMetadataFieldInstructionDataDecoder()
  );
}
function getUpdateTokenMetadataFieldInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority)
    ],
    programAddress,
    data: getUpdateTokenMetadataFieldInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateTokenMetadataFieldInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount()
    },
    data: getUpdateTokenMetadataFieldInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_TOKEN_METADATA_UPDATE_AUTHORITY_DISCRIMINATOR = new Uint8Array([215, 228, 166, 228, 84, 100, 86, 123]);
function getUpdateTokenMetadataUpdateAuthorityDiscriminatorBytes() {
  return kit.getBytesEncoder().encode(
    UPDATE_TOKEN_METADATA_UPDATE_AUTHORITY_DISCRIMINATOR
  );
}
function getUpdateTokenMetadataUpdateAuthorityInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getBytesEncoder()],
      [
        "newUpdateAuthority",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_TOKEN_METADATA_UPDATE_AUTHORITY_DISCRIMINATOR
    })
  );
}
function getUpdateTokenMetadataUpdateAuthorityInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getBytesDecoder()],
    [
      "newUpdateAuthority",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateTokenMetadataUpdateAuthorityInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateTokenMetadataUpdateAuthorityInstructionDataEncoder(),
    getUpdateTokenMetadataUpdateAuthorityInstructionDataDecoder()
  );
}
function getUpdateTokenMetadataUpdateAuthorityInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    metadata: { value: input.metadata ?? null, isWritable: true },
    updateAuthority: {
      value: input.updateAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.metadata),
      getAccountMeta(accounts.updateAuthority)
    ],
    programAddress,
    data: getUpdateTokenMetadataUpdateAuthorityInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateTokenMetadataUpdateAuthorityInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      metadata: getNextAccount(),
      updateAuthority: getNextAccount()
    },
    data: getUpdateTokenMetadataUpdateAuthorityInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var UPDATE_TRANSFER_HOOK_DISCRIMINATOR = 36;
function getUpdateTransferHookDiscriminatorBytes() {
  return kit.getU8Encoder().encode(UPDATE_TRANSFER_HOOK_DISCRIMINATOR);
}
var UPDATE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR = 1;
function getUpdateTransferHookTransferHookDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    UPDATE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR
  );
}
function getUpdateTransferHookInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferHookDiscriminator", kit.getU8Encoder()],
      [
        "programId",
        kit.getOptionEncoder(kit.getAddressEncoder(), {
          prefix: null,
          noneValue: "zeroes"
        })
      ]
    ]),
    (value) => ({
      ...value,
      discriminator: UPDATE_TRANSFER_HOOK_DISCRIMINATOR,
      transferHookDiscriminator: UPDATE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR
    })
  );
}
function getUpdateTransferHookInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferHookDiscriminator", kit.getU8Decoder()],
    [
      "programId",
      kit.getOptionDecoder(kit.getAddressDecoder(), {
        prefix: null,
        noneValue: "zeroes"
      })
    ]
  ]);
}
function getUpdateTransferHookInstructionDataCodec() {
  return kit.combineCodec(
    getUpdateTransferHookInstructionDataEncoder(),
    getUpdateTransferHookInstructionDataDecoder()
  );
}
function getUpdateTransferHookInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getUpdateTransferHookInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseUpdateTransferHookInstruction(instruction) {
  if (instruction.accounts.length < 2) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      authority: getNextAccount()
    },
    data: getUpdateTransferHookInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var WITHDRAW_EXCESS_LAMPORTS_DISCRIMINATOR = 38;
function getWithdrawExcessLamportsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(WITHDRAW_EXCESS_LAMPORTS_DISCRIMINATOR);
}
function getWithdrawExcessLamportsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([["discriminator", kit.getU8Encoder()]]),
    (value) => ({
      ...value,
      discriminator: WITHDRAW_EXCESS_LAMPORTS_DISCRIMINATOR
    })
  );
}
function getWithdrawExcessLamportsInstructionDataDecoder() {
  return kit.getStructDecoder([["discriminator", kit.getU8Decoder()]]);
}
function getWithdrawExcessLamportsInstructionDataCodec() {
  return kit.combineCodec(
    getWithdrawExcessLamportsInstructionDataEncoder(),
    getWithdrawExcessLamportsInstructionDataDecoder()
  );
}
function getWithdrawExcessLamportsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    sourceAccount: { value: input.sourceAccount ?? null, isWritable: true },
    destinationAccount: {
      value: input.destinationAccount ?? null,
      isWritable: true
    },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sourceAccount),
      getAccountMeta(accounts.destinationAccount),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getWithdrawExcessLamportsInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseWithdrawExcessLamportsInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sourceAccount: getNextAccount(),
      destinationAccount: getNextAccount(),
      authority: getNextAccount()
    },
    data: getWithdrawExcessLamportsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_DISCRIMINATOR = 26;
function getWithdrawWithheldTokensFromAccountsDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_DISCRIMINATOR
  );
}
var WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_TRANSFER_FEE_DISCRIMINATOR = 3;
function getWithdrawWithheldTokensFromAccountsTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getWithdrawWithheldTokensFromAccountsInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()],
      ["numTokenAccounts", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_DISCRIMINATOR,
      transferFeeDiscriminator: WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getWithdrawWithheldTokensFromAccountsInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()],
    ["numTokenAccounts", kit.getU8Decoder()]
  ]);
}
function getWithdrawWithheldTokensFromAccountsInstructionDataCodec() {
  return kit.combineCodec(
    getWithdrawWithheldTokensFromAccountsInstructionDataEncoder(),
    getWithdrawWithheldTokensFromAccountsInstructionDataDecoder()
  );
}
function getWithdrawWithheldTokensFromAccountsInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false },
    feeReceiver: { value: input.feeReceiver ?? null, isWritable: true },
    withdrawWithheldAuthority: {
      value: input.withdrawWithheldAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = [
    ...(args.multiSigners ?? []).map((signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })),
    ...args.sources.map((address) => ({ address, role: kit.AccountRole.WRITABLE }))
  ];
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.feeReceiver),
      getAccountMeta(accounts.withdrawWithheldAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getWithdrawWithheldTokensFromAccountsInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseWithdrawWithheldTokensFromAccountsInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      feeReceiver: getNextAccount(),
      withdrawWithheldAuthority: getNextAccount()
    },
    data: getWithdrawWithheldTokensFromAccountsInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 37;
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
var WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 2;
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()],
      ["numTokenAccounts", kit.getU8Encoder()],
      ["proofInstructionOffset", kit.getI8Encoder()],
      ["newDecryptableAvailableBalance", getDecryptableBalanceEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()],
    ["numTokenAccounts", kit.getU8Decoder()],
    ["proofInstructionOffset", kit.getI8Decoder()],
    ["newDecryptableAvailableBalance", getDecryptableBalanceDecoder()]
  ]);
}
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataCodec() {
  return kit.combineCodec(
    getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder(),
    getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder()
  );
}
function getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: false },
    destination: { value: input.destination ?? null, isWritable: true },
    instructionsSysvarOrContextState: {
      value: input.instructionsSysvarOrContextState ?? null,
      isWritable: false
    },
    record: { value: input.record ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.instructionsSysvarOrContextState),
      getAccountMeta(accounts.record),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction(instruction) {
  if (instruction.accounts.length < 5) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      destination: getNextAccount(),
      instructionsSysvarOrContextState: getNextAccount(),
      record: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var WITHDRAW_WITHHELD_TOKENS_FROM_MINT_DISCRIMINATOR = 26;
function getWithdrawWithheldTokensFromMintDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_MINT_DISCRIMINATOR
  );
}
var WITHDRAW_WITHHELD_TOKENS_FROM_MINT_TRANSFER_FEE_DISCRIMINATOR = 2;
function getWithdrawWithheldTokensFromMintTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_MINT_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getWithdrawWithheldTokensFromMintInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["transferFeeDiscriminator", kit.getU8Encoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: WITHDRAW_WITHHELD_TOKENS_FROM_MINT_DISCRIMINATOR,
      transferFeeDiscriminator: WITHDRAW_WITHHELD_TOKENS_FROM_MINT_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getWithdrawWithheldTokensFromMintInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["transferFeeDiscriminator", kit.getU8Decoder()]
  ]);
}
function getWithdrawWithheldTokensFromMintInstructionDataCodec() {
  return kit.combineCodec(
    getWithdrawWithheldTokensFromMintInstructionDataEncoder(),
    getWithdrawWithheldTokensFromMintInstructionDataDecoder()
  );
}
function getWithdrawWithheldTokensFromMintInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    feeReceiver: { value: input.feeReceiver ?? null, isWritable: true },
    withdrawWithheldAuthority: {
      value: input.withdrawWithheldAuthority ?? null,
      isWritable: false
    }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.feeReceiver),
      getAccountMeta(accounts.withdrawWithheldAuthority),
      ...remainingAccounts
    ],
    programAddress,
    data: getWithdrawWithheldTokensFromMintInstructionDataEncoder().encode({})
  };
  return instruction;
}
function parseWithdrawWithheldTokensFromMintInstruction(instruction) {
  if (instruction.accounts.length < 3) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      feeReceiver: getNextAccount(),
      withdrawWithheldAuthority: getNextAccount()
    },
    data: getWithdrawWithheldTokensFromMintInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 37;
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
var WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = 1;
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes() {
  return kit.getU8Encoder().encode(
    WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
  );
}
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataEncoder() {
  return kit.transformEncoder(
    kit.getStructEncoder([
      ["discriminator", kit.getU8Encoder()],
      ["confidentialTransferFeeDiscriminator", kit.getU8Encoder()],
      ["proofInstructionOffset", kit.getI8Encoder()],
      ["newDecryptableAvailableBalance", getDecryptableBalanceEncoder()]
    ]),
    (value) => ({
      ...value,
      discriminator: WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR,
      confidentialTransferFeeDiscriminator: WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR
    })
  );
}
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataDecoder() {
  return kit.getStructDecoder([
    ["discriminator", kit.getU8Decoder()],
    ["confidentialTransferFeeDiscriminator", kit.getU8Decoder()],
    ["proofInstructionOffset", kit.getI8Decoder()],
    ["newDecryptableAvailableBalance", getDecryptableBalanceDecoder()]
  ]);
}
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataCodec() {
  return kit.combineCodec(
    getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataEncoder(),
    getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataDecoder()
  );
}
function getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction(input, config) {
  const programAddress = config?.programAddress ?? TOKEN_2022_PROGRAM_ADDRESS;
  const originalAccounts = {
    mint: { value: input.mint ?? null, isWritable: true },
    destination: { value: input.destination ?? null, isWritable: true },
    instructionsSysvarOrContextState: {
      value: input.instructionsSysvarOrContextState ?? null,
      isWritable: false
    },
    record: { value: input.record ?? null, isWritable: false },
    authority: { value: input.authority ?? null, isWritable: false }
  };
  const accounts = originalAccounts;
  const args = { ...input };
  const remainingAccounts = (args.multiSigners ?? []).map(
    (signer) => ({
      address: signer.address,
      role: kit.AccountRole.READONLY_SIGNER,
      signer
    })
  );
  const getAccountMeta = getAccountMetaFactory(programAddress);
  const instruction = {
    accounts: [
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.destination),
      getAccountMeta(accounts.instructionsSysvarOrContextState),
      getAccountMeta(accounts.record),
      getAccountMeta(accounts.authority),
      ...remainingAccounts
    ],
    programAddress,
    data: getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataEncoder().encode(
      args
    )
  };
  return instruction;
}
function parseWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction(instruction) {
  if (instruction.accounts.length < 5) {
    throw new Error("Not enough accounts");
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts[accountIndex];
    accountIndex += 1;
    return accountMeta;
  };
  const getNextOptionalAccount = () => {
    const accountMeta = getNextAccount();
    return accountMeta.address === TOKEN_2022_PROGRAM_ADDRESS ? void 0 : accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      mint: getNextAccount(),
      destination: getNextAccount(),
      instructionsSysvarOrContextState: getNextAccount(),
      record: getNextOptionalAccount(),
      authority: getNextAccount()
    },
    data: getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataDecoder().decode(
      instruction.data
    )
  };
}
var ONE_IN_BASIS_POINTS = 1e4;
var SECONDS_PER_YEAR = 60 * 60 * 24 * 365.24;
function calculateExponentForTimesAndRate(t1, t2, r) {
  const timespan = t2 - t1;
  if (timespan < 0) {
    throw new Error("Invalid timespan: end time before start time");
  }
  const numerator = r * timespan;
  const exponent = numerator / (SECONDS_PER_YEAR * ONE_IN_BASIS_POINTS);
  return Math.exp(exponent);
}
function calculateTotalScale({
  currentTimestamp,
  lastUpdateTimestamp,
  initializationTimestamp,
  preUpdateAverageRate,
  currentRate
}) {
  const preUpdateExp = calculateExponentForTimesAndRate(
    initializationTimestamp,
    lastUpdateTimestamp,
    preUpdateAverageRate
  );
  const postUpdateExp = calculateExponentForTimesAndRate(
    lastUpdateTimestamp,
    currentTimestamp,
    currentRate
  );
  return preUpdateExp * postUpdateExp;
}
function getDecimalFactor(decimals) {
  return Math.pow(10, decimals);
}
async function getSysvarClockTimestamp(rpc) {
  const info = await sysvars.fetchSysvarClock(rpc);
  if (!info) {
    throw new Error("Failed to fetch sysvar clock");
  }
  return info.unixTimestamp;
}
function amountToUiAmountForInterestBearingMintWithoutSimulation(amount, decimals, currentTimestamp, lastUpdateTimestamp, initializationTimestamp, preUpdateAverageRate, currentRate) {
  const totalScale = calculateTotalScale({
    currentTimestamp,
    lastUpdateTimestamp,
    initializationTimestamp,
    preUpdateAverageRate,
    currentRate
  });
  const scaledAmount = Number(amount) * totalScale;
  const decimalFactor = getDecimalFactor(decimals);
  return (Math.trunc(scaledAmount) / decimalFactor).toString();
}
function uiAmountToAmountForInterestBearingMintWithoutSimulation(uiAmount, decimals, currentTimestamp, lastUpdateTimestamp, initializationTimestamp, preUpdateAverageRate, currentRate) {
  const uiAmountNumber = parseFloat(uiAmount);
  const decimalsFactor = getDecimalFactor(decimals);
  const uiAmountScaled = uiAmountNumber * decimalsFactor;
  const totalScale = calculateTotalScale({
    currentTimestamp,
    lastUpdateTimestamp,
    initializationTimestamp,
    preUpdateAverageRate,
    currentRate
  });
  const originalPrincipal = uiAmountScaled / totalScale;
  return BigInt(Math.trunc(originalPrincipal));
}
function amountToUiAmountForScaledUiAmountMintWithoutSimulation(amount, decimals, multiplier) {
  const scaledAmount = Number(amount) * multiplier;
  const decimalFactor = getDecimalFactor(decimals);
  return (Math.trunc(scaledAmount) / decimalFactor).toString();
}
function uiAmountToAmountForScaledUiAmountMintWithoutSimulation(uiAmount, decimals, multiplier) {
  const uiAmountNumber = parseFloat(uiAmount);
  const decimalsFactor = getDecimalFactor(decimals);
  const uiAmountScaled = uiAmountNumber * decimalsFactor;
  const rawAmount = uiAmountScaled / multiplier;
  return BigInt(Math.trunc(rawAmount));
}
async function amountToUiAmountForMintWithoutSimulation(rpc, mint, amount) {
  const accountInfo = await fetchMint(rpc, mint);
  const extensions = kit.unwrapOption(accountInfo.data.extensions);
  const interestBearingMintConfigState = extensions?.find(
    (ext) => ext.__kind === "InterestBearingConfig"
  );
  const scaledUiAmountConfig = extensions?.find(
    (ext) => ext.__kind === "ScaledUiAmountConfig"
  );
  if (!interestBearingMintConfigState && !scaledUiAmountConfig) {
    const amountNumber = Number(amount);
    const decimalsFactor = getDecimalFactor(accountInfo.data.decimals);
    return (amountNumber / decimalsFactor).toString();
  }
  const timestamp = await getSysvarClockTimestamp(rpc);
  if (interestBearingMintConfigState) {
    return amountToUiAmountForInterestBearingMintWithoutSimulation(
      amount,
      accountInfo.data.decimals,
      Number(timestamp),
      Number(interestBearingMintConfigState.lastUpdateTimestamp),
      Number(interestBearingMintConfigState.initializationTimestamp),
      interestBearingMintConfigState.preUpdateAverageRate,
      interestBearingMintConfigState.currentRate
    );
  }
  if (scaledUiAmountConfig) {
    let multiplier = scaledUiAmountConfig.multiplier;
    if (timestamp >= scaledUiAmountConfig.newMultiplierEffectiveTimestamp) {
      multiplier = scaledUiAmountConfig.newMultiplier;
    }
    return amountToUiAmountForScaledUiAmountMintWithoutSimulation(
      amount,
      accountInfo.data.decimals,
      multiplier
    );
  }
  throw new Error("Unknown mint extension type");
}
async function uiAmountToAmountForMintWithoutSimulation(rpc, mint, uiAmount) {
  const accountInfo = await fetchMint(rpc, mint);
  const extensions = kit.unwrapOption(accountInfo.data.extensions);
  const interestBearingMintConfigState = extensions?.find(
    (ext) => ext.__kind === "InterestBearingConfig"
  );
  const scaledUiAmountConfig = extensions?.find(
    (ext) => ext.__kind === "ScaledUiAmountConfig"
  );
  if (!interestBearingMintConfigState && !scaledUiAmountConfig) {
    const uiAmountScaled = parseFloat(uiAmount) * getDecimalFactor(accountInfo.data.decimals);
    return BigInt(Math.trunc(uiAmountScaled));
  }
  const timestamp = await getSysvarClockTimestamp(rpc);
  if (interestBearingMintConfigState) {
    return uiAmountToAmountForInterestBearingMintWithoutSimulation(
      uiAmount,
      accountInfo.data.decimals,
      Number(timestamp),
      Number(interestBearingMintConfigState.lastUpdateTimestamp),
      Number(interestBearingMintConfigState.initializationTimestamp),
      interestBearingMintConfigState.preUpdateAverageRate,
      interestBearingMintConfigState.currentRate
    );
  }
  if (scaledUiAmountConfig) {
    let multiplier = scaledUiAmountConfig.multiplier;
    if (timestamp >= scaledUiAmountConfig.newMultiplierEffectiveTimestamp) {
      multiplier = scaledUiAmountConfig.newMultiplier;
    }
    return uiAmountToAmountForScaledUiAmountMintWithoutSimulation(
      uiAmount,
      accountInfo.data.decimals,
      multiplier
    );
  }
  throw new Error("Unknown mint extension type");
}
function getPreInitializeInstructionsForMintExtensions(mint, extensions) {
  return extensions.flatMap((extension2) => {
    switch (extension2.__kind) {
      case "ConfidentialTransferMint":
        return [
          getInitializeConfidentialTransferMintInstruction({
            mint,
            ...extension2
          })
        ];
      case "DefaultAccountState":
        return [
          getInitializeDefaultAccountStateInstruction({
            mint,
            state: extension2.state
          })
        ];
      case "TransferFeeConfig":
        return [
          getInitializeTransferFeeConfigInstruction({
            mint,
            transferFeeConfigAuthority: extension2.transferFeeConfigAuthority,
            withdrawWithheldAuthority: extension2.withdrawWithheldAuthority,
            transferFeeBasisPoints: extension2.newerTransferFee.transferFeeBasisPoints,
            maximumFee: extension2.newerTransferFee.maximumFee
          })
        ];
      case "MetadataPointer":
        return [
          getInitializeMetadataPointerInstruction({
            mint,
            authority: extension2.authority,
            metadataAddress: extension2.metadataAddress
          })
        ];
      case "InterestBearingConfig":
        return [
          getInitializeInterestBearingMintInstruction({
            mint,
            rateAuthority: extension2.rateAuthority,
            rate: extension2.currentRate
          })
        ];
      case "ScaledUiAmountConfig":
        return [
          getInitializeScaledUiAmountMintInstruction({
            mint,
            authority: extension2.authority,
            multiplier: extension2.multiplier
          })
        ];
      case "PausableConfig":
        return [
          getInitializePausableConfigInstruction({
            mint,
            authority: extension2.authority
          })
        ];
      case "GroupPointer":
        return [
          getInitializeGroupPointerInstruction({
            mint,
            authority: extension2.authority,
            groupAddress: extension2.groupAddress
          })
        ];
      case "GroupMemberPointer":
        return [
          getInitializeGroupMemberPointerInstruction({
            mint,
            authority: extension2.authority,
            memberAddress: extension2.memberAddress
          })
        ];
      case "NonTransferable":
        return getInitializeNonTransferableMintInstruction({ mint });
      case "TransferHook":
        return [
          getInitializeTransferHookInstruction({
            mint,
            authority: extension2.authority,
            programId: extension2.programId
          })
        ];
      case "PermanentDelegate":
        return getInitializePermanentDelegateInstruction({
          mint,
          delegate: extension2.delegate
        });
      case "ConfidentialTransferFee":
        return [
          getInitializeConfidentialTransferFeeInstruction({
            mint,
            authority: extension2.authority,
            withdrawWithheldAuthorityElGamalPubkey: extension2.elgamalPubkey
          })
        ];
      case "MintCloseAuthority":
        return getInitializeMintCloseAuthorityInstruction({
          closeAuthority: extension2.closeAuthority,
          mint
        });
      default:
        return [];
    }
  });
}
function getPostInitializeInstructionsForMintExtensions(mint, authority, extensions) {
  return extensions.flatMap((extension2) => {
    switch (extension2.__kind) {
      case "TokenMetadata":
        const tokenMetadataUpdateAuthority = kit.isOption(extension2.updateAuthority) ? extension2.updateAuthority : kit.wrapNullable(extension2.updateAuthority);
        if (kit.isNone(tokenMetadataUpdateAuthority)) {
          return [];
        }
        return [
          getInitializeTokenMetadataInstruction({
            metadata: mint,
            updateAuthority: tokenMetadataUpdateAuthority.value,
            mint,
            mintAuthority: authority,
            name: extension2.name,
            symbol: extension2.symbol,
            uri: extension2.uri
          })
        ];
      case "TokenGroup":
        return [
          getInitializeTokenGroupInstruction({
            group: mint,
            updateAuthority: kit.isOption(extension2.updateAuthority) ? extension2.updateAuthority : kit.wrapNullable(extension2.updateAuthority),
            mint,
            mintAuthority: authority,
            maxSize: extension2.maxSize
          })
        ];
      default:
        return [];
    }
  });
}
function getPostInitializeInstructionsForTokenExtensions(token, owner, extensions, multiSigners) {
  return extensions.flatMap((extension2) => {
    switch (extension2.__kind) {
      case "MemoTransfer":
        return [
          extension2.requireIncomingTransferMemos ? getEnableMemoTransfersInstruction({ owner, token, multiSigners }) : getDisableMemoTransfersInstruction({
            owner,
            token,
            multiSigners
          })
        ];
      case "CpiGuard":
        return [
          extension2.lockCpi ? getEnableCpiGuardInstruction({ owner, token, multiSigners }) : getDisableCpiGuardInstruction({
            owner,
            token,
            multiSigners
          })
        ];
      default:
        return [];
    }
  });
}
var TOKEN_BASE_SIZE = 165;
function getTokenSize(extensions) {
  if (extensions == null) return TOKEN_BASE_SIZE;
  const tvlEncoder = kit.getHiddenPrefixEncoder(
    kit.getArrayEncoder(getExtensionEncoder(), { size: "remainder" }),
    [kit.getConstantEncoder(kit.getU8Encoder().encode(2))]
  );
  return TOKEN_BASE_SIZE + tvlEncoder.encode(extensions).length;
}
var MINT_BASE_SIZE = 82;
function getMintSize(extensions) {
  if (extensions == null) return MINT_BASE_SIZE;
  const tvlEncoder = kit.getHiddenPrefixEncoder(
    kit.getArrayEncoder(getExtensionEncoder(), { size: "remainder" }),
    [kit.getConstantEncoder(kit.padLeftEncoder(kit.getU8Encoder(), 83).encode(1))]
  );
  return MINT_BASE_SIZE + tvlEncoder.encode(extensions).length;
}

exports.AMOUNT_TO_UI_AMOUNT_DISCRIMINATOR = AMOUNT_TO_UI_AMOUNT_DISCRIMINATOR;
exports.APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = APPLY_CONFIDENTIAL_PENDING_BALANCE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR = APPLY_CONFIDENTIAL_PENDING_BALANCE_DISCRIMINATOR;
exports.APPROVE_CHECKED_DISCRIMINATOR = APPROVE_CHECKED_DISCRIMINATOR;
exports.APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = APPROVE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR;
exports.APPROVE_DISCRIMINATOR = APPROVE_DISCRIMINATOR;
exports.ASSOCIATED_TOKEN_ERROR__INVALID_OWNER = ASSOCIATED_TOKEN_ERROR__INVALID_OWNER;
exports.ASSOCIATED_TOKEN_PROGRAM_ADDRESS = ASSOCIATED_TOKEN_PROGRAM_ADDRESS;
exports.AccountState = AccountState;
exports.AssociatedTokenInstruction = AssociatedTokenInstruction;
exports.AuthorityType = AuthorityType;
exports.BURN_CHECKED_DISCRIMINATOR = BURN_CHECKED_DISCRIMINATOR;
exports.BURN_DISCRIMINATOR = BURN_DISCRIMINATOR;
exports.CLOSE_ACCOUNT_DISCRIMINATOR = CLOSE_ACCOUNT_DISCRIMINATOR;
exports.CONFIDENTIAL_DEPOSIT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIDENTIAL_DEPOSIT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIDENTIAL_DEPOSIT_DISCRIMINATOR = CONFIDENTIAL_DEPOSIT_DISCRIMINATOR;
exports.CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIDENTIAL_TRANSFER_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIDENTIAL_TRANSFER_WITH_FEE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIDENTIAL_TRANSFER_WITH_FEE_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIDENTIAL_TRANSFER_WITH_FEE_DISCRIMINATOR = CONFIDENTIAL_TRANSFER_WITH_FEE_DISCRIMINATOR;
exports.CONFIDENTIAL_WITHDRAW_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIDENTIAL_WITHDRAW_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIDENTIAL_WITHDRAW_DISCRIMINATOR = CONFIDENTIAL_WITHDRAW_DISCRIMINATOR;
exports.CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = CONFIGURE_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR;
exports.CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR = CREATE_ASSOCIATED_TOKEN_DISCRIMINATOR;
exports.CREATE_ASSOCIATED_TOKEN_IDEMPOTENT_DISCRIMINATOR = CREATE_ASSOCIATED_TOKEN_IDEMPOTENT_DISCRIMINATOR;
exports.CREATE_NATIVE_MINT_DISCRIMINATOR = CREATE_NATIVE_MINT_DISCRIMINATOR;
exports.DISABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = DISABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.DISABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR = DISABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR;
exports.DISABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR = DISABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR;
exports.DISABLE_CPI_GUARD_DISCRIMINATOR = DISABLE_CPI_GUARD_DISCRIMINATOR;
exports.DISABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = DISABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.DISABLE_HARVEST_TO_MINT_DISCRIMINATOR = DISABLE_HARVEST_TO_MINT_DISCRIMINATOR;
exports.DISABLE_MEMO_TRANSFERS_DISCRIMINATOR = DISABLE_MEMO_TRANSFERS_DISCRIMINATOR;
exports.DISABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR = DISABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR;
exports.DISABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = DISABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.DISABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR = DISABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR;
exports.EMIT_TOKEN_METADATA_DISCRIMINATOR = EMIT_TOKEN_METADATA_DISCRIMINATOR;
exports.EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR = EMPTY_CONFIDENTIAL_TRANSFER_ACCOUNT_DISCRIMINATOR;
exports.ENABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = ENABLE_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.ENABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR = ENABLE_CONFIDENTIAL_CREDITS_DISCRIMINATOR;
exports.ENABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR = ENABLE_CPI_GUARD_CPI_GUARD_DISCRIMINATOR;
exports.ENABLE_CPI_GUARD_DISCRIMINATOR = ENABLE_CPI_GUARD_DISCRIMINATOR;
exports.ENABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = ENABLE_HARVEST_TO_MINT_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.ENABLE_HARVEST_TO_MINT_DISCRIMINATOR = ENABLE_HARVEST_TO_MINT_DISCRIMINATOR;
exports.ENABLE_MEMO_TRANSFERS_DISCRIMINATOR = ENABLE_MEMO_TRANSFERS_DISCRIMINATOR;
exports.ENABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR = ENABLE_MEMO_TRANSFERS_MEMO_TRANSFERS_DISCRIMINATOR;
exports.ENABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = ENABLE_NON_CONFIDENTIAL_CREDITS_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.ENABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR = ENABLE_NON_CONFIDENTIAL_CREDITS_DISCRIMINATOR;
exports.ExtensionType = ExtensionType;
exports.FREEZE_ACCOUNT_DISCRIMINATOR = FREEZE_ACCOUNT_DISCRIMINATOR;
exports.GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR = GET_ACCOUNT_DATA_SIZE_DISCRIMINATOR;
exports.HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR = HARVEST_WITHHELD_TOKENS_TO_MINT_DISCRIMINATOR;
exports.HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = HARVEST_WITHHELD_TOKENS_TO_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR = HARVEST_WITHHELD_TOKENS_TO_MINT_TRANSFER_FEE_DISCRIMINATOR;
exports.INITIALIZE_ACCOUNT2_DISCRIMINATOR = INITIALIZE_ACCOUNT2_DISCRIMINATOR;
exports.INITIALIZE_ACCOUNT3_DISCRIMINATOR = INITIALIZE_ACCOUNT3_DISCRIMINATOR;
exports.INITIALIZE_ACCOUNT_DISCRIMINATOR = INITIALIZE_ACCOUNT_DISCRIMINATOR;
exports.INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = INITIALIZE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR = INITIALIZE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR;
exports.INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = INITIALIZE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR;
exports.INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = INITIALIZE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR;
exports.INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR = INITIALIZE_GROUP_MEMBER_POINTER_DISCRIMINATOR;
exports.INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = INITIALIZE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR;
exports.INITIALIZE_GROUP_POINTER_DISCRIMINATOR = INITIALIZE_GROUP_POINTER_DISCRIMINATOR;
exports.INITIALIZE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR = INITIALIZE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR;
exports.INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR = INITIALIZE_IMMUTABLE_OWNER_DISCRIMINATOR;
exports.INITIALIZE_INTEREST_BEARING_MINT_DISCRIMINATOR = INITIALIZE_INTEREST_BEARING_MINT_DISCRIMINATOR;
exports.INITIALIZE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR = INITIALIZE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR;
exports.INITIALIZE_METADATA_POINTER_DISCRIMINATOR = INITIALIZE_METADATA_POINTER_DISCRIMINATOR;
exports.INITIALIZE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR = INITIALIZE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR;
exports.INITIALIZE_MINT2_DISCRIMINATOR = INITIALIZE_MINT2_DISCRIMINATOR;
exports.INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR = INITIALIZE_MINT_CLOSE_AUTHORITY_DISCRIMINATOR;
exports.INITIALIZE_MINT_DISCRIMINATOR = INITIALIZE_MINT_DISCRIMINATOR;
exports.INITIALIZE_MULTISIG2_DISCRIMINATOR = INITIALIZE_MULTISIG2_DISCRIMINATOR;
exports.INITIALIZE_MULTISIG_DISCRIMINATOR = INITIALIZE_MULTISIG_DISCRIMINATOR;
exports.INITIALIZE_NON_TRANSFERABLE_MINT_DISCRIMINATOR = INITIALIZE_NON_TRANSFERABLE_MINT_DISCRIMINATOR;
exports.INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR = INITIALIZE_PAUSABLE_CONFIG_DISCRIMINATOR;
exports.INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR = INITIALIZE_PAUSABLE_CONFIG_PAUSABLE_DISCRIMINATOR;
exports.INITIALIZE_PERMANENT_DELEGATE_DISCRIMINATOR = INITIALIZE_PERMANENT_DELEGATE_DISCRIMINATOR;
exports.INITIALIZE_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = INITIALIZE_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR;
exports.INITIALIZE_SCALED_UI_AMOUNT_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = INITIALIZE_SCALED_UI_AMOUNT_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR;
exports.INITIALIZE_TOKEN_GROUP_DISCRIMINATOR = INITIALIZE_TOKEN_GROUP_DISCRIMINATOR;
exports.INITIALIZE_TOKEN_GROUP_MEMBER_DISCRIMINATOR = INITIALIZE_TOKEN_GROUP_MEMBER_DISCRIMINATOR;
exports.INITIALIZE_TOKEN_METADATA_DISCRIMINATOR = INITIALIZE_TOKEN_METADATA_DISCRIMINATOR;
exports.INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR = INITIALIZE_TRANSFER_FEE_CONFIG_DISCRIMINATOR;
exports.INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR = INITIALIZE_TRANSFER_FEE_CONFIG_TRANSFER_FEE_DISCRIMINATOR;
exports.INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR = INITIALIZE_TRANSFER_HOOK_DISCRIMINATOR;
exports.INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR = INITIALIZE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR;
exports.MINT_TO_CHECKED_DISCRIMINATOR = MINT_TO_CHECKED_DISCRIMINATOR;
exports.MINT_TO_DISCRIMINATOR = MINT_TO_DISCRIMINATOR;
exports.PAUSE_DISCRIMINATOR = PAUSE_DISCRIMINATOR;
exports.PAUSE_PAUSABLE_DISCRIMINATOR = PAUSE_PAUSABLE_DISCRIMINATOR;
exports.REALLOCATE_DISCRIMINATOR = REALLOCATE_DISCRIMINATOR;
exports.RECOVER_NESTED_ASSOCIATED_TOKEN_DISCRIMINATOR = RECOVER_NESTED_ASSOCIATED_TOKEN_DISCRIMINATOR;
exports.REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR = REMOVE_TOKEN_METADATA_KEY_DISCRIMINATOR;
exports.RESUME_DISCRIMINATOR = RESUME_DISCRIMINATOR;
exports.RESUME_PAUSABLE_DISCRIMINATOR = RESUME_PAUSABLE_DISCRIMINATOR;
exports.REVOKE_DISCRIMINATOR = REVOKE_DISCRIMINATOR;
exports.SET_AUTHORITY_DISCRIMINATOR = SET_AUTHORITY_DISCRIMINATOR;
exports.SET_TRANSFER_FEE_DISCRIMINATOR = SET_TRANSFER_FEE_DISCRIMINATOR;
exports.SET_TRANSFER_FEE_TRANSFER_FEE_DISCRIMINATOR = SET_TRANSFER_FEE_TRANSFER_FEE_DISCRIMINATOR;
exports.SYNC_NATIVE_DISCRIMINATOR = SYNC_NATIVE_DISCRIMINATOR;
exports.THAW_ACCOUNT_DISCRIMINATOR = THAW_ACCOUNT_DISCRIMINATOR;
exports.TOKEN_2022_ERROR__ACCOUNT_FROZEN = TOKEN_2022_ERROR__ACCOUNT_FROZEN;
exports.TOKEN_2022_ERROR__ALREADY_IN_USE = TOKEN_2022_ERROR__ALREADY_IN_USE;
exports.TOKEN_2022_ERROR__AUTHORITY_TYPE_NOT_SUPPORTED = TOKEN_2022_ERROR__AUTHORITY_TYPE_NOT_SUPPORTED;
exports.TOKEN_2022_ERROR__FIXED_SUPPLY = TOKEN_2022_ERROR__FIXED_SUPPLY;
exports.TOKEN_2022_ERROR__INSUFFICIENT_FUNDS = TOKEN_2022_ERROR__INSUFFICIENT_FUNDS;
exports.TOKEN_2022_ERROR__INVALID_INSTRUCTION = TOKEN_2022_ERROR__INVALID_INSTRUCTION;
exports.TOKEN_2022_ERROR__INVALID_MINT = TOKEN_2022_ERROR__INVALID_MINT;
exports.TOKEN_2022_ERROR__INVALID_NUMBER_OF_PROVIDED_SIGNERS = TOKEN_2022_ERROR__INVALID_NUMBER_OF_PROVIDED_SIGNERS;
exports.TOKEN_2022_ERROR__INVALID_NUMBER_OF_REQUIRED_SIGNERS = TOKEN_2022_ERROR__INVALID_NUMBER_OF_REQUIRED_SIGNERS;
exports.TOKEN_2022_ERROR__INVALID_STATE = TOKEN_2022_ERROR__INVALID_STATE;
exports.TOKEN_2022_ERROR__MINT_CANNOT_FREEZE = TOKEN_2022_ERROR__MINT_CANNOT_FREEZE;
exports.TOKEN_2022_ERROR__MINT_DECIMALS_MISMATCH = TOKEN_2022_ERROR__MINT_DECIMALS_MISMATCH;
exports.TOKEN_2022_ERROR__MINT_MISMATCH = TOKEN_2022_ERROR__MINT_MISMATCH;
exports.TOKEN_2022_ERROR__NATIVE_NOT_SUPPORTED = TOKEN_2022_ERROR__NATIVE_NOT_SUPPORTED;
exports.TOKEN_2022_ERROR__NON_NATIVE_HAS_BALANCE = TOKEN_2022_ERROR__NON_NATIVE_HAS_BALANCE;
exports.TOKEN_2022_ERROR__NON_NATIVE_NOT_SUPPORTED = TOKEN_2022_ERROR__NON_NATIVE_NOT_SUPPORTED;
exports.TOKEN_2022_ERROR__NOT_RENT_EXEMPT = TOKEN_2022_ERROR__NOT_RENT_EXEMPT;
exports.TOKEN_2022_ERROR__OVERFLOW = TOKEN_2022_ERROR__OVERFLOW;
exports.TOKEN_2022_ERROR__OWNER_MISMATCH = TOKEN_2022_ERROR__OWNER_MISMATCH;
exports.TOKEN_2022_ERROR__UNINITIALIZED_STATE = TOKEN_2022_ERROR__UNINITIALIZED_STATE;
exports.TOKEN_2022_PROGRAM_ADDRESS = TOKEN_2022_PROGRAM_ADDRESS;
exports.TRANSFER_CHECKED_DISCRIMINATOR = TRANSFER_CHECKED_DISCRIMINATOR;
exports.TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR = TRANSFER_CHECKED_WITH_FEE_DISCRIMINATOR;
exports.TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR = TRANSFER_CHECKED_WITH_FEE_TRANSFER_FEE_DISCRIMINATOR;
exports.TRANSFER_DISCRIMINATOR = TRANSFER_DISCRIMINATOR;
exports.Token2022Account = Token2022Account;
exports.Token2022Instruction = Token2022Instruction;
exports.UI_AMOUNT_TO_AMOUNT_DISCRIMINATOR = UI_AMOUNT_TO_AMOUNT_DISCRIMINATOR;
exports.UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR = UPDATE_CONFIDENTIAL_TRANSFER_MINT_CONFIDENTIAL_TRANSFER_DISCRIMINATOR;
exports.UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR = UPDATE_CONFIDENTIAL_TRANSFER_MINT_DISCRIMINATOR;
exports.UPDATE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = UPDATE_DEFAULT_ACCOUNT_STATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR;
exports.UPDATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR = UPDATE_DEFAULT_ACCOUNT_STATE_DISCRIMINATOR;
exports.UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR = UPDATE_GROUP_MEMBER_POINTER_DISCRIMINATOR;
exports.UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR = UPDATE_GROUP_MEMBER_POINTER_GROUP_MEMBER_POINTER_DISCRIMINATOR;
exports.UPDATE_GROUP_POINTER_DISCRIMINATOR = UPDATE_GROUP_POINTER_DISCRIMINATOR;
exports.UPDATE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR = UPDATE_GROUP_POINTER_GROUP_POINTER_DISCRIMINATOR;
exports.UPDATE_METADATA_POINTER_DISCRIMINATOR = UPDATE_METADATA_POINTER_DISCRIMINATOR;
exports.UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR = UPDATE_METADATA_POINTER_METADATA_POINTER_DISCRIMINATOR;
exports.UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR = UPDATE_MULTIPLIER_SCALED_UI_MINT_DISCRIMINATOR;
exports.UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR = UPDATE_MULTIPLIER_SCALED_UI_MINT_SCALED_UI_AMOUNT_MINT_DISCRIMINATOR;
exports.UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR = UPDATE_RATE_INTEREST_BEARING_MINT_DISCRIMINATOR;
exports.UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR = UPDATE_RATE_INTEREST_BEARING_MINT_INTEREST_BEARING_MINT_DISCRIMINATOR;
exports.UPDATE_TOKEN_GROUP_MAX_SIZE_DISCRIMINATOR = UPDATE_TOKEN_GROUP_MAX_SIZE_DISCRIMINATOR;
exports.UPDATE_TOKEN_GROUP_UPDATE_AUTHORITY_DISCRIMINATOR = UPDATE_TOKEN_GROUP_UPDATE_AUTHORITY_DISCRIMINATOR;
exports.UPDATE_TOKEN_METADATA_FIELD_DISCRIMINATOR = UPDATE_TOKEN_METADATA_FIELD_DISCRIMINATOR;
exports.UPDATE_TOKEN_METADATA_UPDATE_AUTHORITY_DISCRIMINATOR = UPDATE_TOKEN_METADATA_UPDATE_AUTHORITY_DISCRIMINATOR;
exports.UPDATE_TRANSFER_HOOK_DISCRIMINATOR = UPDATE_TRANSFER_HOOK_DISCRIMINATOR;
exports.UPDATE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR = UPDATE_TRANSFER_HOOK_TRANSFER_HOOK_DISCRIMINATOR;
exports.WITHDRAW_EXCESS_LAMPORTS_DISCRIMINATOR = WITHDRAW_EXCESS_LAMPORTS_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_ACCOUNTS_TRANSFER_FEE_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_MINT_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_MINT_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_MINT_FOR_CONFIDENTIAL_TRANSFER_FEE_DISCRIMINATOR;
exports.WITHDRAW_WITHHELD_TOKENS_FROM_MINT_TRANSFER_FEE_DISCRIMINATOR = WITHDRAW_WITHHELD_TOKENS_FROM_MINT_TRANSFER_FEE_DISCRIMINATOR;
exports.amountToUiAmountForInterestBearingMintWithoutSimulation = amountToUiAmountForInterestBearingMintWithoutSimulation;
exports.amountToUiAmountForMintWithoutSimulation = amountToUiAmountForMintWithoutSimulation;
exports.amountToUiAmountForScaledUiAmountMintWithoutSimulation = amountToUiAmountForScaledUiAmountMintWithoutSimulation;
exports.decodeMint = decodeMint;
exports.decodeMultisig = decodeMultisig;
exports.decodeToken = decodeToken;
exports.extension = extension;
exports.fetchAllMaybeMint = fetchAllMaybeMint;
exports.fetchAllMaybeMultisig = fetchAllMaybeMultisig;
exports.fetchAllMaybeToken = fetchAllMaybeToken;
exports.fetchAllMint = fetchAllMint;
exports.fetchAllMultisig = fetchAllMultisig;
exports.fetchAllToken = fetchAllToken;
exports.fetchMaybeMint = fetchMaybeMint;
exports.fetchMaybeMultisig = fetchMaybeMultisig;
exports.fetchMaybeToken = fetchMaybeToken;
exports.fetchMint = fetchMint;
exports.fetchMultisig = fetchMultisig;
exports.fetchToken = fetchToken;
exports.findAssociatedTokenPda = findAssociatedTokenPda;
exports.getAccountStateCodec = getAccountStateCodec;
exports.getAccountStateDecoder = getAccountStateDecoder;
exports.getAccountStateEncoder = getAccountStateEncoder;
exports.getAmountToUiAmountDiscriminatorBytes = getAmountToUiAmountDiscriminatorBytes;
exports.getAmountToUiAmountInstruction = getAmountToUiAmountInstruction;
exports.getAmountToUiAmountInstructionDataCodec = getAmountToUiAmountInstructionDataCodec;
exports.getAmountToUiAmountInstructionDataDecoder = getAmountToUiAmountInstructionDataDecoder;
exports.getAmountToUiAmountInstructionDataEncoder = getAmountToUiAmountInstructionDataEncoder;
exports.getApplyConfidentialPendingBalanceConfidentialTransferDiscriminatorBytes = getApplyConfidentialPendingBalanceConfidentialTransferDiscriminatorBytes;
exports.getApplyConfidentialPendingBalanceDiscriminatorBytes = getApplyConfidentialPendingBalanceDiscriminatorBytes;
exports.getApplyConfidentialPendingBalanceInstruction = getApplyConfidentialPendingBalanceInstruction;
exports.getApplyConfidentialPendingBalanceInstructionDataCodec = getApplyConfidentialPendingBalanceInstructionDataCodec;
exports.getApplyConfidentialPendingBalanceInstructionDataDecoder = getApplyConfidentialPendingBalanceInstructionDataDecoder;
exports.getApplyConfidentialPendingBalanceInstructionDataEncoder = getApplyConfidentialPendingBalanceInstructionDataEncoder;
exports.getApproveCheckedDiscriminatorBytes = getApproveCheckedDiscriminatorBytes;
exports.getApproveCheckedInstruction = getApproveCheckedInstruction;
exports.getApproveCheckedInstructionDataCodec = getApproveCheckedInstructionDataCodec;
exports.getApproveCheckedInstructionDataDecoder = getApproveCheckedInstructionDataDecoder;
exports.getApproveCheckedInstructionDataEncoder = getApproveCheckedInstructionDataEncoder;
exports.getApproveConfidentialTransferAccountConfidentialTransferDiscriminatorBytes = getApproveConfidentialTransferAccountConfidentialTransferDiscriminatorBytes;
exports.getApproveConfidentialTransferAccountDiscriminatorBytes = getApproveConfidentialTransferAccountDiscriminatorBytes;
exports.getApproveConfidentialTransferAccountInstruction = getApproveConfidentialTransferAccountInstruction;
exports.getApproveConfidentialTransferAccountInstructionDataCodec = getApproveConfidentialTransferAccountInstructionDataCodec;
exports.getApproveConfidentialTransferAccountInstructionDataDecoder = getApproveConfidentialTransferAccountInstructionDataDecoder;
exports.getApproveConfidentialTransferAccountInstructionDataEncoder = getApproveConfidentialTransferAccountInstructionDataEncoder;
exports.getApproveDiscriminatorBytes = getApproveDiscriminatorBytes;
exports.getApproveInstruction = getApproveInstruction;
exports.getApproveInstructionDataCodec = getApproveInstructionDataCodec;
exports.getApproveInstructionDataDecoder = getApproveInstructionDataDecoder;
exports.getApproveInstructionDataEncoder = getApproveInstructionDataEncoder;
exports.getAssociatedTokenErrorMessage = getAssociatedTokenErrorMessage;
exports.getAuthorityTypeCodec = getAuthorityTypeCodec;
exports.getAuthorityTypeDecoder = getAuthorityTypeDecoder;
exports.getAuthorityTypeEncoder = getAuthorityTypeEncoder;
exports.getBurnCheckedDiscriminatorBytes = getBurnCheckedDiscriminatorBytes;
exports.getBurnCheckedInstruction = getBurnCheckedInstruction;
exports.getBurnCheckedInstructionDataCodec = getBurnCheckedInstructionDataCodec;
exports.getBurnCheckedInstructionDataDecoder = getBurnCheckedInstructionDataDecoder;
exports.getBurnCheckedInstructionDataEncoder = getBurnCheckedInstructionDataEncoder;
exports.getBurnDiscriminatorBytes = getBurnDiscriminatorBytes;
exports.getBurnInstruction = getBurnInstruction;
exports.getBurnInstructionDataCodec = getBurnInstructionDataCodec;
exports.getBurnInstructionDataDecoder = getBurnInstructionDataDecoder;
exports.getBurnInstructionDataEncoder = getBurnInstructionDataEncoder;
exports.getCloseAccountDiscriminatorBytes = getCloseAccountDiscriminatorBytes;
exports.getCloseAccountInstruction = getCloseAccountInstruction;
exports.getCloseAccountInstructionDataCodec = getCloseAccountInstructionDataCodec;
exports.getCloseAccountInstructionDataDecoder = getCloseAccountInstructionDataDecoder;
exports.getCloseAccountInstructionDataEncoder = getCloseAccountInstructionDataEncoder;
exports.getConfidentialDepositConfidentialTransferDiscriminatorBytes = getConfidentialDepositConfidentialTransferDiscriminatorBytes;
exports.getConfidentialDepositDiscriminatorBytes = getConfidentialDepositDiscriminatorBytes;
exports.getConfidentialDepositInstruction = getConfidentialDepositInstruction;
exports.getConfidentialDepositInstructionDataCodec = getConfidentialDepositInstructionDataCodec;
exports.getConfidentialDepositInstructionDataDecoder = getConfidentialDepositInstructionDataDecoder;
exports.getConfidentialDepositInstructionDataEncoder = getConfidentialDepositInstructionDataEncoder;
exports.getConfidentialTransferConfidentialTransferDiscriminatorBytes = getConfidentialTransferConfidentialTransferDiscriminatorBytes;
exports.getConfidentialTransferDiscriminatorBytes = getConfidentialTransferDiscriminatorBytes;
exports.getConfidentialTransferInstruction = getConfidentialTransferInstruction;
exports.getConfidentialTransferInstructionDataCodec = getConfidentialTransferInstructionDataCodec;
exports.getConfidentialTransferInstructionDataDecoder = getConfidentialTransferInstructionDataDecoder;
exports.getConfidentialTransferInstructionDataEncoder = getConfidentialTransferInstructionDataEncoder;
exports.getConfidentialTransferWithFeeConfidentialTransferDiscriminatorBytes = getConfidentialTransferWithFeeConfidentialTransferDiscriminatorBytes;
exports.getConfidentialTransferWithFeeDiscriminatorBytes = getConfidentialTransferWithFeeDiscriminatorBytes;
exports.getConfidentialTransferWithFeeInstruction = getConfidentialTransferWithFeeInstruction;
exports.getConfidentialTransferWithFeeInstructionDataCodec = getConfidentialTransferWithFeeInstructionDataCodec;
exports.getConfidentialTransferWithFeeInstructionDataDecoder = getConfidentialTransferWithFeeInstructionDataDecoder;
exports.getConfidentialTransferWithFeeInstructionDataEncoder = getConfidentialTransferWithFeeInstructionDataEncoder;
exports.getConfidentialWithdrawConfidentialTransferDiscriminatorBytes = getConfidentialWithdrawConfidentialTransferDiscriminatorBytes;
exports.getConfidentialWithdrawDiscriminatorBytes = getConfidentialWithdrawDiscriminatorBytes;
exports.getConfidentialWithdrawInstruction = getConfidentialWithdrawInstruction;
exports.getConfidentialWithdrawInstructionDataCodec = getConfidentialWithdrawInstructionDataCodec;
exports.getConfidentialWithdrawInstructionDataDecoder = getConfidentialWithdrawInstructionDataDecoder;
exports.getConfidentialWithdrawInstructionDataEncoder = getConfidentialWithdrawInstructionDataEncoder;
exports.getConfigureConfidentialTransferAccountConfidentialTransferDiscriminatorBytes = getConfigureConfidentialTransferAccountConfidentialTransferDiscriminatorBytes;
exports.getConfigureConfidentialTransferAccountDiscriminatorBytes = getConfigureConfidentialTransferAccountDiscriminatorBytes;
exports.getConfigureConfidentialTransferAccountInstruction = getConfigureConfidentialTransferAccountInstruction;
exports.getConfigureConfidentialTransferAccountInstructionDataCodec = getConfigureConfidentialTransferAccountInstructionDataCodec;
exports.getConfigureConfidentialTransferAccountInstructionDataDecoder = getConfigureConfidentialTransferAccountInstructionDataDecoder;
exports.getConfigureConfidentialTransferAccountInstructionDataEncoder = getConfigureConfidentialTransferAccountInstructionDataEncoder;
exports.getCreateAssociatedTokenDiscriminatorBytes = getCreateAssociatedTokenDiscriminatorBytes;
exports.getCreateAssociatedTokenIdempotentDiscriminatorBytes = getCreateAssociatedTokenIdempotentDiscriminatorBytes;
exports.getCreateAssociatedTokenIdempotentInstruction = getCreateAssociatedTokenIdempotentInstruction;
exports.getCreateAssociatedTokenIdempotentInstructionAsync = getCreateAssociatedTokenIdempotentInstructionAsync;
exports.getCreateAssociatedTokenIdempotentInstructionDataCodec = getCreateAssociatedTokenIdempotentInstructionDataCodec;
exports.getCreateAssociatedTokenIdempotentInstructionDataDecoder = getCreateAssociatedTokenIdempotentInstructionDataDecoder;
exports.getCreateAssociatedTokenIdempotentInstructionDataEncoder = getCreateAssociatedTokenIdempotentInstructionDataEncoder;
exports.getCreateAssociatedTokenInstruction = getCreateAssociatedTokenInstruction;
exports.getCreateAssociatedTokenInstructionAsync = getCreateAssociatedTokenInstructionAsync;
exports.getCreateAssociatedTokenInstructionDataCodec = getCreateAssociatedTokenInstructionDataCodec;
exports.getCreateAssociatedTokenInstructionDataDecoder = getCreateAssociatedTokenInstructionDataDecoder;
exports.getCreateAssociatedTokenInstructionDataEncoder = getCreateAssociatedTokenInstructionDataEncoder;
exports.getCreateNativeMintDiscriminatorBytes = getCreateNativeMintDiscriminatorBytes;
exports.getCreateNativeMintInstruction = getCreateNativeMintInstruction;
exports.getCreateNativeMintInstructionDataCodec = getCreateNativeMintInstructionDataCodec;
exports.getCreateNativeMintInstructionDataDecoder = getCreateNativeMintInstructionDataDecoder;
exports.getCreateNativeMintInstructionDataEncoder = getCreateNativeMintInstructionDataEncoder;
exports.getDecryptableBalanceCodec = getDecryptableBalanceCodec;
exports.getDecryptableBalanceDecoder = getDecryptableBalanceDecoder;
exports.getDecryptableBalanceEncoder = getDecryptableBalanceEncoder;
exports.getDisableConfidentialCreditsConfidentialTransferDiscriminatorBytes = getDisableConfidentialCreditsConfidentialTransferDiscriminatorBytes;
exports.getDisableConfidentialCreditsDiscriminatorBytes = getDisableConfidentialCreditsDiscriminatorBytes;
exports.getDisableConfidentialCreditsInstruction = getDisableConfidentialCreditsInstruction;
exports.getDisableConfidentialCreditsInstructionDataCodec = getDisableConfidentialCreditsInstructionDataCodec;
exports.getDisableConfidentialCreditsInstructionDataDecoder = getDisableConfidentialCreditsInstructionDataDecoder;
exports.getDisableConfidentialCreditsInstructionDataEncoder = getDisableConfidentialCreditsInstructionDataEncoder;
exports.getDisableCpiGuardCpiGuardDiscriminatorBytes = getDisableCpiGuardCpiGuardDiscriminatorBytes;
exports.getDisableCpiGuardDiscriminatorBytes = getDisableCpiGuardDiscriminatorBytes;
exports.getDisableCpiGuardInstruction = getDisableCpiGuardInstruction;
exports.getDisableCpiGuardInstructionDataCodec = getDisableCpiGuardInstructionDataCodec;
exports.getDisableCpiGuardInstructionDataDecoder = getDisableCpiGuardInstructionDataDecoder;
exports.getDisableCpiGuardInstructionDataEncoder = getDisableCpiGuardInstructionDataEncoder;
exports.getDisableHarvestToMintConfidentialTransferFeeDiscriminatorBytes = getDisableHarvestToMintConfidentialTransferFeeDiscriminatorBytes;
exports.getDisableHarvestToMintDiscriminatorBytes = getDisableHarvestToMintDiscriminatorBytes;
exports.getDisableHarvestToMintInstruction = getDisableHarvestToMintInstruction;
exports.getDisableHarvestToMintInstructionDataCodec = getDisableHarvestToMintInstructionDataCodec;
exports.getDisableHarvestToMintInstructionDataDecoder = getDisableHarvestToMintInstructionDataDecoder;
exports.getDisableHarvestToMintInstructionDataEncoder = getDisableHarvestToMintInstructionDataEncoder;
exports.getDisableMemoTransfersDiscriminatorBytes = getDisableMemoTransfersDiscriminatorBytes;
exports.getDisableMemoTransfersInstruction = getDisableMemoTransfersInstruction;
exports.getDisableMemoTransfersInstructionDataCodec = getDisableMemoTransfersInstructionDataCodec;
exports.getDisableMemoTransfersInstructionDataDecoder = getDisableMemoTransfersInstructionDataDecoder;
exports.getDisableMemoTransfersInstructionDataEncoder = getDisableMemoTransfersInstructionDataEncoder;
exports.getDisableMemoTransfersMemoTransfersDiscriminatorBytes = getDisableMemoTransfersMemoTransfersDiscriminatorBytes;
exports.getDisableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes = getDisableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes;
exports.getDisableNonConfidentialCreditsDiscriminatorBytes = getDisableNonConfidentialCreditsDiscriminatorBytes;
exports.getDisableNonConfidentialCreditsInstruction = getDisableNonConfidentialCreditsInstruction;
exports.getDisableNonConfidentialCreditsInstructionDataCodec = getDisableNonConfidentialCreditsInstructionDataCodec;
exports.getDisableNonConfidentialCreditsInstructionDataDecoder = getDisableNonConfidentialCreditsInstructionDataDecoder;
exports.getDisableNonConfidentialCreditsInstructionDataEncoder = getDisableNonConfidentialCreditsInstructionDataEncoder;
exports.getEmitTokenMetadataDiscriminatorBytes = getEmitTokenMetadataDiscriminatorBytes;
exports.getEmitTokenMetadataInstruction = getEmitTokenMetadataInstruction;
exports.getEmitTokenMetadataInstructionDataCodec = getEmitTokenMetadataInstructionDataCodec;
exports.getEmitTokenMetadataInstructionDataDecoder = getEmitTokenMetadataInstructionDataDecoder;
exports.getEmitTokenMetadataInstructionDataEncoder = getEmitTokenMetadataInstructionDataEncoder;
exports.getEmptyConfidentialTransferAccountConfidentialTransferDiscriminatorBytes = getEmptyConfidentialTransferAccountConfidentialTransferDiscriminatorBytes;
exports.getEmptyConfidentialTransferAccountDiscriminatorBytes = getEmptyConfidentialTransferAccountDiscriminatorBytes;
exports.getEmptyConfidentialTransferAccountInstruction = getEmptyConfidentialTransferAccountInstruction;
exports.getEmptyConfidentialTransferAccountInstructionDataCodec = getEmptyConfidentialTransferAccountInstructionDataCodec;
exports.getEmptyConfidentialTransferAccountInstructionDataDecoder = getEmptyConfidentialTransferAccountInstructionDataDecoder;
exports.getEmptyConfidentialTransferAccountInstructionDataEncoder = getEmptyConfidentialTransferAccountInstructionDataEncoder;
exports.getEnableConfidentialCreditsConfidentialTransferDiscriminatorBytes = getEnableConfidentialCreditsConfidentialTransferDiscriminatorBytes;
exports.getEnableConfidentialCreditsDiscriminatorBytes = getEnableConfidentialCreditsDiscriminatorBytes;
exports.getEnableConfidentialCreditsInstruction = getEnableConfidentialCreditsInstruction;
exports.getEnableConfidentialCreditsInstructionDataCodec = getEnableConfidentialCreditsInstructionDataCodec;
exports.getEnableConfidentialCreditsInstructionDataDecoder = getEnableConfidentialCreditsInstructionDataDecoder;
exports.getEnableConfidentialCreditsInstructionDataEncoder = getEnableConfidentialCreditsInstructionDataEncoder;
exports.getEnableCpiGuardCpiGuardDiscriminatorBytes = getEnableCpiGuardCpiGuardDiscriminatorBytes;
exports.getEnableCpiGuardDiscriminatorBytes = getEnableCpiGuardDiscriminatorBytes;
exports.getEnableCpiGuardInstruction = getEnableCpiGuardInstruction;
exports.getEnableCpiGuardInstructionDataCodec = getEnableCpiGuardInstructionDataCodec;
exports.getEnableCpiGuardInstructionDataDecoder = getEnableCpiGuardInstructionDataDecoder;
exports.getEnableCpiGuardInstructionDataEncoder = getEnableCpiGuardInstructionDataEncoder;
exports.getEnableHarvestToMintConfidentialTransferFeeDiscriminatorBytes = getEnableHarvestToMintConfidentialTransferFeeDiscriminatorBytes;
exports.getEnableHarvestToMintDiscriminatorBytes = getEnableHarvestToMintDiscriminatorBytes;
exports.getEnableHarvestToMintInstruction = getEnableHarvestToMintInstruction;
exports.getEnableHarvestToMintInstructionDataCodec = getEnableHarvestToMintInstructionDataCodec;
exports.getEnableHarvestToMintInstructionDataDecoder = getEnableHarvestToMintInstructionDataDecoder;
exports.getEnableHarvestToMintInstructionDataEncoder = getEnableHarvestToMintInstructionDataEncoder;
exports.getEnableMemoTransfersDiscriminatorBytes = getEnableMemoTransfersDiscriminatorBytes;
exports.getEnableMemoTransfersInstruction = getEnableMemoTransfersInstruction;
exports.getEnableMemoTransfersInstructionDataCodec = getEnableMemoTransfersInstructionDataCodec;
exports.getEnableMemoTransfersInstructionDataDecoder = getEnableMemoTransfersInstructionDataDecoder;
exports.getEnableMemoTransfersInstructionDataEncoder = getEnableMemoTransfersInstructionDataEncoder;
exports.getEnableMemoTransfersMemoTransfersDiscriminatorBytes = getEnableMemoTransfersMemoTransfersDiscriminatorBytes;
exports.getEnableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes = getEnableNonConfidentialCreditsConfidentialTransferDiscriminatorBytes;
exports.getEnableNonConfidentialCreditsDiscriminatorBytes = getEnableNonConfidentialCreditsDiscriminatorBytes;
exports.getEnableNonConfidentialCreditsInstruction = getEnableNonConfidentialCreditsInstruction;
exports.getEnableNonConfidentialCreditsInstructionDataCodec = getEnableNonConfidentialCreditsInstructionDataCodec;
exports.getEnableNonConfidentialCreditsInstructionDataDecoder = getEnableNonConfidentialCreditsInstructionDataDecoder;
exports.getEnableNonConfidentialCreditsInstructionDataEncoder = getEnableNonConfidentialCreditsInstructionDataEncoder;
exports.getEncryptedBalanceCodec = getEncryptedBalanceCodec;
exports.getEncryptedBalanceDecoder = getEncryptedBalanceDecoder;
exports.getEncryptedBalanceEncoder = getEncryptedBalanceEncoder;
exports.getExtensionCodec = getExtensionCodec;
exports.getExtensionDecoder = getExtensionDecoder;
exports.getExtensionEncoder = getExtensionEncoder;
exports.getExtensionTypeCodec = getExtensionTypeCodec;
exports.getExtensionTypeDecoder = getExtensionTypeDecoder;
exports.getExtensionTypeEncoder = getExtensionTypeEncoder;
exports.getFreezeAccountDiscriminatorBytes = getFreezeAccountDiscriminatorBytes;
exports.getFreezeAccountInstruction = getFreezeAccountInstruction;
exports.getFreezeAccountInstructionDataCodec = getFreezeAccountInstructionDataCodec;
exports.getFreezeAccountInstructionDataDecoder = getFreezeAccountInstructionDataDecoder;
exports.getFreezeAccountInstructionDataEncoder = getFreezeAccountInstructionDataEncoder;
exports.getGetAccountDataSizeDiscriminatorBytes = getGetAccountDataSizeDiscriminatorBytes;
exports.getGetAccountDataSizeInstruction = getGetAccountDataSizeInstruction;
exports.getGetAccountDataSizeInstructionDataCodec = getGetAccountDataSizeInstructionDataCodec;
exports.getGetAccountDataSizeInstructionDataDecoder = getGetAccountDataSizeInstructionDataDecoder;
exports.getGetAccountDataSizeInstructionDataEncoder = getGetAccountDataSizeInstructionDataEncoder;
exports.getHarvestWithheldTokensToMintDiscriminatorBytes = getHarvestWithheldTokensToMintDiscriminatorBytes;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes = getHarvestWithheldTokensToMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeDiscriminatorBytes = getHarvestWithheldTokensToMintForConfidentialTransferFeeDiscriminatorBytes;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction = getHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataCodec = getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataCodec;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataDecoder = getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataDecoder;
exports.getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataEncoder = getHarvestWithheldTokensToMintForConfidentialTransferFeeInstructionDataEncoder;
exports.getHarvestWithheldTokensToMintInstruction = getHarvestWithheldTokensToMintInstruction;
exports.getHarvestWithheldTokensToMintInstructionDataCodec = getHarvestWithheldTokensToMintInstructionDataCodec;
exports.getHarvestWithheldTokensToMintInstructionDataDecoder = getHarvestWithheldTokensToMintInstructionDataDecoder;
exports.getHarvestWithheldTokensToMintInstructionDataEncoder = getHarvestWithheldTokensToMintInstructionDataEncoder;
exports.getHarvestWithheldTokensToMintTransferFeeDiscriminatorBytes = getHarvestWithheldTokensToMintTransferFeeDiscriminatorBytes;
exports.getInitializeAccount2DiscriminatorBytes = getInitializeAccount2DiscriminatorBytes;
exports.getInitializeAccount2Instruction = getInitializeAccount2Instruction;
exports.getInitializeAccount2InstructionDataCodec = getInitializeAccount2InstructionDataCodec;
exports.getInitializeAccount2InstructionDataDecoder = getInitializeAccount2InstructionDataDecoder;
exports.getInitializeAccount2InstructionDataEncoder = getInitializeAccount2InstructionDataEncoder;
exports.getInitializeAccount3DiscriminatorBytes = getInitializeAccount3DiscriminatorBytes;
exports.getInitializeAccount3Instruction = getInitializeAccount3Instruction;
exports.getInitializeAccount3InstructionDataCodec = getInitializeAccount3InstructionDataCodec;
exports.getInitializeAccount3InstructionDataDecoder = getInitializeAccount3InstructionDataDecoder;
exports.getInitializeAccount3InstructionDataEncoder = getInitializeAccount3InstructionDataEncoder;
exports.getInitializeAccountDiscriminatorBytes = getInitializeAccountDiscriminatorBytes;
exports.getInitializeAccountInstruction = getInitializeAccountInstruction;
exports.getInitializeAccountInstructionDataCodec = getInitializeAccountInstructionDataCodec;
exports.getInitializeAccountInstructionDataDecoder = getInitializeAccountInstructionDataDecoder;
exports.getInitializeAccountInstructionDataEncoder = getInitializeAccountInstructionDataEncoder;
exports.getInitializeConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes = getInitializeConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes;
exports.getInitializeConfidentialTransferFeeDiscriminatorBytes = getInitializeConfidentialTransferFeeDiscriminatorBytes;
exports.getInitializeConfidentialTransferFeeInstruction = getInitializeConfidentialTransferFeeInstruction;
exports.getInitializeConfidentialTransferFeeInstructionDataCodec = getInitializeConfidentialTransferFeeInstructionDataCodec;
exports.getInitializeConfidentialTransferFeeInstructionDataDecoder = getInitializeConfidentialTransferFeeInstructionDataDecoder;
exports.getInitializeConfidentialTransferFeeInstructionDataEncoder = getInitializeConfidentialTransferFeeInstructionDataEncoder;
exports.getInitializeConfidentialTransferMintConfidentialTransferDiscriminatorBytes = getInitializeConfidentialTransferMintConfidentialTransferDiscriminatorBytes;
exports.getInitializeConfidentialTransferMintDiscriminatorBytes = getInitializeConfidentialTransferMintDiscriminatorBytes;
exports.getInitializeConfidentialTransferMintInstruction = getInitializeConfidentialTransferMintInstruction;
exports.getInitializeConfidentialTransferMintInstructionDataCodec = getInitializeConfidentialTransferMintInstructionDataCodec;
exports.getInitializeConfidentialTransferMintInstructionDataDecoder = getInitializeConfidentialTransferMintInstructionDataDecoder;
exports.getInitializeConfidentialTransferMintInstructionDataEncoder = getInitializeConfidentialTransferMintInstructionDataEncoder;
exports.getInitializeDefaultAccountStateDefaultAccountStateDiscriminatorBytes = getInitializeDefaultAccountStateDefaultAccountStateDiscriminatorBytes;
exports.getInitializeDefaultAccountStateDiscriminatorBytes = getInitializeDefaultAccountStateDiscriminatorBytes;
exports.getInitializeDefaultAccountStateInstruction = getInitializeDefaultAccountStateInstruction;
exports.getInitializeDefaultAccountStateInstructionDataCodec = getInitializeDefaultAccountStateInstructionDataCodec;
exports.getInitializeDefaultAccountStateInstructionDataDecoder = getInitializeDefaultAccountStateInstructionDataDecoder;
exports.getInitializeDefaultAccountStateInstructionDataEncoder = getInitializeDefaultAccountStateInstructionDataEncoder;
exports.getInitializeGroupMemberPointerDiscriminatorBytes = getInitializeGroupMemberPointerDiscriminatorBytes;
exports.getInitializeGroupMemberPointerGroupMemberPointerDiscriminatorBytes = getInitializeGroupMemberPointerGroupMemberPointerDiscriminatorBytes;
exports.getInitializeGroupMemberPointerInstruction = getInitializeGroupMemberPointerInstruction;
exports.getInitializeGroupMemberPointerInstructionDataCodec = getInitializeGroupMemberPointerInstructionDataCodec;
exports.getInitializeGroupMemberPointerInstructionDataDecoder = getInitializeGroupMemberPointerInstructionDataDecoder;
exports.getInitializeGroupMemberPointerInstructionDataEncoder = getInitializeGroupMemberPointerInstructionDataEncoder;
exports.getInitializeGroupPointerDiscriminatorBytes = getInitializeGroupPointerDiscriminatorBytes;
exports.getInitializeGroupPointerGroupPointerDiscriminatorBytes = getInitializeGroupPointerGroupPointerDiscriminatorBytes;
exports.getInitializeGroupPointerInstruction = getInitializeGroupPointerInstruction;
exports.getInitializeGroupPointerInstructionDataCodec = getInitializeGroupPointerInstructionDataCodec;
exports.getInitializeGroupPointerInstructionDataDecoder = getInitializeGroupPointerInstructionDataDecoder;
exports.getInitializeGroupPointerInstructionDataEncoder = getInitializeGroupPointerInstructionDataEncoder;
exports.getInitializeImmutableOwnerDiscriminatorBytes = getInitializeImmutableOwnerDiscriminatorBytes;
exports.getInitializeImmutableOwnerInstruction = getInitializeImmutableOwnerInstruction;
exports.getInitializeImmutableOwnerInstructionDataCodec = getInitializeImmutableOwnerInstructionDataCodec;
exports.getInitializeImmutableOwnerInstructionDataDecoder = getInitializeImmutableOwnerInstructionDataDecoder;
exports.getInitializeImmutableOwnerInstructionDataEncoder = getInitializeImmutableOwnerInstructionDataEncoder;
exports.getInitializeInterestBearingMintDiscriminatorBytes = getInitializeInterestBearingMintDiscriminatorBytes;
exports.getInitializeInterestBearingMintInstruction = getInitializeInterestBearingMintInstruction;
exports.getInitializeInterestBearingMintInstructionDataCodec = getInitializeInterestBearingMintInstructionDataCodec;
exports.getInitializeInterestBearingMintInstructionDataDecoder = getInitializeInterestBearingMintInstructionDataDecoder;
exports.getInitializeInterestBearingMintInstructionDataEncoder = getInitializeInterestBearingMintInstructionDataEncoder;
exports.getInitializeInterestBearingMintInterestBearingMintDiscriminatorBytes = getInitializeInterestBearingMintInterestBearingMintDiscriminatorBytes;
exports.getInitializeMetadataPointerDiscriminatorBytes = getInitializeMetadataPointerDiscriminatorBytes;
exports.getInitializeMetadataPointerInstruction = getInitializeMetadataPointerInstruction;
exports.getInitializeMetadataPointerInstructionDataCodec = getInitializeMetadataPointerInstructionDataCodec;
exports.getInitializeMetadataPointerInstructionDataDecoder = getInitializeMetadataPointerInstructionDataDecoder;
exports.getInitializeMetadataPointerInstructionDataEncoder = getInitializeMetadataPointerInstructionDataEncoder;
exports.getInitializeMetadataPointerMetadataPointerDiscriminatorBytes = getInitializeMetadataPointerMetadataPointerDiscriminatorBytes;
exports.getInitializeMint2DiscriminatorBytes = getInitializeMint2DiscriminatorBytes;
exports.getInitializeMint2Instruction = getInitializeMint2Instruction;
exports.getInitializeMint2InstructionDataCodec = getInitializeMint2InstructionDataCodec;
exports.getInitializeMint2InstructionDataDecoder = getInitializeMint2InstructionDataDecoder;
exports.getInitializeMint2InstructionDataEncoder = getInitializeMint2InstructionDataEncoder;
exports.getInitializeMintCloseAuthorityDiscriminatorBytes = getInitializeMintCloseAuthorityDiscriminatorBytes;
exports.getInitializeMintCloseAuthorityInstruction = getInitializeMintCloseAuthorityInstruction;
exports.getInitializeMintCloseAuthorityInstructionDataCodec = getInitializeMintCloseAuthorityInstructionDataCodec;
exports.getInitializeMintCloseAuthorityInstructionDataDecoder = getInitializeMintCloseAuthorityInstructionDataDecoder;
exports.getInitializeMintCloseAuthorityInstructionDataEncoder = getInitializeMintCloseAuthorityInstructionDataEncoder;
exports.getInitializeMintDiscriminatorBytes = getInitializeMintDiscriminatorBytes;
exports.getInitializeMintInstruction = getInitializeMintInstruction;
exports.getInitializeMintInstructionDataCodec = getInitializeMintInstructionDataCodec;
exports.getInitializeMintInstructionDataDecoder = getInitializeMintInstructionDataDecoder;
exports.getInitializeMintInstructionDataEncoder = getInitializeMintInstructionDataEncoder;
exports.getInitializeMultisig2DiscriminatorBytes = getInitializeMultisig2DiscriminatorBytes;
exports.getInitializeMultisig2Instruction = getInitializeMultisig2Instruction;
exports.getInitializeMultisig2InstructionDataCodec = getInitializeMultisig2InstructionDataCodec;
exports.getInitializeMultisig2InstructionDataDecoder = getInitializeMultisig2InstructionDataDecoder;
exports.getInitializeMultisig2InstructionDataEncoder = getInitializeMultisig2InstructionDataEncoder;
exports.getInitializeMultisigDiscriminatorBytes = getInitializeMultisigDiscriminatorBytes;
exports.getInitializeMultisigInstruction = getInitializeMultisigInstruction;
exports.getInitializeMultisigInstructionDataCodec = getInitializeMultisigInstructionDataCodec;
exports.getInitializeMultisigInstructionDataDecoder = getInitializeMultisigInstructionDataDecoder;
exports.getInitializeMultisigInstructionDataEncoder = getInitializeMultisigInstructionDataEncoder;
exports.getInitializeNonTransferableMintDiscriminatorBytes = getInitializeNonTransferableMintDiscriminatorBytes;
exports.getInitializeNonTransferableMintInstruction = getInitializeNonTransferableMintInstruction;
exports.getInitializeNonTransferableMintInstructionDataCodec = getInitializeNonTransferableMintInstructionDataCodec;
exports.getInitializeNonTransferableMintInstructionDataDecoder = getInitializeNonTransferableMintInstructionDataDecoder;
exports.getInitializeNonTransferableMintInstructionDataEncoder = getInitializeNonTransferableMintInstructionDataEncoder;
exports.getInitializePausableConfigDiscriminatorBytes = getInitializePausableConfigDiscriminatorBytes;
exports.getInitializePausableConfigInstruction = getInitializePausableConfigInstruction;
exports.getInitializePausableConfigInstructionDataCodec = getInitializePausableConfigInstructionDataCodec;
exports.getInitializePausableConfigInstructionDataDecoder = getInitializePausableConfigInstructionDataDecoder;
exports.getInitializePausableConfigInstructionDataEncoder = getInitializePausableConfigInstructionDataEncoder;
exports.getInitializePausableConfigPausableDiscriminatorBytes = getInitializePausableConfigPausableDiscriminatorBytes;
exports.getInitializePermanentDelegateDiscriminatorBytes = getInitializePermanentDelegateDiscriminatorBytes;
exports.getInitializePermanentDelegateInstruction = getInitializePermanentDelegateInstruction;
exports.getInitializePermanentDelegateInstructionDataCodec = getInitializePermanentDelegateInstructionDataCodec;
exports.getInitializePermanentDelegateInstructionDataDecoder = getInitializePermanentDelegateInstructionDataDecoder;
exports.getInitializePermanentDelegateInstructionDataEncoder = getInitializePermanentDelegateInstructionDataEncoder;
exports.getInitializeScaledUiAmountMintDiscriminatorBytes = getInitializeScaledUiAmountMintDiscriminatorBytes;
exports.getInitializeScaledUiAmountMintInstruction = getInitializeScaledUiAmountMintInstruction;
exports.getInitializeScaledUiAmountMintInstructionDataCodec = getInitializeScaledUiAmountMintInstructionDataCodec;
exports.getInitializeScaledUiAmountMintInstructionDataDecoder = getInitializeScaledUiAmountMintInstructionDataDecoder;
exports.getInitializeScaledUiAmountMintInstructionDataEncoder = getInitializeScaledUiAmountMintInstructionDataEncoder;
exports.getInitializeScaledUiAmountMintScaledUiAmountMintDiscriminatorBytes = getInitializeScaledUiAmountMintScaledUiAmountMintDiscriminatorBytes;
exports.getInitializeTokenGroupDiscriminatorBytes = getInitializeTokenGroupDiscriminatorBytes;
exports.getInitializeTokenGroupInstruction = getInitializeTokenGroupInstruction;
exports.getInitializeTokenGroupInstructionDataCodec = getInitializeTokenGroupInstructionDataCodec;
exports.getInitializeTokenGroupInstructionDataDecoder = getInitializeTokenGroupInstructionDataDecoder;
exports.getInitializeTokenGroupInstructionDataEncoder = getInitializeTokenGroupInstructionDataEncoder;
exports.getInitializeTokenGroupMemberDiscriminatorBytes = getInitializeTokenGroupMemberDiscriminatorBytes;
exports.getInitializeTokenGroupMemberInstruction = getInitializeTokenGroupMemberInstruction;
exports.getInitializeTokenGroupMemberInstructionDataCodec = getInitializeTokenGroupMemberInstructionDataCodec;
exports.getInitializeTokenGroupMemberInstructionDataDecoder = getInitializeTokenGroupMemberInstructionDataDecoder;
exports.getInitializeTokenGroupMemberInstructionDataEncoder = getInitializeTokenGroupMemberInstructionDataEncoder;
exports.getInitializeTokenMetadataDiscriminatorBytes = getInitializeTokenMetadataDiscriminatorBytes;
exports.getInitializeTokenMetadataInstruction = getInitializeTokenMetadataInstruction;
exports.getInitializeTokenMetadataInstructionDataCodec = getInitializeTokenMetadataInstructionDataCodec;
exports.getInitializeTokenMetadataInstructionDataDecoder = getInitializeTokenMetadataInstructionDataDecoder;
exports.getInitializeTokenMetadataInstructionDataEncoder = getInitializeTokenMetadataInstructionDataEncoder;
exports.getInitializeTransferFeeConfigDiscriminatorBytes = getInitializeTransferFeeConfigDiscriminatorBytes;
exports.getInitializeTransferFeeConfigInstruction = getInitializeTransferFeeConfigInstruction;
exports.getInitializeTransferFeeConfigInstructionDataCodec = getInitializeTransferFeeConfigInstructionDataCodec;
exports.getInitializeTransferFeeConfigInstructionDataDecoder = getInitializeTransferFeeConfigInstructionDataDecoder;
exports.getInitializeTransferFeeConfigInstructionDataEncoder = getInitializeTransferFeeConfigInstructionDataEncoder;
exports.getInitializeTransferFeeConfigTransferFeeDiscriminatorBytes = getInitializeTransferFeeConfigTransferFeeDiscriminatorBytes;
exports.getInitializeTransferHookDiscriminatorBytes = getInitializeTransferHookDiscriminatorBytes;
exports.getInitializeTransferHookInstruction = getInitializeTransferHookInstruction;
exports.getInitializeTransferHookInstructionDataCodec = getInitializeTransferHookInstructionDataCodec;
exports.getInitializeTransferHookInstructionDataDecoder = getInitializeTransferHookInstructionDataDecoder;
exports.getInitializeTransferHookInstructionDataEncoder = getInitializeTransferHookInstructionDataEncoder;
exports.getInitializeTransferHookTransferHookDiscriminatorBytes = getInitializeTransferHookTransferHookDiscriminatorBytes;
exports.getMintCodec = getMintCodec;
exports.getMintDecoder = getMintDecoder;
exports.getMintEncoder = getMintEncoder;
exports.getMintSize = getMintSize;
exports.getMintToCheckedDiscriminatorBytes = getMintToCheckedDiscriminatorBytes;
exports.getMintToCheckedInstruction = getMintToCheckedInstruction;
exports.getMintToCheckedInstructionDataCodec = getMintToCheckedInstructionDataCodec;
exports.getMintToCheckedInstructionDataDecoder = getMintToCheckedInstructionDataDecoder;
exports.getMintToCheckedInstructionDataEncoder = getMintToCheckedInstructionDataEncoder;
exports.getMintToDiscriminatorBytes = getMintToDiscriminatorBytes;
exports.getMintToInstruction = getMintToInstruction;
exports.getMintToInstructionDataCodec = getMintToInstructionDataCodec;
exports.getMintToInstructionDataDecoder = getMintToInstructionDataDecoder;
exports.getMintToInstructionDataEncoder = getMintToInstructionDataEncoder;
exports.getMultisigCodec = getMultisigCodec;
exports.getMultisigDecoder = getMultisigDecoder;
exports.getMultisigEncoder = getMultisigEncoder;
exports.getMultisigSize = getMultisigSize;
exports.getPauseDiscriminatorBytes = getPauseDiscriminatorBytes;
exports.getPauseInstruction = getPauseInstruction;
exports.getPauseInstructionDataCodec = getPauseInstructionDataCodec;
exports.getPauseInstructionDataDecoder = getPauseInstructionDataDecoder;
exports.getPauseInstructionDataEncoder = getPauseInstructionDataEncoder;
exports.getPausePausableDiscriminatorBytes = getPausePausableDiscriminatorBytes;
exports.getPostInitializeInstructionsForMintExtensions = getPostInitializeInstructionsForMintExtensions;
exports.getPostInitializeInstructionsForTokenExtensions = getPostInitializeInstructionsForTokenExtensions;
exports.getPreInitializeInstructionsForMintExtensions = getPreInitializeInstructionsForMintExtensions;
exports.getReallocateDiscriminatorBytes = getReallocateDiscriminatorBytes;
exports.getReallocateInstruction = getReallocateInstruction;
exports.getReallocateInstructionDataCodec = getReallocateInstructionDataCodec;
exports.getReallocateInstructionDataDecoder = getReallocateInstructionDataDecoder;
exports.getReallocateInstructionDataEncoder = getReallocateInstructionDataEncoder;
exports.getRecoverNestedAssociatedTokenDiscriminatorBytes = getRecoverNestedAssociatedTokenDiscriminatorBytes;
exports.getRecoverNestedAssociatedTokenInstruction = getRecoverNestedAssociatedTokenInstruction;
exports.getRecoverNestedAssociatedTokenInstructionAsync = getRecoverNestedAssociatedTokenInstructionAsync;
exports.getRecoverNestedAssociatedTokenInstructionDataCodec = getRecoverNestedAssociatedTokenInstructionDataCodec;
exports.getRecoverNestedAssociatedTokenInstructionDataDecoder = getRecoverNestedAssociatedTokenInstructionDataDecoder;
exports.getRecoverNestedAssociatedTokenInstructionDataEncoder = getRecoverNestedAssociatedTokenInstructionDataEncoder;
exports.getRemoveTokenMetadataKeyDiscriminatorBytes = getRemoveTokenMetadataKeyDiscriminatorBytes;
exports.getRemoveTokenMetadataKeyInstruction = getRemoveTokenMetadataKeyInstruction;
exports.getRemoveTokenMetadataKeyInstructionDataCodec = getRemoveTokenMetadataKeyInstructionDataCodec;
exports.getRemoveTokenMetadataKeyInstructionDataDecoder = getRemoveTokenMetadataKeyInstructionDataDecoder;
exports.getRemoveTokenMetadataKeyInstructionDataEncoder = getRemoveTokenMetadataKeyInstructionDataEncoder;
exports.getResumeDiscriminatorBytes = getResumeDiscriminatorBytes;
exports.getResumeInstruction = getResumeInstruction;
exports.getResumeInstructionDataCodec = getResumeInstructionDataCodec;
exports.getResumeInstructionDataDecoder = getResumeInstructionDataDecoder;
exports.getResumeInstructionDataEncoder = getResumeInstructionDataEncoder;
exports.getResumePausableDiscriminatorBytes = getResumePausableDiscriminatorBytes;
exports.getRevokeDiscriminatorBytes = getRevokeDiscriminatorBytes;
exports.getRevokeInstruction = getRevokeInstruction;
exports.getRevokeInstructionDataCodec = getRevokeInstructionDataCodec;
exports.getRevokeInstructionDataDecoder = getRevokeInstructionDataDecoder;
exports.getRevokeInstructionDataEncoder = getRevokeInstructionDataEncoder;
exports.getSetAuthorityDiscriminatorBytes = getSetAuthorityDiscriminatorBytes;
exports.getSetAuthorityInstruction = getSetAuthorityInstruction;
exports.getSetAuthorityInstructionDataCodec = getSetAuthorityInstructionDataCodec;
exports.getSetAuthorityInstructionDataDecoder = getSetAuthorityInstructionDataDecoder;
exports.getSetAuthorityInstructionDataEncoder = getSetAuthorityInstructionDataEncoder;
exports.getSetTransferFeeDiscriminatorBytes = getSetTransferFeeDiscriminatorBytes;
exports.getSetTransferFeeInstruction = getSetTransferFeeInstruction;
exports.getSetTransferFeeInstructionDataCodec = getSetTransferFeeInstructionDataCodec;
exports.getSetTransferFeeInstructionDataDecoder = getSetTransferFeeInstructionDataDecoder;
exports.getSetTransferFeeInstructionDataEncoder = getSetTransferFeeInstructionDataEncoder;
exports.getSetTransferFeeTransferFeeDiscriminatorBytes = getSetTransferFeeTransferFeeDiscriminatorBytes;
exports.getSyncNativeDiscriminatorBytes = getSyncNativeDiscriminatorBytes;
exports.getSyncNativeInstruction = getSyncNativeInstruction;
exports.getSyncNativeInstructionDataCodec = getSyncNativeInstructionDataCodec;
exports.getSyncNativeInstructionDataDecoder = getSyncNativeInstructionDataDecoder;
exports.getSyncNativeInstructionDataEncoder = getSyncNativeInstructionDataEncoder;
exports.getThawAccountDiscriminatorBytes = getThawAccountDiscriminatorBytes;
exports.getThawAccountInstruction = getThawAccountInstruction;
exports.getThawAccountInstructionDataCodec = getThawAccountInstructionDataCodec;
exports.getThawAccountInstructionDataDecoder = getThawAccountInstructionDataDecoder;
exports.getThawAccountInstructionDataEncoder = getThawAccountInstructionDataEncoder;
exports.getToken2022ErrorMessage = getToken2022ErrorMessage;
exports.getTokenCodec = getTokenCodec;
exports.getTokenDecoder = getTokenDecoder;
exports.getTokenEncoder = getTokenEncoder;
exports.getTokenMetadataFieldCodec = getTokenMetadataFieldCodec;
exports.getTokenMetadataFieldDecoder = getTokenMetadataFieldDecoder;
exports.getTokenMetadataFieldEncoder = getTokenMetadataFieldEncoder;
exports.getTokenSize = getTokenSize;
exports.getTransferCheckedDiscriminatorBytes = getTransferCheckedDiscriminatorBytes;
exports.getTransferCheckedInstruction = getTransferCheckedInstruction;
exports.getTransferCheckedInstructionDataCodec = getTransferCheckedInstructionDataCodec;
exports.getTransferCheckedInstructionDataDecoder = getTransferCheckedInstructionDataDecoder;
exports.getTransferCheckedInstructionDataEncoder = getTransferCheckedInstructionDataEncoder;
exports.getTransferCheckedWithFeeDiscriminatorBytes = getTransferCheckedWithFeeDiscriminatorBytes;
exports.getTransferCheckedWithFeeInstruction = getTransferCheckedWithFeeInstruction;
exports.getTransferCheckedWithFeeInstructionDataCodec = getTransferCheckedWithFeeInstructionDataCodec;
exports.getTransferCheckedWithFeeInstructionDataDecoder = getTransferCheckedWithFeeInstructionDataDecoder;
exports.getTransferCheckedWithFeeInstructionDataEncoder = getTransferCheckedWithFeeInstructionDataEncoder;
exports.getTransferCheckedWithFeeTransferFeeDiscriminatorBytes = getTransferCheckedWithFeeTransferFeeDiscriminatorBytes;
exports.getTransferDiscriminatorBytes = getTransferDiscriminatorBytes;
exports.getTransferFeeCodec = getTransferFeeCodec;
exports.getTransferFeeDecoder = getTransferFeeDecoder;
exports.getTransferFeeEncoder = getTransferFeeEncoder;
exports.getTransferInstruction = getTransferInstruction;
exports.getTransferInstructionDataCodec = getTransferInstructionDataCodec;
exports.getTransferInstructionDataDecoder = getTransferInstructionDataDecoder;
exports.getTransferInstructionDataEncoder = getTransferInstructionDataEncoder;
exports.getUiAmountToAmountDiscriminatorBytes = getUiAmountToAmountDiscriminatorBytes;
exports.getUiAmountToAmountInstruction = getUiAmountToAmountInstruction;
exports.getUiAmountToAmountInstructionDataCodec = getUiAmountToAmountInstructionDataCodec;
exports.getUiAmountToAmountInstructionDataDecoder = getUiAmountToAmountInstructionDataDecoder;
exports.getUiAmountToAmountInstructionDataEncoder = getUiAmountToAmountInstructionDataEncoder;
exports.getUpdateConfidentialTransferMintConfidentialTransferDiscriminatorBytes = getUpdateConfidentialTransferMintConfidentialTransferDiscriminatorBytes;
exports.getUpdateConfidentialTransferMintDiscriminatorBytes = getUpdateConfidentialTransferMintDiscriminatorBytes;
exports.getUpdateConfidentialTransferMintInstruction = getUpdateConfidentialTransferMintInstruction;
exports.getUpdateConfidentialTransferMintInstructionDataCodec = getUpdateConfidentialTransferMintInstructionDataCodec;
exports.getUpdateConfidentialTransferMintInstructionDataDecoder = getUpdateConfidentialTransferMintInstructionDataDecoder;
exports.getUpdateConfidentialTransferMintInstructionDataEncoder = getUpdateConfidentialTransferMintInstructionDataEncoder;
exports.getUpdateDefaultAccountStateDefaultAccountStateDiscriminatorBytes = getUpdateDefaultAccountStateDefaultAccountStateDiscriminatorBytes;
exports.getUpdateDefaultAccountStateDiscriminatorBytes = getUpdateDefaultAccountStateDiscriminatorBytes;
exports.getUpdateDefaultAccountStateInstruction = getUpdateDefaultAccountStateInstruction;
exports.getUpdateDefaultAccountStateInstructionDataCodec = getUpdateDefaultAccountStateInstructionDataCodec;
exports.getUpdateDefaultAccountStateInstructionDataDecoder = getUpdateDefaultAccountStateInstructionDataDecoder;
exports.getUpdateDefaultAccountStateInstructionDataEncoder = getUpdateDefaultAccountStateInstructionDataEncoder;
exports.getUpdateGroupMemberPointerDiscriminatorBytes = getUpdateGroupMemberPointerDiscriminatorBytes;
exports.getUpdateGroupMemberPointerGroupMemberPointerDiscriminatorBytes = getUpdateGroupMemberPointerGroupMemberPointerDiscriminatorBytes;
exports.getUpdateGroupMemberPointerInstruction = getUpdateGroupMemberPointerInstruction;
exports.getUpdateGroupMemberPointerInstructionDataCodec = getUpdateGroupMemberPointerInstructionDataCodec;
exports.getUpdateGroupMemberPointerInstructionDataDecoder = getUpdateGroupMemberPointerInstructionDataDecoder;
exports.getUpdateGroupMemberPointerInstructionDataEncoder = getUpdateGroupMemberPointerInstructionDataEncoder;
exports.getUpdateGroupPointerDiscriminatorBytes = getUpdateGroupPointerDiscriminatorBytes;
exports.getUpdateGroupPointerGroupPointerDiscriminatorBytes = getUpdateGroupPointerGroupPointerDiscriminatorBytes;
exports.getUpdateGroupPointerInstruction = getUpdateGroupPointerInstruction;
exports.getUpdateGroupPointerInstructionDataCodec = getUpdateGroupPointerInstructionDataCodec;
exports.getUpdateGroupPointerInstructionDataDecoder = getUpdateGroupPointerInstructionDataDecoder;
exports.getUpdateGroupPointerInstructionDataEncoder = getUpdateGroupPointerInstructionDataEncoder;
exports.getUpdateMetadataPointerDiscriminatorBytes = getUpdateMetadataPointerDiscriminatorBytes;
exports.getUpdateMetadataPointerInstruction = getUpdateMetadataPointerInstruction;
exports.getUpdateMetadataPointerInstructionDataCodec = getUpdateMetadataPointerInstructionDataCodec;
exports.getUpdateMetadataPointerInstructionDataDecoder = getUpdateMetadataPointerInstructionDataDecoder;
exports.getUpdateMetadataPointerInstructionDataEncoder = getUpdateMetadataPointerInstructionDataEncoder;
exports.getUpdateMetadataPointerMetadataPointerDiscriminatorBytes = getUpdateMetadataPointerMetadataPointerDiscriminatorBytes;
exports.getUpdateMultiplierScaledUiMintDiscriminatorBytes = getUpdateMultiplierScaledUiMintDiscriminatorBytes;
exports.getUpdateMultiplierScaledUiMintInstruction = getUpdateMultiplierScaledUiMintInstruction;
exports.getUpdateMultiplierScaledUiMintInstructionDataCodec = getUpdateMultiplierScaledUiMintInstructionDataCodec;
exports.getUpdateMultiplierScaledUiMintInstructionDataDecoder = getUpdateMultiplierScaledUiMintInstructionDataDecoder;
exports.getUpdateMultiplierScaledUiMintInstructionDataEncoder = getUpdateMultiplierScaledUiMintInstructionDataEncoder;
exports.getUpdateMultiplierScaledUiMintScaledUiAmountMintDiscriminatorBytes = getUpdateMultiplierScaledUiMintScaledUiAmountMintDiscriminatorBytes;
exports.getUpdateRateInterestBearingMintDiscriminatorBytes = getUpdateRateInterestBearingMintDiscriminatorBytes;
exports.getUpdateRateInterestBearingMintInstruction = getUpdateRateInterestBearingMintInstruction;
exports.getUpdateRateInterestBearingMintInstructionDataCodec = getUpdateRateInterestBearingMintInstructionDataCodec;
exports.getUpdateRateInterestBearingMintInstructionDataDecoder = getUpdateRateInterestBearingMintInstructionDataDecoder;
exports.getUpdateRateInterestBearingMintInstructionDataEncoder = getUpdateRateInterestBearingMintInstructionDataEncoder;
exports.getUpdateRateInterestBearingMintInterestBearingMintDiscriminatorBytes = getUpdateRateInterestBearingMintInterestBearingMintDiscriminatorBytes;
exports.getUpdateTokenGroupMaxSizeDiscriminatorBytes = getUpdateTokenGroupMaxSizeDiscriminatorBytes;
exports.getUpdateTokenGroupMaxSizeInstruction = getUpdateTokenGroupMaxSizeInstruction;
exports.getUpdateTokenGroupMaxSizeInstructionDataCodec = getUpdateTokenGroupMaxSizeInstructionDataCodec;
exports.getUpdateTokenGroupMaxSizeInstructionDataDecoder = getUpdateTokenGroupMaxSizeInstructionDataDecoder;
exports.getUpdateTokenGroupMaxSizeInstructionDataEncoder = getUpdateTokenGroupMaxSizeInstructionDataEncoder;
exports.getUpdateTokenGroupUpdateAuthorityDiscriminatorBytes = getUpdateTokenGroupUpdateAuthorityDiscriminatorBytes;
exports.getUpdateTokenGroupUpdateAuthorityInstruction = getUpdateTokenGroupUpdateAuthorityInstruction;
exports.getUpdateTokenGroupUpdateAuthorityInstructionDataCodec = getUpdateTokenGroupUpdateAuthorityInstructionDataCodec;
exports.getUpdateTokenGroupUpdateAuthorityInstructionDataDecoder = getUpdateTokenGroupUpdateAuthorityInstructionDataDecoder;
exports.getUpdateTokenGroupUpdateAuthorityInstructionDataEncoder = getUpdateTokenGroupUpdateAuthorityInstructionDataEncoder;
exports.getUpdateTokenMetadataFieldDiscriminatorBytes = getUpdateTokenMetadataFieldDiscriminatorBytes;
exports.getUpdateTokenMetadataFieldInstruction = getUpdateTokenMetadataFieldInstruction;
exports.getUpdateTokenMetadataFieldInstructionDataCodec = getUpdateTokenMetadataFieldInstructionDataCodec;
exports.getUpdateTokenMetadataFieldInstructionDataDecoder = getUpdateTokenMetadataFieldInstructionDataDecoder;
exports.getUpdateTokenMetadataFieldInstructionDataEncoder = getUpdateTokenMetadataFieldInstructionDataEncoder;
exports.getUpdateTokenMetadataUpdateAuthorityDiscriminatorBytes = getUpdateTokenMetadataUpdateAuthorityDiscriminatorBytes;
exports.getUpdateTokenMetadataUpdateAuthorityInstruction = getUpdateTokenMetadataUpdateAuthorityInstruction;
exports.getUpdateTokenMetadataUpdateAuthorityInstructionDataCodec = getUpdateTokenMetadataUpdateAuthorityInstructionDataCodec;
exports.getUpdateTokenMetadataUpdateAuthorityInstructionDataDecoder = getUpdateTokenMetadataUpdateAuthorityInstructionDataDecoder;
exports.getUpdateTokenMetadataUpdateAuthorityInstructionDataEncoder = getUpdateTokenMetadataUpdateAuthorityInstructionDataEncoder;
exports.getUpdateTransferHookDiscriminatorBytes = getUpdateTransferHookDiscriminatorBytes;
exports.getUpdateTransferHookInstruction = getUpdateTransferHookInstruction;
exports.getUpdateTransferHookInstructionDataCodec = getUpdateTransferHookInstructionDataCodec;
exports.getUpdateTransferHookInstructionDataDecoder = getUpdateTransferHookInstructionDataDecoder;
exports.getUpdateTransferHookInstructionDataEncoder = getUpdateTransferHookInstructionDataEncoder;
exports.getUpdateTransferHookTransferHookDiscriminatorBytes = getUpdateTransferHookTransferHookDiscriminatorBytes;
exports.getWithdrawExcessLamportsDiscriminatorBytes = getWithdrawExcessLamportsDiscriminatorBytes;
exports.getWithdrawExcessLamportsInstruction = getWithdrawExcessLamportsInstruction;
exports.getWithdrawExcessLamportsInstructionDataCodec = getWithdrawExcessLamportsInstructionDataCodec;
exports.getWithdrawExcessLamportsInstructionDataDecoder = getWithdrawExcessLamportsInstructionDataDecoder;
exports.getWithdrawExcessLamportsInstructionDataEncoder = getWithdrawExcessLamportsInstructionDataEncoder;
exports.getWithdrawWithheldTokensFromAccountsDiscriminatorBytes = getWithdrawWithheldTokensFromAccountsDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataCodec = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataCodec;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataDecoder;
exports.getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder = getWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstructionDataEncoder;
exports.getWithdrawWithheldTokensFromAccountsInstruction = getWithdrawWithheldTokensFromAccountsInstruction;
exports.getWithdrawWithheldTokensFromAccountsInstructionDataCodec = getWithdrawWithheldTokensFromAccountsInstructionDataCodec;
exports.getWithdrawWithheldTokensFromAccountsInstructionDataDecoder = getWithdrawWithheldTokensFromAccountsInstructionDataDecoder;
exports.getWithdrawWithheldTokensFromAccountsInstructionDataEncoder = getWithdrawWithheldTokensFromAccountsInstructionDataEncoder;
exports.getWithdrawWithheldTokensFromAccountsTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromAccountsTransferFeeDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromMintDiscriminatorBytes = getWithdrawWithheldTokensFromMintDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeConfidentialTransferFeeDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeDiscriminatorBytes;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataCodec = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataCodec;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataDecoder = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataDecoder;
exports.getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataEncoder = getWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstructionDataEncoder;
exports.getWithdrawWithheldTokensFromMintInstruction = getWithdrawWithheldTokensFromMintInstruction;
exports.getWithdrawWithheldTokensFromMintInstructionDataCodec = getWithdrawWithheldTokensFromMintInstructionDataCodec;
exports.getWithdrawWithheldTokensFromMintInstructionDataDecoder = getWithdrawWithheldTokensFromMintInstructionDataDecoder;
exports.getWithdrawWithheldTokensFromMintInstructionDataEncoder = getWithdrawWithheldTokensFromMintInstructionDataEncoder;
exports.getWithdrawWithheldTokensFromMintTransferFeeDiscriminatorBytes = getWithdrawWithheldTokensFromMintTransferFeeDiscriminatorBytes;
exports.identifyAssociatedTokenInstruction = identifyAssociatedTokenInstruction;
exports.identifyToken2022Account = identifyToken2022Account;
exports.identifyToken2022Instruction = identifyToken2022Instruction;
exports.isAssociatedTokenError = isAssociatedTokenError;
exports.isExtension = isExtension;
exports.isToken2022Error = isToken2022Error;
exports.isTokenMetadataField = isTokenMetadataField;
exports.parseAmountToUiAmountInstruction = parseAmountToUiAmountInstruction;
exports.parseApplyConfidentialPendingBalanceInstruction = parseApplyConfidentialPendingBalanceInstruction;
exports.parseApproveCheckedInstruction = parseApproveCheckedInstruction;
exports.parseApproveConfidentialTransferAccountInstruction = parseApproveConfidentialTransferAccountInstruction;
exports.parseApproveInstruction = parseApproveInstruction;
exports.parseBurnCheckedInstruction = parseBurnCheckedInstruction;
exports.parseBurnInstruction = parseBurnInstruction;
exports.parseCloseAccountInstruction = parseCloseAccountInstruction;
exports.parseConfidentialDepositInstruction = parseConfidentialDepositInstruction;
exports.parseConfidentialTransferInstruction = parseConfidentialTransferInstruction;
exports.parseConfidentialTransferWithFeeInstruction = parseConfidentialTransferWithFeeInstruction;
exports.parseConfidentialWithdrawInstruction = parseConfidentialWithdrawInstruction;
exports.parseConfigureConfidentialTransferAccountInstruction = parseConfigureConfidentialTransferAccountInstruction;
exports.parseCreateAssociatedTokenIdempotentInstruction = parseCreateAssociatedTokenIdempotentInstruction;
exports.parseCreateAssociatedTokenInstruction = parseCreateAssociatedTokenInstruction;
exports.parseCreateNativeMintInstruction = parseCreateNativeMintInstruction;
exports.parseDisableConfidentialCreditsInstruction = parseDisableConfidentialCreditsInstruction;
exports.parseDisableCpiGuardInstruction = parseDisableCpiGuardInstruction;
exports.parseDisableHarvestToMintInstruction = parseDisableHarvestToMintInstruction;
exports.parseDisableMemoTransfersInstruction = parseDisableMemoTransfersInstruction;
exports.parseDisableNonConfidentialCreditsInstruction = parseDisableNonConfidentialCreditsInstruction;
exports.parseEmitTokenMetadataInstruction = parseEmitTokenMetadataInstruction;
exports.parseEmptyConfidentialTransferAccountInstruction = parseEmptyConfidentialTransferAccountInstruction;
exports.parseEnableConfidentialCreditsInstruction = parseEnableConfidentialCreditsInstruction;
exports.parseEnableCpiGuardInstruction = parseEnableCpiGuardInstruction;
exports.parseEnableHarvestToMintInstruction = parseEnableHarvestToMintInstruction;
exports.parseEnableMemoTransfersInstruction = parseEnableMemoTransfersInstruction;
exports.parseEnableNonConfidentialCreditsInstruction = parseEnableNonConfidentialCreditsInstruction;
exports.parseFreezeAccountInstruction = parseFreezeAccountInstruction;
exports.parseGetAccountDataSizeInstruction = parseGetAccountDataSizeInstruction;
exports.parseHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction = parseHarvestWithheldTokensToMintForConfidentialTransferFeeInstruction;
exports.parseHarvestWithheldTokensToMintInstruction = parseHarvestWithheldTokensToMintInstruction;
exports.parseInitializeAccount2Instruction = parseInitializeAccount2Instruction;
exports.parseInitializeAccount3Instruction = parseInitializeAccount3Instruction;
exports.parseInitializeAccountInstruction = parseInitializeAccountInstruction;
exports.parseInitializeConfidentialTransferFeeInstruction = parseInitializeConfidentialTransferFeeInstruction;
exports.parseInitializeConfidentialTransferMintInstruction = parseInitializeConfidentialTransferMintInstruction;
exports.parseInitializeDefaultAccountStateInstruction = parseInitializeDefaultAccountStateInstruction;
exports.parseInitializeGroupMemberPointerInstruction = parseInitializeGroupMemberPointerInstruction;
exports.parseInitializeGroupPointerInstruction = parseInitializeGroupPointerInstruction;
exports.parseInitializeImmutableOwnerInstruction = parseInitializeImmutableOwnerInstruction;
exports.parseInitializeInterestBearingMintInstruction = parseInitializeInterestBearingMintInstruction;
exports.parseInitializeMetadataPointerInstruction = parseInitializeMetadataPointerInstruction;
exports.parseInitializeMint2Instruction = parseInitializeMint2Instruction;
exports.parseInitializeMintCloseAuthorityInstruction = parseInitializeMintCloseAuthorityInstruction;
exports.parseInitializeMintInstruction = parseInitializeMintInstruction;
exports.parseInitializeMultisig2Instruction = parseInitializeMultisig2Instruction;
exports.parseInitializeMultisigInstruction = parseInitializeMultisigInstruction;
exports.parseInitializeNonTransferableMintInstruction = parseInitializeNonTransferableMintInstruction;
exports.parseInitializePausableConfigInstruction = parseInitializePausableConfigInstruction;
exports.parseInitializePermanentDelegateInstruction = parseInitializePermanentDelegateInstruction;
exports.parseInitializeScaledUiAmountMintInstruction = parseInitializeScaledUiAmountMintInstruction;
exports.parseInitializeTokenGroupInstruction = parseInitializeTokenGroupInstruction;
exports.parseInitializeTokenGroupMemberInstruction = parseInitializeTokenGroupMemberInstruction;
exports.parseInitializeTokenMetadataInstruction = parseInitializeTokenMetadataInstruction;
exports.parseInitializeTransferFeeConfigInstruction = parseInitializeTransferFeeConfigInstruction;
exports.parseInitializeTransferHookInstruction = parseInitializeTransferHookInstruction;
exports.parseMintToCheckedInstruction = parseMintToCheckedInstruction;
exports.parseMintToInstruction = parseMintToInstruction;
exports.parsePauseInstruction = parsePauseInstruction;
exports.parseReallocateInstruction = parseReallocateInstruction;
exports.parseRecoverNestedAssociatedTokenInstruction = parseRecoverNestedAssociatedTokenInstruction;
exports.parseRemoveTokenMetadataKeyInstruction = parseRemoveTokenMetadataKeyInstruction;
exports.parseResumeInstruction = parseResumeInstruction;
exports.parseRevokeInstruction = parseRevokeInstruction;
exports.parseSetAuthorityInstruction = parseSetAuthorityInstruction;
exports.parseSetTransferFeeInstruction = parseSetTransferFeeInstruction;
exports.parseSyncNativeInstruction = parseSyncNativeInstruction;
exports.parseThawAccountInstruction = parseThawAccountInstruction;
exports.parseTransferCheckedInstruction = parseTransferCheckedInstruction;
exports.parseTransferCheckedWithFeeInstruction = parseTransferCheckedWithFeeInstruction;
exports.parseTransferInstruction = parseTransferInstruction;
exports.parseUiAmountToAmountInstruction = parseUiAmountToAmountInstruction;
exports.parseUpdateConfidentialTransferMintInstruction = parseUpdateConfidentialTransferMintInstruction;
exports.parseUpdateDefaultAccountStateInstruction = parseUpdateDefaultAccountStateInstruction;
exports.parseUpdateGroupMemberPointerInstruction = parseUpdateGroupMemberPointerInstruction;
exports.parseUpdateGroupPointerInstruction = parseUpdateGroupPointerInstruction;
exports.parseUpdateMetadataPointerInstruction = parseUpdateMetadataPointerInstruction;
exports.parseUpdateMultiplierScaledUiMintInstruction = parseUpdateMultiplierScaledUiMintInstruction;
exports.parseUpdateRateInterestBearingMintInstruction = parseUpdateRateInterestBearingMintInstruction;
exports.parseUpdateTokenGroupMaxSizeInstruction = parseUpdateTokenGroupMaxSizeInstruction;
exports.parseUpdateTokenGroupUpdateAuthorityInstruction = parseUpdateTokenGroupUpdateAuthorityInstruction;
exports.parseUpdateTokenMetadataFieldInstruction = parseUpdateTokenMetadataFieldInstruction;
exports.parseUpdateTokenMetadataUpdateAuthorityInstruction = parseUpdateTokenMetadataUpdateAuthorityInstruction;
exports.parseUpdateTransferHookInstruction = parseUpdateTransferHookInstruction;
exports.parseWithdrawExcessLamportsInstruction = parseWithdrawExcessLamportsInstruction;
exports.parseWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction = parseWithdrawWithheldTokensFromAccountsForConfidentialTransferFeeInstruction;
exports.parseWithdrawWithheldTokensFromAccountsInstruction = parseWithdrawWithheldTokensFromAccountsInstruction;
exports.parseWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction = parseWithdrawWithheldTokensFromMintForConfidentialTransferFeeInstruction;
exports.parseWithdrawWithheldTokensFromMintInstruction = parseWithdrawWithheldTokensFromMintInstruction;
exports.tokenMetadataField = tokenMetadataField;
exports.uiAmountToAmountForInterestBearingMintWithoutSimulation = uiAmountToAmountForInterestBearingMintWithoutSimulation;
exports.uiAmountToAmountForMintWithoutSimulation = uiAmountToAmountForMintWithoutSimulation;
exports.uiAmountToAmountForScaledUiAmountMintWithoutSimulation = uiAmountToAmountForScaledUiAmountMintWithoutSimulation;
//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map