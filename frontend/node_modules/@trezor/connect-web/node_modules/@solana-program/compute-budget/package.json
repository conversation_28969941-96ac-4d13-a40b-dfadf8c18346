{"name": "@solana-program/compute-budget", "version": "0.8.0", "description": "JavaScript client for the Compute Budget program", "sideEffects": false, "module": "./dist/src/index.mjs", "main": "./dist/src/index.js", "types": "./dist/types/index.d.ts", "type": "commonjs", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/src/index.mjs", "require": "./dist/src/index.js"}}, "files": ["./dist/src", "./dist/types"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/solana-program/compute-budget.git"}, "bugs": {"url": "https://github.com/solana-program/compute-budget/issues"}, "homepage": "https://github.com/solana-program/compute-budget#readme", "peerDependencies": {"@solana/kit": "^2.1.0"}, "devDependencies": {"@solana/eslint-config-solana": "^3.0.3", "@solana/kit": "^2.1.0", "@types/node": "^20", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "eslint": "^8.57.0", "prettier": "^3.3.3", "rimraf": "^5.0.5", "tsup": "^8.1.2", "typedoc": "^0.25.12", "typescript": "^5.5.3", "vitest": "^3.1.3"}, "scripts": {"build": "rimraf dist && tsup && tsc -p ./tsconfig.declarations.json", "build:docs": "typedoc", "dev": "vitest", "test": "vitest run", "lint": "eslint --ext js,ts,tsx src", "lint:fix": "eslint --fix --ext js,ts,tsx src", "format": "prettier --check src test", "format:fix": "prettier --write src test"}}