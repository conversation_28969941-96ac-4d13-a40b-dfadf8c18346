{"version": 3, "file": "allocate.d.ts", "sourceRoot": "", "sources": ["../../../../src/generated/instructions/allocate.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EASL,KAAK,OAAO,EACZ,KAAK,KAAK,EACV,KAAK,OAAO,EACZ,KAAK,OAAO,EACZ,KAAK,YAAY,EACjB,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,wBAAwB,EAC7B,KAAK,oBAAoB,EACzB,KAAK,iBAAiB,EACtB,KAAK,qBAAqB,EAC3B,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,sBAAsB,EAAE,MAAM,aAAa,CAAC;AAGrD,eAAO,MAAM,sBAAsB,IAAI,CAAC;AAExC,wBAAgB,6BAA6B,6CAE5C;AAED,MAAM,MAAM,mBAAmB,CAC7B,QAAQ,SAAS,MAAM,GAAG,OAAO,sBAAsB,EACvD,kBAAkB,SAAS,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,EACjE,kBAAkB,SAAS,SAAS,YAAY,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,IAC7D,YAAY,CAAC,QAAQ,CAAC,GACxB,oBAAoB,CAAC,UAAU,CAAC,GAChC,wBAAwB,CACtB;IACE,kBAAkB,SAAS,MAAM,GAC7B,qBAAqB,CAAC,kBAAkB,CAAC,GACvC,kBAAkB,CAAC,kBAAkB,CAAC,GACxC,kBAAkB;IACtB,GAAG,kBAAkB;CACtB,CACF,CAAC;AAEJ,MAAM,MAAM,uBAAuB,GAAG;IAAE,aAAa,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC;AAE/E,MAAM,MAAM,2BAA2B,GAAG;IAAE,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;CAAE,CAAC;AAErE,wBAAgB,iCAAiC,IAAI,OAAO,CAAC,2BAA2B,CAAC,CAQxF;AAED,wBAAgB,iCAAiC,IAAI,OAAO,CAAC,uBAAuB,CAAC,CAKpF;AAED,wBAAgB,+BAA+B,IAAI,KAAK,CACtD,2BAA2B,EAC3B,uBAAuB,CACxB,CAKA;AAED,MAAM,MAAM,aAAa,CAAC,kBAAkB,SAAS,MAAM,GAAG,MAAM,IAAI;IACtE,UAAU,EAAE,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IAClD,KAAK,EAAE,2BAA2B,CAAC,OAAO,CAAC,CAAC;CAC7C,CAAC;AAEF,wBAAgB,sBAAsB,CACpC,kBAAkB,SAAS,MAAM,EACjC,eAAe,SAAS,OAAO,GAAG,OAAO,sBAAsB,EAE/D,KAAK,EAAE,aAAa,CAAC,kBAAkB,CAAC,EACxC,MAAM,CAAC,EAAE;IAAE,cAAc,CAAC,EAAE,eAAe,CAAA;CAAE,GAC5C,mBAAmB,CAAC,eAAe,EAAE,kBAAkB,CAAC,CA0B1D;AAED,MAAM,MAAM,yBAAyB,CACnC,QAAQ,SAAS,MAAM,GAAG,OAAO,sBAAsB,EACvD,aAAa,SAAS,SAAS,YAAY,EAAE,GAAG,SAAS,YAAY,EAAE,IACrE;IACF,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAClC,QAAQ,EAAE;QACR,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;KAC9B,CAAC;IACF,IAAI,EAAE,uBAAuB,CAAC;CAC/B,CAAC;AAEF,wBAAgB,wBAAwB,CACtC,QAAQ,SAAS,MAAM,EACvB,aAAa,SAAS,SAAS,YAAY,EAAE,EAE7C,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,GACjC,wBAAwB,CAAC,aAAa,CAAC,GACvC,oBAAoB,CAAC,UAAU,CAAC,GACjC,yBAAyB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAkBpD"}