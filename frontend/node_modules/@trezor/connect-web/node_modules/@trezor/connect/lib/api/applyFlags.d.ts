import { PROTO } from '../constants';
import { AbstractMethod } from '../core/AbstractMethod';
export default class ApplyFlags extends AbstractMethod<'applyFlags', PROTO.ApplyFlags> {
    init(): void;
    get confirmation(): {
        view: "device-management";
        customConfirmButton: {
            className: string;
            label: string;
        };
        label: string;
    };
    run(): Promise<{
        message: string;
    }>;
}
//# sourceMappingURL=applyFlags.d.ts.map