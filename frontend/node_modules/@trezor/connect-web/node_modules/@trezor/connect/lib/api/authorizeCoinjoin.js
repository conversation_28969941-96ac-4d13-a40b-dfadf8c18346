"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const schema_utils_1 = require("@trezor/schema-utils");
const AbstractMethod_1 = require("../core/AbstractMethod");
const paramsValidator_1 = require("./common/paramsValidator");
const coinInfo_1 = require("../data/coinInfo");
const authorizeCoinjoin_1 = require("../types/api/authorizeCoinjoin");
const pathUtils_1 = require("../utils/pathUtils");
class AuthorizeCoinjoin extends AbstractMethod_1.AbstractMethod {
    init() {
        const { payload } = this;
        (0, schema_utils_1.Assert)(authorizeCoinjoin_1.AuthorizeCoinjoin, payload);
        const address_n = (0, pathUtils_1.validatePath)(payload.path, 3);
        const script_type = payload.scriptType || (0, pathUtils_1.getScriptType)(address_n);
        const coinInfo = (0, coinInfo_1.getBitcoinNetwork)(payload.coin || address_n);
        this.firmwareRange = (0, paramsValidator_1.getFirmwareRange)(this.name, coinInfo, this.firmwareRange);
        this.preauthorized = payload.preauthorized;
        this.params = {
            coordinator: payload.coordinator,
            max_rounds: payload.maxRounds,
            max_coordinator_fee_rate: payload.maxCoordinatorFeeRate,
            max_fee_per_kvbyte: payload.maxFeePerKvbyte,
            address_n,
            coin_name: coinInfo?.name,
            script_type,
            amount_unit: payload.amountUnit,
        };
    }
    async run() {
        const cmd = this.device.getCommands();
        if (this.preauthorized) {
            if (await cmd.preauthorize(false)) {
                return { message: 'Success' };
            }
        }
        const response = await cmd.typedCall('AuthorizeCoinJoin', 'Success', this.params);
        return response.message;
    }
}
exports.default = AuthorizeCoinjoin;
//# sourceMappingURL=authorizeCoinjoin.js.map