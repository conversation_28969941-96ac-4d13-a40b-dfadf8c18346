import { PROTO } from '../constants';
import { AbstractMethod } from '../core/AbstractMethod';
export default class BackupDevice extends AbstractMethod<'backupDevice', PROTO.BackupDevice> {
    init(): void;
    get confirmation(): {
        view: "device-management";
        customConfirmButton: {
            className: string;
            label: string;
        };
        label: string;
    };
    run(): Promise<{
        message: string;
    }>;
}
//# sourceMappingURL=backupDevice.d.ts.map