"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const schema_utils_1 = require("@trezor/schema-utils");
const constants_1 = require("../constants");
const AbstractMethod_1 = require("../core/AbstractMethod");
class ApplyFlags extends AbstractMethod_1.AbstractMethod {
    init() {
        this.requiredPermissions = ['management'];
        this.useDeviceState = false;
        const { payload } = this;
        (0, schema_utils_1.Assert)(constants_1.PROTO.ApplyFlags, payload);
        this.params = {
            flags: payload.flags,
        };
    }
    get confirmation() {
        return {
            view: 'device-management',
            customConfirmButton: {
                className: 'confirm',
                label: 'Proceed',
            },
            label: 'Do you really want to apply flags?',
        };
    }
    async run() {
        const cmd = this.device.getCommands();
        const response = await cmd.typedCall('ApplyFlags', 'Success', this.params);
        return response.message;
    }
}
exports.default = ApplyFlags;
//# sourceMappingURL=applyFlags.js.map