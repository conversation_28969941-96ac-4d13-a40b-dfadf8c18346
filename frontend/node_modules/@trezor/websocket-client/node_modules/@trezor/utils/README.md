# @trezor/utils

[![NPM](https://img.shields.io/npm/v/@trezor/utils.svg)](https://www.npmjs.org/package/@trezor/utils)

A collection of typescript utils that are intended to be used across trezor-suite monorepo.

## Publishing

This package is published to npm registry because it is a dependency of [@trezor/connect](https://github.com/trezor/trezor-suite/issues/5440) which can be installed as a standalone package.

[Follow instructions](../../docs/releases/npm-packages.md) how to publish @trezor package to npm registry.
