"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.arrayToDictionary = void 0;
const validateKey = (key) => {
    if (['string', 'number'].includes(typeof key)) {
        return true;
    }
    return false;
};
const arrayToDictionary = (array, getKey, multiple) => multiple
    ? array.reduce((prev, cur) => {
        const key = getKey(cur);
        if (validate<PERSON><PERSON>(key)) {
            return {
                ...prev,
                [key]: [...(prev[key] ?? []), cur],
            };
        }
        return prev;
    }, {})
    : array.reduce((prev, cur) => {
        const key = getKey(cur);
        if (validate<PERSON><PERSON>(key)) {
            return {
                ...prev,
                [key]: cur,
            };
        }
        return prev;
    }, {});
exports.arrayToDictionary = arrayToDictionary;
//# sourceMappingURL=arrayToDictionary.js.map