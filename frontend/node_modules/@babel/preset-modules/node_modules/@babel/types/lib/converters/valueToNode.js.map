{"version": 3, "names": ["_isValidIdentifier", "require", "_index", "_default", "exports", "default", "valueToNode", "objectToString", "Function", "call", "bind", "Object", "prototype", "toString", "isRegExp", "value", "isPlainObject", "proto", "getPrototypeOf", "undefined", "identifier", "booleanLiteral", "nullLiteral", "stringLiteral", "result", "Number", "isFinite", "numericLiteral", "Math", "abs", "numerator", "isNaN", "binaryExpression", "is", "unaryExpression", "bigIntLiteral", "pattern", "source", "flags", "exec", "regExpLiteral", "Array", "isArray", "arrayExpression", "map", "props", "key", "keys", "nodeKey", "computed", "isValidIdentifier", "push", "objectProperty", "objectExpression", "Error"], "sources": ["../../src/converters/valueToNode.ts"], "sourcesContent": ["import isValidIdentifier from \"../validators/isValidIdentifier.ts\";\nimport {\n  identifier,\n  booleanLiteral,\n  nullLiteral,\n  stringLiteral,\n  numericLiteral,\n  bigIntLiteral,\n  regExpLiteral,\n  arrayExpression,\n  objectProperty,\n  objectExpression,\n  unaryExpression,\n  binaryExpression,\n} from \"../builders/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default valueToNode as {\n  (value: undefined): t.Identifier; // TODO: This should return \"void 0\"\n  (value: boolean): t.<PERSON>iteral;\n  (value: null): t.NullLiteral;\n  (value: string): t.StringLiteral;\n  // Infinities and NaN need to use a BinaryExpression; negative values must be wrapped in UnaryExpression\n  (value: number): t.NumericLiteral | t.BinaryExpression | t.UnaryExpression;\n  (value: bigint): t.BigIntLiteral;\n  (value: RegExp): t.RegExpLiteral;\n  (value: ReadonlyArray<unknown>): t.ArrayExpression;\n\n  // this throws with objects that are not plain objects,\n  // or if there are non-valueToNode-able values\n  (value: object): t.ObjectExpression;\n\n  (value: unknown): t.Expression;\n};\n\n// @ts-expect-error: Object.prototype.toString must return a string\nconst objectToString: (value: unknown) => string = Function.call.bind(\n  Object.prototype.toString,\n);\n\nfunction isRegExp(value: unknown): value is RegExp {\n  return objectToString(value) === \"[object RegExp]\";\n}\n\nfunction isPlainObject(value: unknown): value is object {\n  if (\n    typeof value !== \"object\" ||\n    value === null ||\n    Object.prototype.toString.call(value) !== \"[object Object]\"\n  ) {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  // Object.prototype's __proto__ is null. Every other class's __proto__.__proto__ is\n  // not null by default. We cannot check if proto === Object.prototype because it\n  // could come from another realm.\n  return proto === null || Object.getPrototypeOf(proto) === null;\n}\n\nfunction valueToNode(value: unknown): t.Expression {\n  // undefined\n  if (value === undefined) {\n    return identifier(\"undefined\");\n  }\n\n  // boolean\n  if (value === true || value === false) {\n    return booleanLiteral(value);\n  }\n\n  // null\n  if (value === null) {\n    return nullLiteral();\n  }\n\n  // strings\n  if (typeof value === \"string\") {\n    return stringLiteral(value);\n  }\n\n  // numbers\n  if (typeof value === \"number\") {\n    let result;\n    if (Number.isFinite(value)) {\n      result = numericLiteral(Math.abs(value));\n    } else {\n      let numerator;\n      if (Number.isNaN(value)) {\n        // NaN\n        numerator = numericLiteral(0);\n      } else {\n        // Infinity / -Infinity\n        numerator = numericLiteral(1);\n      }\n\n      result = binaryExpression(\"/\", numerator, numericLiteral(0));\n    }\n\n    if (value < 0 || Object.is(value, -0)) {\n      result = unaryExpression(\"-\", result);\n    }\n\n    return result;\n  }\n\n  // bigints\n  if (typeof value === \"bigint\") {\n    return bigIntLiteral(value.toString());\n  }\n\n  // regexes\n  if (isRegExp(value)) {\n    const pattern = value.source;\n    const flags = /\\/([a-z]*)$/.exec(value.toString())[1];\n    return regExpLiteral(pattern, flags);\n  }\n\n  // array\n  if (Array.isArray(value)) {\n    return arrayExpression(value.map(valueToNode));\n  }\n\n  // object\n  if (isPlainObject(value)) {\n    const props = [];\n    for (const key of Object.keys(value)) {\n      let nodeKey,\n        computed = false;\n      if (isValidIdentifier(key)) {\n        if (key === \"__proto__\") {\n          computed = true;\n          nodeKey = stringLiteral(key);\n        } else {\n          nodeKey = identifier(key);\n        }\n      } else {\n        nodeKey = stringLiteral(key);\n      }\n      props.push(\n        objectProperty(\n          nodeKey,\n          valueToNode(\n            // @ts-expect-error key must present in value\n            value[key],\n          ),\n          computed,\n        ),\n      );\n    }\n    return objectExpression(props);\n  }\n\n  throw new Error(\"don't know how to turn this value into a node\");\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAawC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGzBC,WAAW;AAmB1B,MAAMC,cAA0C,GAAGC,QAAQ,CAACC,IAAI,CAACC,IAAI,CACnEC,MAAM,CAACC,SAAS,CAACC,QACnB,CAAC;AAED,SAASC,QAAQA,CAACC,KAAc,EAAmB;EACjD,OAAOR,cAAc,CAACQ,KAAK,CAAC,KAAK,iBAAiB;AACpD;AAEA,SAASC,aAAaA,CAACD,KAAc,EAAmB;EACtD,IACE,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,KAAK,IAAI,IACdJ,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACJ,IAAI,CAACM,KAAK,CAAC,KAAK,iBAAiB,EAC3D;IACA,OAAO,KAAK;EACd;EACA,MAAME,KAAK,GAAGN,MAAM,CAACO,cAAc,CAACH,KAAK,CAAC;EAI1C,OAAOE,KAAK,KAAK,IAAI,IAAIN,MAAM,CAACO,cAAc,CAACD,KAAK,CAAC,KAAK,IAAI;AAChE;AAEA,SAASX,WAAWA,CAACS,KAAc,EAAgB;EAEjD,IAAIA,KAAK,KAAKI,SAAS,EAAE;IACvB,OAAO,IAAAC,iBAAU,EAAC,WAAW,CAAC;EAChC;EAGA,IAAIL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,EAAE;IACrC,OAAO,IAAAM,qBAAc,EAACN,KAAK,CAAC;EAC9B;EAGA,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAAO,kBAAW,EAAC,CAAC;EACtB;EAGA,IAAI,OAAOP,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,IAAAQ,oBAAa,EAACR,KAAK,CAAC;EAC7B;EAGA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIS,MAAM;IACV,IAAIC,MAAM,CAACC,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC1BS,MAAM,GAAG,IAAAG,qBAAc,EAACC,IAAI,CAACC,GAAG,CAACd,KAAK,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAIe,SAAS;MACb,IAAIL,MAAM,CAACM,KAAK,CAAChB,KAAK,CAAC,EAAE;QAEvBe,SAAS,GAAG,IAAAH,qBAAc,EAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QAELG,SAAS,GAAG,IAAAH,qBAAc,EAAC,CAAC,CAAC;MAC/B;MAEAH,MAAM,GAAG,IAAAQ,uBAAgB,EAAC,GAAG,EAAEF,SAAS,EAAE,IAAAH,qBAAc,EAAC,CAAC,CAAC,CAAC;IAC9D;IAEA,IAAIZ,KAAK,GAAG,CAAC,IAAIJ,MAAM,CAACsB,EAAE,CAAClB,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;MACrCS,MAAM,GAAG,IAAAU,sBAAe,EAAC,GAAG,EAAEV,MAAM,CAAC;IACvC;IAEA,OAAOA,MAAM;EACf;EAGA,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,IAAAoB,oBAAa,EAACpB,KAAK,CAACF,QAAQ,CAAC,CAAC,CAAC;EACxC;EAGA,IAAIC,QAAQ,CAACC,KAAK,CAAC,EAAE;IACnB,MAAMqB,OAAO,GAAGrB,KAAK,CAACsB,MAAM;IAC5B,MAAMC,KAAK,GAAG,aAAa,CAACC,IAAI,CAACxB,KAAK,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,OAAO,IAAA2B,oBAAa,EAACJ,OAAO,EAAEE,KAAK,CAAC;EACtC;EAGA,IAAIG,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;IACxB,OAAO,IAAA4B,sBAAe,EAAC5B,KAAK,CAAC6B,GAAG,CAACtC,WAAW,CAAC,CAAC;EAChD;EAGA,IAAIU,aAAa,CAACD,KAAK,CAAC,EAAE;IACxB,MAAM8B,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMC,GAAG,IAAInC,MAAM,CAACoC,IAAI,CAAChC,KAAK,CAAC,EAAE;MACpC,IAAIiC,OAAO;QACTC,QAAQ,GAAG,KAAK;MAClB,IAAI,IAAAC,0BAAiB,EAACJ,GAAG,CAAC,EAAE;QAC1B,IAAIA,GAAG,KAAK,WAAW,EAAE;UACvBG,QAAQ,GAAG,IAAI;UACfD,OAAO,GAAG,IAAAzB,oBAAa,EAACuB,GAAG,CAAC;QAC9B,CAAC,MAAM;UACLE,OAAO,GAAG,IAAA5B,iBAAU,EAAC0B,GAAG,CAAC;QAC3B;MACF,CAAC,MAAM;QACLE,OAAO,GAAG,IAAAzB,oBAAa,EAACuB,GAAG,CAAC;MAC9B;MACAD,KAAK,CAACM,IAAI,CACR,IAAAC,qBAAc,EACZJ,OAAO,EACP1C,WAAW,CAETS,KAAK,CAAC+B,GAAG,CACX,CAAC,EACDG,QACF,CACF,CAAC;IACH;IACA,OAAO,IAAAI,uBAAgB,EAACR,KAAK,CAAC;EAChC;EAEA,MAAM,IAAIS,KAAK,CAAC,+CAA+C,CAAC;AAClE", "ignoreList": []}