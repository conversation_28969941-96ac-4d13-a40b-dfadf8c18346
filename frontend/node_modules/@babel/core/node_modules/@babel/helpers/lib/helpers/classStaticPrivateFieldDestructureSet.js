"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classStaticPrivateFieldDestructureSet;
var _classApplyDescriptorDestructureSet = require("classApplyDescriptorDestructureSet");
var _assertClassBrand = require("assertClassBrand");
var _classCheckPrivateStaticFieldDescriptor = require("classCheckPrivateStaticFieldDescriptor");
function _classStaticPrivateFieldDestructureSet(receiver, classConstructor, descriptor) {
  _assertClassBrand(classConstructor, receiver);
  _classCheckPrivateStaticFieldDescriptor(descriptor, "set");
  return _classApplyDescriptorDestructureSet(receiver, descriptor);
}

//# sourceMappingURL=classStaticPrivateFieldDestructureSet.js.map
