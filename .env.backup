# Solana Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_NODE_RPC_ENDPOINT=https://api.devnet.solana.com
SOLANA_NODE_WSS_ENDPOINT=wss://api.devnet.solana.com
SOLANA_PRIVATE_KEY=2ZJb3T6TZfTSbgnYrjH6AfJjSSFLoRHBAsRGwoSrsuJJGxPZ2x1QvYFGcbY1XEXxPZjdYHXBJB1J9CTvutVpnGqS
SOLANA_NETWORK=devnet

# Helius RPC (Enhanced Solana RPC)
HELIUS_API_KEY=
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=

# Jupiter API
JUPITER_API_URL=https://quote-api.jup.ag/v6
JUPITER_API_KEY=

# Birdeye API
BIRDEYE_API_KEY=08e6f6596a6a4396be34afb62d81380f
BIRDEYE_API_URL=https://public-api.birdeye.so

# DexScreener API
DEXSCREENER_API_URL=https://api.dexscreener.com/latest

# Trading Configuration
STARTING_CAPITAL=0.1
MAX_POSITION_SIZE=0.05
MAX_DAILY_LOSS=0.01
MAX_CONCURRENT_POSITIONS=3
DEFAULT_SLIPPAGE=0.5

# Risk Management
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=20
MAX_HOLD_TIME_HOURS=4
RISK_PER_TRADE_PERCENTAGE=5

# Database
DATABASE_URL=./data/trading.db
DATABASE_TYPE=sqlite

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/trading.log

# Dashboard
DASHBOARD_PORT=3000
API_PORT=3001

# WebSocket
WS_PORT=3002

# Paper Trading Mode (IMPORTANT: Set to false for live trading)
PAPER_TRADING=false
PAPER_STARTING_BALANCE=0.1
REAL_TRADING_MODE=true

# Frontend Trading Configuration
FRONTEND_TRADING=true
ENABLE_CHARTS=true
ENABLE_OPPORTUNITIES=true
SUPERALGOS_PORT=34248

# Monitoring
ENABLE_ALERTS=true
ALERT_WEBHOOK_URL=

# Development
NODE_ENV=development
DEBUG=true
